using System.Diagnostics.CodeAnalysis;
using Azure.Storage.Blobs;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using MST.Common.AWS.Extensions;
using MST.Common.Azure.Extensions;
using NBA.NextGen.CMSPlatform.VODService.Domain.Common;
using Vod.Common.Extensions;

namespace Vod.TranscribeCron.Extensions;

[ExcludeFromCodeCoverage]
public static class ApplicationExtensions
{
    public static IServiceCollection AddApplicationDependencies(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddSharedApplicationDependencies(configuration);
        services.AddSingleton<TranscribeCronJob>();

        var normalFlowBlobConnectionString = configuration.GetValue<string>("blob_connection_string");
        var normalFlowCopyContainerName = configuration.GetSection("NormalFlowSettings").GetValue<string>("CopyContainerName");
        var directFlowCopyContainerName = configuration.GetSection("FileTransformationSettings").GetValue<string>("CopyContainerName");
        var live2vodCopyContainerName = configuration.GetSection("LiveToVodSettings").GetValue<string>("CopyContainerName");
        var vcmsStorageCopyContainerName = configuration.GetSection("VcmsStorageSettings").GetValue<string>("CopyContainerName");

        var blobClient = new BlobServiceClient(normalFlowBlobConnectionString);
        services.AddSingleton(blobClient);

        services.RegisterBlobDataContainer(normalFlowCopyContainerName);
        services.RegisterBlobDataContainer(directFlowCopyContainerName);
        services.RegisterBlobDataContainer(live2vodCopyContainerName);
        services.RegisterBlobDataContainer(vcmsStorageCopyContainerName);

        return services;
    }

    public static IServiceCollection AddApplicationFactories(this IServiceCollection services)
    {
        return services;
    }

    public static IServiceCollection AddApplicationHandlers(this IServiceCollection services)
    {
        return services;
    }

    public static IServiceCollection AddApplicationConfiguration(this IServiceCollection services, IConfiguration configuration)
    {
        return services;
    }

    public static IServiceCollection AddApplicationRepositories(this IServiceCollection services)
    {
        return services;
    }
}