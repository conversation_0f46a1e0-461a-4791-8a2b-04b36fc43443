using Amazon.MediaConvert.Model;
using Microsoft.Azure.Cosmos;
using Microsoft.Extensions.Logging;
using MST.Common.Data;
using NBA.NextGen.CMSPlatform.VODService.Application.Dispatcher;
using NBA.NextGen.CMSPlatform.VODService.Application.Dispatcher.ContentHandler;
using NBA.NextGen.CMSPlatform.VODService.Application.Services;
using NBA.NextGen.CMSPlatform.VODService.Domain.Common;
using NBA.NextGen.CMSPlatform.VODService.Domain.Common.Enums;
using Newtonsoft.Json.Linq;

namespace Vod.TranscribeCron;

public class TranscribeCronJob
{
    private readonly IQueryableRepository<TranscribeJob> _queryableRepository;
    private readonly IAWSTranscribeService _awsTranscribeService;
    private readonly NormalFlowDispatcher _normalFlowDispatcher;
    private readonly IVodAssetRegistryService _vodAssetRegistryService;
    private readonly ILogger<TranscribeJob> _logger;
    private readonly IAWSTranscriptionOrchestrator _awsTranscriptionOrchestrator;

    public TranscribeCronJob(
        IQueryableRepositoryFactory queryableRepositoryFactory,
        IAWSTranscriptionOrchestrator awsTranscriptionOrchestrator,
        IAWSTranscribeService awsTranscribeService,
        NormalFlowDispatcher normalFlowDispatcher,
        IVodAssetRegistryService vodAssetRegistryService,
        ILogger<TranscribeJob> logger)
    {
        _queryableRepository = queryableRepositoryFactory.Resolve<TranscribeJob>();
        _awsTranscribeService = awsTranscribeService;
        _normalFlowDispatcher = normalFlowDispatcher;
        _vodAssetRegistryService = vodAssetRegistryService;
        _awsTranscriptionOrchestrator = awsTranscriptionOrchestrator;
        _logger = logger;
    }

    public async Task RunAsync()
    {
        _logger.LogInformation("Starting Transcribe job processor at {Time}", DateTime.UtcNow);

        IEnumerable<TranscribeJob> jobs;
        try
        {
            jobs = await _queryableRepository.GetItemsAsync(_ => true, 0, -1) ?? Enumerable.Empty<TranscribeJob>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while fetching Transcribe jobs.");
            return;
        }

        if (jobs == null || !jobs.Any())
        {
            _logger.LogInformation("No Transcribe jobs found to process.");
            return;
        }

        _logger.LogInformation("Found {Count} Transcribe job(s) to process.", jobs.Count());

        foreach (var job in jobs)
        {
            var jobResponse = await _awsTranscribeService.GetTranscriptionJobByIdAsync(job.JobId);
            var jobStatus = jobResponse.TranscriptionJob.TranscriptionJobStatus;
            _logger.LogInformation("Status for job {JobId}: {jobStatus}", job.JobId, jobStatus);

            if (jobStatus != Amazon.TranscribeService.TranscriptionJobStatus.COMPLETED && jobStatus != Amazon.TranscribeService.TranscriptionJobStatus.FAILED)
            {
                continue;
            }

            if (jobStatus == Amazon.TranscribeService.TranscriptionJobStatus.COMPLETED)
            {

                var urls = jobResponse.TranscriptionJob.Subtitles.SubtitleFileUris;
                var vttUrl = urls.FirstOrDefault(s => s.ToLower().EndsWith("vtt"));
                var srtUrl = urls.FirstOrDefault(s => s.ToLower().EndsWith("srt"));

                var finalCaptions = await _awsTranscriptionOrchestrator.RearrangeCaptionsAsync(job.JobId, srtUrl, vttUrl);

                var finalvttUrl = finalCaptions.FirstOrDefault(s => s.ToLower().EndsWith("vtt"));
                var finalsrtUrl = finalCaptions.FirstOrDefault(s => s.ToLower().EndsWith("srt"));

                var handler = _normalFlowDispatcher.GetHandler(job.TriggerAsset);

                if (!string.IsNullOrWhiteSpace(job.ResourceId))
                {
                    handler.SetResourceIdentifier(job.ResourceId);
                }

                if (handler is ISupportAsyncCaptions capHandler)
                {
                    try
                    {

                        _logger.LogInformation(
                            $"Transcribe Job: {job.Id} - Handler: {capHandler.GetType().Name} Adding Captions to Payload!");
                        var json = System.Text.Encoding.UTF8.GetString(System.Convert.FromBase64String(job.EncodedContent));
                        var payload = JObject.Parse(json);
                        var result = await capHandler.LinkCaptionsToPayloadAndSubmitAsync(payload, finalvttUrl, finalsrtUrl);
                        await _awsTranscriptionOrchestrator.CleanUpAsync(job.Id);
                        continue;
                    }
                    catch (Exception e)
                    {
                        await _vodAssetRegistryService.AddAssetEventAsync(handler.ResourceIdentifier, AssetEventTypes.TranscribingFailed, DateTime.UtcNow, e.Message).ConfigureAwait(false);
                        await _awsTranscriptionOrchestrator.CleanUpAsync(job.JobId);
                        
                    }
                }
            }

            if (jobStatus == Amazon.TranscribeService.TranscriptionJobStatus.FAILED)
            {
                await _awsTranscriptionOrchestrator.CleanUpAsync(job.JobId);
            }
        }
    }
}