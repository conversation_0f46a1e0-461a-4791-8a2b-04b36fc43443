
services:
  vod-api-service:
    container_name: vod-api-service
    image: ${DOCKER_REGISTRY-}vod-api-service
    build:
      context: .
      dockerfile: Vod.Api/Dockerfile
    ports:
      - "5001:8080"
    environment:
      - ASPNETCORE_URLS=http://+:8080

  vod-ecms-processor:
    image: ${DOCKER_REGISTRY-}vod-ecms-processor
    build:
      context: .
      dockerfile: Vod.EcmsProcessor/Dockerfile

  vod-incomingasset-processor:
    image: ${DOCKER_REGISTRY-}vod-incomingasset-processor
    build:
      context: .
      dockerfile: Vod.IncomingAssetProcessor/Dockerfile

  vod-vcms-processor:
    image: ${DOCKER_REGISTRY-}vod-vcms-processor
    build:
      context: .
      dockerfile: Vod.VcmsProcessor/Dockerfile

  vod-videoindex-checker:
    image: ${DOCKER_REGISTRY-}vod-videoindex-checker
    build:
      context: .
      dockerfile: Vod.VideoIndexerState<PERSON>hecker/Dockerfile

  vod-delayedresource-checker:
    image: ${DOCKER_REGISTRY-}vod-delayedresource-checker
    build:
      context: .
      dockerfile: Vod.DelayedResourceMonitor/Dockerfile

  vod-mediaconvert-cron:
    image: ${DOCKER_REGISTRY-}vod-mediaconvert-cron
    build:
      context: .
      dockerfile: Vod.MediaConvertCron/Dockerfile

  vod-transcribe-cron:
    image: ${DOCKER_REGISTRY-}vod-transcribe-cron
    build:
      context: .
      dockerfile: Vod.TranscribeCron/Dockerfile

  vod-incomingassetcopy-processor:
      image: ${DOCKER_REGISTRY-}vod-incomingassetcopy-processor
      build:
        context: .
        dockerfile: Vod.IncomingAssetCopyProcessor/Dockerfile