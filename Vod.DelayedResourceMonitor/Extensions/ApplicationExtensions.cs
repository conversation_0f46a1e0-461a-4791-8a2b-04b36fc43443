using System.Diagnostics.CodeAnalysis;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using MST.Common.AWS.Extensions;
using MST.Common.Azure.Extensions;
using Vod.Common.Extensions;
using Vod.Common.Models;

namespace Vod.DelayedResourceMonitor.Extensions;

[ExcludeFromCodeCoverage]
public static class ApplicationExtensions
{
    public static IServiceCollection AddApplicationDependencies(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddSharedApplicationDependencies(configuration);

        var blobQueue = configuration.GetSection("AWSQueueNames")["IncomingBlob"] ?? throw new NullReferenceException();
        services.RegisterSQSSender<IncomingObjectEvent>(blobQueue, _ => typeof(IncomingObjectEvent).Name);
        services.AddSingleton<DelayedResourceJob>();


        return services;
    }

    public static IServiceCollection AddApplicationFactories(this IServiceCollection services)
    {
        return services;
    }

    public static IServiceCollection AddApplicationHandlers(this IServiceCollection services)
    {
        return services;
    }

    public static IServiceCollection AddApplicationConfiguration(this IServiceCollection services, IConfiguration configuration)
    {
        return services;
    }

    public static IServiceCollection AddApplicationRepositories(this IServiceCollection services)
    {
        return services;
    }
}