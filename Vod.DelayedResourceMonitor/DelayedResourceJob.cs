using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using MST.Common.Data;
using MST.Common.Messaging;
using NBA.NextGen.CMSPlatform.VODService.Application.Services;
using NBA.NextGen.CMSPlatform.VODService.Domain.Common.Enums;
using Vod.Common.Data;
using Vod.Common.Models;

namespace Vod.DelayedResourceMonitor;

public class DelayedResourceJob
{
    private readonly IObjectRepository<DelayedResource> _objectRepository;
    private readonly IQueryableRepository<DelayedResource> _queryableRepository;
    private readonly IMessageSender<IncomingObjectEvent> _messageSender;
    private readonly ILogger _logger;
    private readonly IDataStoreFactory _dataStoreFactory;
    private readonly IVodAssetRegistryService _assetRegistryService;
    private readonly int _thresholdInMinutes;

    public DelayedResourceJob(IConfiguration configuration, IObjectRepositoryFactory objectRepositoryFactory, IQueryableRepositoryFactory queryableRepositoryFactory, IMessageSenderFactory messageSenderFactory, IDataStoreFactory dataStoreFactory, IVodAssetRegistryService assetRegistryService, ILogger<DelayedResourceJob> logger)
    {
        _objectRepository = objectRepositoryFactory.Resolve<DelayedResource>();
        _queryableRepository = queryableRepositoryFactory.Resolve<DelayedResource>();
        _messageSender = messageSenderFactory.Resolve<IncomingObjectEvent>();
        _logger = logger;
        _dataStoreFactory = dataStoreFactory;
        _assetRegistryService = assetRegistryService;

        var threshold = configuration.GetSection("DelayedAssetMonitor")["AlertThresholdMinutes"];
        if (!int.TryParse(threshold, out _thresholdInMinutes))
        {
            _thresholdInMinutes = 30;
        }
    }

    public async Task RunAsync()
    {
        var items = await _queryableRepository.GetItemsAsync(_ => true, 0, -1);
        if (items is null)
        {
            return;
        }

        _logger.LogInformation("Delayed Assets Count: " + items.Count());
        foreach (var item in items)
        {
            if (item.ContainerName is null || item.MissingFile is null || item.AssetId is null)
            {
                await _objectRepository.DeleteItemAsync(item.Id);
                continue;
            }

            _logger.LogInformation("Checking for Delayed Asset: " + item.MissingFile);

            var dataStore = _dataStoreFactory.Resolve(item.ContainerName);

            if (dataStore is null)
            {
                continue;
            }

            var exists = await dataStore.ExistsAsync(item.MissingFile);

            if (exists)
            {
                var json = System.Text.Encoding.UTF8.GetString(System.Convert.FromBase64String(item.Payload ?? ""));
                var eventData = System.Text.Json.JsonSerializer.Deserialize<IncomingObjectEvent>(json);
                if (eventData is not null)
                {
                    _logger.LogInformation("Delayed Asset: " + item.MissingFile + " Found, resubmitting!");
                    await _messageSender.SendAsync(eventData);
                }
                await _assetRegistryService.AddAssetEventAsync(item.AssetId, AssetEventTypes.AssetFoundResubmitted, DateTime.UtcNow);
                await _objectRepository.DeleteItemAsync(item.Id);

                continue;
            }

            item.LastCheckedAtUtc = DateTime.UtcNow;

            if (item.LastCheckedAtUtc > item.CreatedAtUtc.AddHours(24))
            {
                var msg = "Delayed Asset: " + item.MissingFile + " Still Missing After 24 hours, cleaning up!";
                await _assetRegistryService.AddAssetEventAsync(item.AssetId, AssetEventTypes.IngestionFailedAssetMissingTimeout, DateTime.UtcNow, msg);
                _logger.LogInformation(msg);
                await _objectRepository.DeleteItemAsync(item.Id);
                continue;
            }

            if (!item.ThresholdAlertSent && item.LastCheckedAtUtc > item.CreatedAtUtc.AddMinutes(_thresholdInMinutes))
            {
                var msg = "Delayed Asset: " + item.MissingFile + " Still Missing After " + _thresholdInMinutes + " minutes!";
                await _assetRegistryService.AddAssetEventAsync(item.AssetId, AssetEventTypes.IngestionBlockedThresholdAssetMissing, DateTime.UtcNow, msg);
                item.ThresholdAlertSent = true;
            }

            _logger.LogInformation("Delayed Asset: " + item.MissingFile + " Still Missing!");
            await _objectRepository.UpdateItemAsync(item);
        }
    }
}
