using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Vod.Common.Extensions;
using Vod.DelayedResourceMonitor;
using Vod.DelayedResourceMonitor.Extensions;

var builder = Host.CreateApplicationBuilder(args);
builder.Configuration
        .SetBasePath(AppDomain.CurrentDomain.BaseDirectory)
        .AddJsonFile("local.settings.json", true, true)
        .AddEnvironmentVariables();

var envName = builder.Configuration.GetValue<string>("ENV") ?? "qa";
builder.Configuration
        .AddAmazonSecretsManager(Environment.GetEnvironmentVariable("AWS_REGION") ?? "us-east-1",
                Environment.GetEnvironmentVariable("ENV") is not null ? $"/vod-middle-tier/{Environment.GetEnvironmentVariable("ENV")}" : "/vod-middle-tier/dev")
        .AddJsonFile($"{envName}.settings.json", false, true)
        .AddSharedConfiguration();

builder.Services.AddApplicationConfiguration(builder.Configuration)
        .AddApplicationDependencies(builder.Configuration)
        .AddApplicationHandlers()
        .AddApplicationFactories()
        .AddApplicationRepositories();

var app = builder.Build();

var job = app.Services.GetService<DelayedResourceJob>() ?? throw new NullReferenceException(nameof(DelayedResourceJob));

await job.RunAsync();