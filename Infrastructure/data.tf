data "aws_caller_identity" "current" {}

data "aws_secretsmanager_secret_version" "secrets" {
  secret_id = aws_secretsmanager_secret.secret.id
}

data "aws_iam_policy_document" "vod_trust" {
  statement {
    effect = "Allow"
    actions = [
      "sts:AssumeRoleWithWebIdentity"
    ]

    principals {
      identifiers = [
        "arn:aws:iam::${data.aws_caller_identity.current.account_id}:oidc-provider/oidc.eks.${var.aws_region}.amazonaws.com/id/${var.eks_cluster_id}"
      ]
      type = "Federated"
    }

    condition {
      test     = "StringEquals"
      variable = "oidc.eks.${var.aws_region}.amazonaws.com/id/${var.eks_cluster_id}:aud"
      values = [
        "sts.amazonaws.com"
      ]
    }

    condition {
      test     = "StringLike"
      variable = "oidc.eks.${var.aws_region}.amazonaws.com/id/${var.eks_cluster_id}:sub"
      values = [
        "system:serviceaccount:vod:vod*"
      ]
    }
  }
}

data "aws_iam_policy_document" "vod_policy_document" {
  statement {
    effect = "Allow"
    actions = [
      "sqs:GetQueueUrl",
      "sqs:PurgeQueue",
      "sqs:ReceiveMessage",
      "sqs:SendMessage",
      "sqs:StartMessageMoveTask",
      "sqs:DeleteMessage",
      "sqs:GetQueueAttributes"
    ]
    resources = [
      "arn:aws:sqs:${var.aws_region}:${data.aws_caller_identity.current.account_id}:vcvodsv-${var.environment}*"
    ]
  }

  statement {
    effect = "Allow"
    actions = [
      "sns:ConfirmSubscription",
      "sns:Publish",
      "sns:Subscribe",
      "sns:Unsubscribe"
    ]
    resources = [
      "arn:aws:sns:${var.aws_region}:${data.aws_caller_identity.current.account_id}:vod-*"
    ]
  }

  statement {
    effect = "Allow"
    actions = [
      "s3:PutObject",
      "s3:DeleteObject",
      "s3:List*",
      "s3:Get*",
    ]
    resources = [
      aws_s3_bucket.nf_s3_bucket.arn,
      aws_s3_bucket.df_s3_bucket.arn,
      aws_s3_bucket.l2v_s3_bucket.arn,
      aws_s3_bucket.df_generated_adi_s3_bucket.arn,
      aws_s3_bucket.transcribe_s3_bucket.arn,
      "${aws_s3_bucket.nf_s3_bucket.arn}/*",
      "${aws_s3_bucket.df_s3_bucket.arn}/*",
      "${aws_s3_bucket.l2v_s3_bucket.arn}/*",
      "${aws_s3_bucket.df_generated_adi_s3_bucket.arn}/*",
      "${aws_s3_bucket.transcribe_s3_bucket.arn}/*"
    ]
  }

  statement {
    effect  = "Allow"
    actions = ["ssm:*"]
    resources = [
      "arn:aws:ssm:${var.aws_region}:${data.aws_caller_identity.current.account_id}:parameter/mst/*"
    ]
  }

  statement {
    effect  = "Allow"
    actions = ["secretsmanager:*"]
    resources = [
      "arn:aws:secretsmanager:${var.aws_region}:${data.aws_caller_identity.current.account_id}:secret:/vod-middle-tier/${var.environment}*"
    ]
  }

  statement {
    effect  = "Allow"
    actions = ["iam:PassRole"]
    resources = [
      "arn:aws:iam::${data.aws_caller_identity.current.account_id}:role/${var.media_convert_role_name}"
    ]
  }

  statement {
    effect = "Allow"
    actions = [
      "mediaconvert:GetJob",
      "mediaconvert:CreateJob"
    ]
    resources = [
      "arn:aws:mediaconvert:${var.aws_region}:${data.aws_caller_identity.current.account_id}:queues/Default"
    ]
  }

  statement {
    effect = "Allow"
    actions = [
      "mediaconvert:GetJob"
    ]
    resources = [
      "arn:aws:mediaconvert:${var.aws_region}:${data.aws_caller_identity.current.account_id}:jobs/*"
    ]
  }

  statement {
    effect = "Allow"
    actions = [
      "transcribe:StartTranscriptionJob",
      "transcribe:GetTranscriptionJob",
      "transcribe:ListTranscriptionJobs"
    ]
    resources = [
      "arn:aws:transcribe:${var.aws_region}:${data.aws_caller_identity.current.account_id}:transcription-job/*"
    ]
  }
}

data "aws_iam_policy_document" "vod_shared_secrets_policy_document" {
  statement {
    effect = "Allow"
    actions = [
      "secretsmanager:List*",
      "secretsmanager:Describe*",
      "secretsmanager:Get*"
    ]
    resources = [
      "arn:aws:secretsmanager:${var.aws_region}:${data.aws_caller_identity.current.account_id}:secret:/shared-secrets/*"
    ]
  }
}

data "aws_iam_policy_document" "eventbridge_sqs_publish_policy" {
  version = "2012-10-17"
  statement {
    effect  = "Allow"
    actions = ["sqs:SendMessage"]
    principals {
      type        = "Service"
      identifiers = ["events.amazonaws.com"]
    }
    resources = [data.aws_sqs_queue.incoming_asset_queue.arn]
  }
}

data "aws_sqs_queue" "incoming_asset_queue" {
  name       = "vcvodsv-${var.environment}-incomingblob-cloudevents-queue.fifo"
  depends_on = [aws_sqs_queue.shared_queue]
}