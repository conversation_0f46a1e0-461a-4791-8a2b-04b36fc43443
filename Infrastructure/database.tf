module "docdb" {
  source               = "./modules/docdb"
  environment          = var.environment
  vpc_id               = var.vpc_id
  subnet_ids           = var.subnet_ids
  security_group_ids   = var.security_group_ids
  docdb_instance_class = var.docdb_instance_class
  docdb_instance_count = var.docdb_instance_count
  master_username      = var.master_username
  master_password      = jsondecode(data.aws_secretsmanager_secret_version.secrets.secret_string)["docdb_password"]
  encrypt_db           = var.encrypt_db
  common_tags          = local.tags
}

resource "random_password" "docdb_password" {
  length  = 16
  special = true
}