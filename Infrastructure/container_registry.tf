resource "aws_ecr_repository" "vod_container_repositories" {
  for_each = { for k, v in toset(var.ecr_names) : k => v if var.environment == "dev" }
  name     = each.key
  tags     = merge(local.tags, { Name = each.key })

  encryption_configuration {
    encryption_type = "KMS"
  }

  image_scanning_configuration {
    scan_on_push = true
  }
}

resource "aws_ecr_lifecycle_policy" "vod_container_repository_policies" {
  for_each   = { for k, v in toset(var.ecr_names) : k => v if var.environment == "dev" }
  repository = each.key

  policy = <<EOF
{
    "rules": [
        {
            "rulePriority": 1,
            "description": "Keep last 30 images",
            "selection": {
                "tagStatus": "tagged",
                "tagPrefixList": ["dev","qa","prod"],
                "countType": "imageCountMoreThan",
                "countNumber": 30
            },
            "action": {
                "type": "expire"
            }
        }
    ]
}
EOF
}