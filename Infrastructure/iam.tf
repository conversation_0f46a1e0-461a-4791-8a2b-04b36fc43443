resource "aws_iam_policy" "vod_policy" {
  name   = "mst-${var.environment}-vodmiddletier-policy001"
  policy = data.aws_iam_policy_document.vod_policy_document.json
}

resource "aws_iam_policy" "vod_shared_secrets_policy" {
  name   = "mst-${var.environment}-vodmiddletier-shared-secrets-policy001"
  policy = data.aws_iam_policy_document.vod_shared_secrets_policy_document.json
}

resource "aws_iam_role" "vod_irsa_role" {
  name               = "mst-${var.environment}-vodmiddletier-role001"
  assume_role_policy = data.aws_iam_policy_document.vod_trust.json
}

resource "aws_iam_role_policy_attachments_exclusive" "vod_irsa_policies" {
  role_name = aws_iam_role.vod_irsa_role.name
  policy_arns = [
    aws_iam_policy.vod_policy.arn,
    aws_iam_policy.vod_shared_secrets_policy.arn
  ]
}