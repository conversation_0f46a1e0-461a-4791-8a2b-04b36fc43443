variable "environment" {
  description = "Deployment environment (dev, uat, prod)"
  type        = string
}

variable "aws_region" {
  description = "AWS region to deploy resources"
  type        = string
  default     = "us-east-1"
}

variable "vpc_id" {
  description = "ID of the existing VPC"
  type        = string
}

variable "subnet_ids" {
  description = "List of subnet IDs within the VPC for the DocumentDB cluster"
  type        = list(string)
}

variable "security_group_ids" {
  description = "List of security group IDs to associate with the DocumentDB cluster"
  type        = list(string)
}

variable "docdb_instance_class" {
  description = "Instance class for DocumentDB instances"
  type        = string
}

variable "docdb_instance_count" {
  description = "Number of instances in the DocumentDB cluster"
  type        = number
}

variable "master_username" {
  description = "Username for the master DB user"
  type        = string
}

variable "tags" {
  description = "A map of common tags"
  type        = map(string)
  default = {
    Name               = ""
    Environment        = ""
    League             = "NBA"
    Department         = "NextGen"
    SubDepartment      = "DTC"
    Application        = "OTT"
    DistroEmail        = "<EMAIL>"
    SLA                = "High"
    DataClassification = "Internal"
  }
}

variable "aws_backup_region" {
  description = "AWS region to use for backups or replicas"
  type        = string
  default     = "us-east-2"
}

variable "secrets" {
  type    = map(string)
  default = {}
}

variable "ecr_names" {
  description = "List of all repository names"
  type        = list(string)
  default = [
    "mst/vod-api-service",
    "mst/vod-ecms-processor",
    "mst/vod-incomingasset-processor",
    "mst/vod-vcms-processor",
    "mst/vod-mediaconvert-cron",
    "mst/vod-transcribe-cron",
    "mst/vod-videoindexer-statechecker",
    "mst/vod-delayedresource-checker",
    "mst/vod-incomingassetcopy-processor"
  ]
}

variable "eks_cluster_id" {
  type    = string
  default = ""
}

variable "media_convert_role_name" {
  type    = string
  default = "MediaConvert"
}

variable "encrypt_db" {
  type    = bool
  default = false
}

variable "mediakind_arn" {
  description = "MediaKind role ARN"
  type        = string
}

variable "mpd_hub_user_arn" {
  description = "Mediakind MPD Hub ARN"
  type        = string
}

variable "mk_power_user_role_arn" {
  description = "MediaKind Power User ARN"
  type        = string
}