resource "aws_docdb_subnet_group" "docdb_subnet_group" {
  name       = "${var.environment}-vod-docdb-subnet-group"
  subnet_ids = var.subnet_ids
  tags       = merge(var.common_tags, { Name = "${var.environment}-vod-docdb-subnet-group" })
}

resource "aws_docdb_cluster_parameter_group" "docdb_cluster_param_group" {
  family      = "docdb5.0"
  name        = "${var.environment}-vod-docdb-param-group"
  description = "${var.environment} DocDB cluster parameter group"
  tags        = merge(var.common_tags, { Name = "${var.environment}-vod-docdb-param-group" })
}

resource "aws_docdb_cluster" "docdb_cluster" {
  cluster_identifier              = "${var.environment}-vod-docdb-cluster"
  engine                          = "docdb"
  master_username                 = var.master_username
  master_password                 = var.master_password
  db_subnet_group_name            = aws_docdb_subnet_group.docdb_subnet_group.name
  vpc_security_group_ids          = var.security_group_ids
  db_cluster_parameter_group_name = aws_docdb_cluster_parameter_group.docdb_cluster_param_group.name
  storage_encrypted               = var.encrypt_db
  tags                            = merge(var.common_tags, { Name = "${var.environment}-vod-docdb-cluster" })
}

resource "aws_docdb_cluster_instance" "docdb_instances" {
  count                       = var.docdb_instance_count
  identifier                  = "${var.environment}-vod-docdb-instance-${count.index}"
  cluster_identifier          = aws_docdb_cluster.docdb_cluster.id
  instance_class              = var.docdb_instance_class
  enable_performance_insights = true
  tags                        = merge(var.common_tags, { Name = "${var.environment}-vod-docdb-instance-${count.index}" })
}