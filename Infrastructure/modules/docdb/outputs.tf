output "docdb_cluster_endpoint" {
  description = "The endpoint of the DocumentDB cluster"
  value       = aws_docdb_cluster.docdb_cluster.endpoint
}

output "docdb_cluster_reader_endpoint" {
  description = "The reader endpoint of the DocumentDB cluster"
  value       = aws_docdb_cluster.docdb_cluster.reader_endpoint
}

output "docdb_cluster_port" {
  description = "The port on which the DocumentDB cluster accepts connections"
  value       = aws_docdb_cluster.docdb_cluster.port
}

output "docdb_cluster_id" {
  description = "The ID of the DocumentDB cluster"
  value       = aws_docdb_cluster.docdb_cluster.id
}

output "docdb_cluster_username" {
  description = "The username of the DocumentDB cluster"
  value       = aws_docdb_cluster.docdb_cluster.master_username
}