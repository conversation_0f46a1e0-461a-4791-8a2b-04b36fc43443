variable "environment" {
  description = "Deployment environment (dev, uat, prod)"
  type        = string
}

variable "vpc_id" {
  description = "ID of the existing VPC"
  type        = string
}

variable "subnet_ids" {
  description = "List of subnet IDs within the VPC for the DocumentDB cluster"
  type        = list(string)
}

variable "security_group_ids" {
  description = "List of security group IDs to associate with the DocumentDB cluster"
  type        = list(string)
}

variable "docdb_instance_class" {
  description = "Instance class for DocumentDB instances"
  type        = string
}

variable "docdb_instance_count" {
  description = "Number of instances in the DocumentDB cluster"
  type        = number
}

variable "master_username" {
  description = "Username for the master DB user"
  type        = string
}

variable "master_password" {
  description = "Password for the master DB user"
  type        = string
  sensitive   = true
}

variable "common_tags" {
  description = "A list of tags to be applied to all resources"
  type        = map(string)
}

variable "encrypt_db" {
  description = "A bool value reflecting whether or not to enable encryption-at-rest. Forces replacement."
  type        = bool
  default     = false
}