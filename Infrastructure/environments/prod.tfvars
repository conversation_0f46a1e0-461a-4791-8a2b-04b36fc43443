environment            = "prod"
vpc_id                 = "vpc-005306254e1a3a793"
subnet_ids             = ["subnet-0039f5f2c49ecfca6", "subnet-027472476ffacc89b", "subnet-04ecce716a06abaf3"]
security_group_ids     = ["sg-0db65aa3e1fa7dd07"]
docdb_instance_class   = "db.t3.medium"
docdb_instance_count   = 2
master_username        = "docdbuser"
aws_region             = "us-west-2"
aws_backup_region      = "us-west-1"
stage_name             = "pd"
eks_cluster_id         = "49372F5059CB674CA978339C51DC7F04"
encrypt_db             = true
mediakind_arn          = "arn:aws:iam::121174169890:role/eks-irsa-s3-role"
mpd_hub_user_arn       = "arn:aws:iam::121174169890:user/mpduser"
mk_power_user_role_arn = "arn:aws:iam::121174169890:role/aws-reserved/sso.amazonaws.com/AWSReservedSSO_PowerUserAccess_afeb38f4b192d83e"