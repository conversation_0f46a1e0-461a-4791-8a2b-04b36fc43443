environment            = "dev"
vpc_id                 = "vpc-bb39b9c0"
subnet_ids             = ["subnet-d59ea988", "subnet-0810c0cf91bf31279"]
security_group_ids     = ["sg-041435d71499c606e"]
docdb_instance_class   = "db.t3.medium"
docdb_instance_count   = 2
master_username        = "docdbuser"
aws_region             = "us-east-1"
stage_name             = "dev"
eks_cluster_id         = "2E0FF5FE0ED2B667AD2CEB3C99646FF6"
encrypt_db             = false
mediakind_arn          = "arn:aws:iam::026090525130:role/eks-irsa-s3-role"
mpd_hub_user_arn       = "arn:aws:iam::026090525130:user/mpd-hub-user"
mk_power_user_role_arn = "arn:aws:iam::466206554144:role/aws-reserved/sso.amazonaws.com/AWSReservedSSO_PowerUserAccess_fceaaecb6871c737"