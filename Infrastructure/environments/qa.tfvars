environment            = "qa"
vpc_id                 = "vpc-bb39b9c0"
subnet_ids             = ["subnet-02234a12660cc3c1b", "subnet-0ec2149f09616f9aa"]
security_group_ids     = ["sg-041435d71499c606e"]
docdb_instance_class   = "db.t3.medium"
docdb_instance_count   = 2
master_username        = "docdbuser"
aws_region             = "us-east-1"
stage_name             = "qa"
eks_cluster_id         = "50CBC52C5E2E21464FE3320D03A33858"
encrypt_db             = true
mediakind_arn          = "arn:aws:iam::466206554144:role/eks-irsa-s3-role"
mpd_hub_user_arn       = "arn:aws:iam::466206554144:user/mpduser"
mk_power_user_role_arn = "arn:aws:iam::466206554144:role/aws-reserved/sso.amazonaws.com/AWSReservedSSO_PowerUserAccess_fceaaecb6871c737"