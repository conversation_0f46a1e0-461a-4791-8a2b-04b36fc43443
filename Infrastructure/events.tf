resource "aws_cloudwatch_event_target" "incoming_asset_target" {
  arn       = data.aws_sqs_queue.incoming_asset_queue.arn
  rule      = aws_cloudwatch_event_rule.incoming_asset_rule.name
  target_id = "vcvodsv-${var.environment}-incomingasset-s3-notification-target"
  sqs_target {
    message_group_id = "vcvodsv-${var.environment}-incomingasset-group"
  }
}

resource "aws_cloudwatch_event_rule" "incoming_asset_rule" {
  name        = "vcvodsv-${var.environment}-incomingasset-s3-notification-processor"
  description = "The EventBridge rule to send notification of incoming assets to the incoming assets SQS queue for the ${var.environment} environment."
  tags        = merge(local.tags, { Name = "vcvodsv-${var.environment}-incomingasset-s3-notification-processor" })
  event_pattern = jsonencode({
    source      = ["aws.s3"]
    detail-type = ["Object Created"]
    detail = {
      bucket = {
        name = [aws_s3_bucket.nf_s3_bucket.id, aws_s3_bucket.df_s3_bucket.id, aws_s3_bucket.l2v_s3_bucket.id]
      }
      object = {
        size = [
          {
            numeric = [">", 0]
          }
        ]
      }
    }
  })
  depends_on = [aws_s3_bucket.df_s3_bucket, aws_s3_bucket.nf_s3_bucket, aws_s3_bucket.l2v_s3_bucket]
}