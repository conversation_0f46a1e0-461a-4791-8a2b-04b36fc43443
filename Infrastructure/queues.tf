resource "aws_sqs_queue" "dlq" {
  for_each                    = toset(local.queue_names)
  name                        = replace(each.key, ".fifo", "-dlq.fifo")
  max_message_size            = 262144
  visibility_timeout_seconds  = 180
  fifo_queue                  = true
  content_based_deduplication = true

  tags = merge(local.tags, {
    Name = replace(each.key, ".fifo", "-dlq.fifo")
  })
}
resource "aws_sqs_queue" "shared_queue" {
  for_each                    = toset(local.queue_names)
  name                        = each.key
  max_message_size            = 262144
  visibility_timeout_seconds  = 180
  fifo_queue                  = true
  content_based_deduplication = true

  redrive_policy = jsonencode({
    deadLetterTargetArn = aws_sqs_queue.dlq[each.key].arn
    maxReceiveCount     = 10
  })

  tags = local.tags
}

resource "aws_sqs_queue_policy" "incoming_asset_queue_policy" {
  policy    = data.aws_iam_policy_document.eventbridge_sqs_publish_policy.json
  queue_url = data.aws_sqs_queue.incoming_asset_queue.id
}