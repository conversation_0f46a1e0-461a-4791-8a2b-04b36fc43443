resource "random_string" "dummy_secret_value" {
  length = 16
}

resource "aws_secretsmanager_secret" "secret" {
  description = "Secrets for VOD Middletier"
  name        = "/vod-middle-tier/${var.environment}"
  replica {
    region = var.aws_backup_region
  }
  tags = local.tags
}

resource "aws_secretsmanager_secret_version" "secret_version" {
  secret_id     = aws_secretsmanager_secret.secret.id
  secret_string = jsonencode(local.secret_contents)
  lifecycle {
    ignore_changes = all
  }
}