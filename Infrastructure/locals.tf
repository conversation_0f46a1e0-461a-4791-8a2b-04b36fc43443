locals {
  queue_names = [
    "vcvodsv-${var.environment}-incomingblob-cloudevents-queue.fifo",
    "vcvodsv-${var.environment}-ecms-submit-queue.fifo",
    "vcvodsv-${var.environment}-vcms-submit-queue.fifo",
    "vcvodsv-${var.environment}-ecms-update-queue.fifo",
    "vcvodsv-${var.environment}-asset-copy-queue.fifo",
  ]

  tags = merge(var.tags, { Environment = var.environment })
  secret_contents = merge(
    var.secrets,
    {
      gms_password                  = random_string.dummy_secret_value.result
      blob_connection_string        = random_string.dummy_secret_value.result
      wordpress_api_key             = random_string.dummy_secret_value.result
      slack_error_webhook_url       = random_string.dummy_secret_value.result
      slack_webhook_url             = random_string.dummy_secret_value.result
      vcms_ingest_key               = random_string.dummy_secret_value.result
      cosmos_connection_string      = random_string.dummy_secret_value.result
      docdb_connection_string       = random_string.dummy_secret_value.result
      docdb_password                = random_string.dummy_secret_value.result
      event_grid_key                = random_string.dummy_secret_value.result
      service_bus_connection_string = random_string.dummy_secret_value.result
      "freewheel_secrets.json"      = random_string.dummy_secret_value.result
      "gam_test.json"               = random_string.dummy_secret_value.result
      "gam_prod.json"               = random_string.dummy_secret_value.result
    }
  )
}