using System.Diagnostics.CodeAnalysis;
using Vod.Common.Extensions;
using MST.Common.Azure.Extensions;
using MST.Common.Azure.Workers;
using Vod.VcmsProcessor;
using MST.Common.AWS.Extensions;

namespace Vod.IncomingAssetProcessor.Extensions;

[ExcludeFromCodeCoverage]
public static class ApplicationExtensions
{
    public static IServiceCollection AddApplicationDependencies(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddSharedApplicationDependencies(configuration);

        return services;
    }

    public static IServiceCollection AddApplicationFactories(this IServiceCollection services)
    {
        return services;
    }

    public static IServiceCollection AddApplicationHandlers(this IServiceCollection services, IConfiguration configuration)
    {
        var queueName = configuration.GetSection("AWSQueueNames")["SubmitToVcms"] ?? throw new NullReferenceException();
        services.RegisterSQSConsumer<SubmitToVcmsMessageHandler>(queueName);
        
        return services;
    }

    public static IServiceCollection AddApplicationConfiguration(this IServiceCollection services, IConfiguration configuration)
    {
        return services;
    }

    public static IServiceCollection AddApplicationRepositories(this IServiceCollection services)
    {
        return services;
    }
}