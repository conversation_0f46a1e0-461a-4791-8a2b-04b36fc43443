using MST.Common.Messaging;
using MST.Common.Utility;
using NBA.NextGen.CMSPlatform.VODService.Application.Services;
using NBA.NextGen.CMSPlatform.VODService.Domain.Common;
using Newtonsoft.Json;
using Vod.Common.Services;

namespace Vod.VcmsProcessor;

public class SubmitToVcmsMessageHandler : IMessageHandler
{
    private readonly IVcmsService _vcmsService;
    private readonly ILogger _logger;
    private readonly IMessageSender<AdiMessageWrapper> _vcmsSubmitSender;
    private readonly IVodAssetRegistryService _assetRegistryService;

    public SubmitToVcmsMessageHandler(ILogger<SubmitToVcmsMessageHandler> logger, IVcmsService vcmsService, IMessageSenderFactory senderFactory, IVodAssetRegistryService assetRegistryService)
    {
        _logger = logger;
        _vcmsService = vcmsService;
        _vcmsSubmitSender = senderFactory.Resolve<AdiMessageWrapper>();
        _assetRegistryService = assetRegistryService;
    }

    public async Task ProcessMessage(ReceivedMessage message)
    {
        _logger.LogInformation("Received AdiMessageWrapper");

        var request = JsonConvert.DeserializeObject<AdiMessageWrapper>(message.Content ?? "");

        if (request is null)
        {
            throw new NullReferenceException(
                "Could Not Deserialize incoming message of type: AdiMessageWrapper"
            );
        }

        _logger.LogInformation(
            $"SubmitToVcmsMessageHandler received processing ADI AssetId: {request.Adi.GetAdiAssetId()}!"
        );

        try
        {
            var success = await _vcmsService.SubmitToVcmsAsync(request.Adi);
            _logger.LogInformation($"Success: {success}");
            _logger.LogInformation($"SubmitToVcmsMessageHandler finished processing ADI AssetId: {request.Adi.GetAdiAssetId()}!"
        );
        }
        catch (Exception e)
        {
            // Resubmit up to 3 times.
            if (request.RetryCount < 2)
            {
                request.RetryCount++;
                await _vcmsSubmitSender.SendAsync(request);
                _logger.LogError(e, "Failed to Send ADI to VCMS, will resubmit to end of queue.");
            }
            else
            {
                var identifier = request.Adi.GetAdiAssetId();
                _logger.LogError(e, "Failed to Send ADI to VCMS, will dead-letter.");
                await _assetRegistryService.AddAssetEventAsync(identifier ?? "No Asset Id Found", NBA.NextGen.CMSPlatform.VODService.Domain.Common.Enums.AssetEventTypes.VcmsSubmitMessageDeadLettered, DateTime.UtcNow, e.Message);
                message.ShouldDeadLetter = true;
            }
        }
    }

    public Task ProcessError(Exception exception)
    {
        _logger.LogError(
            exception,
            "SubmitToVcmsMessageHandler Queue Error: {ExceptionMessage}",
            exception.Message
        );
        return Task.CompletedTask;
    }
}
