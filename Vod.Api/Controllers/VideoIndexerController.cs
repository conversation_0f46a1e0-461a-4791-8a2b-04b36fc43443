
using Amazon.MediaConvert;
using Microsoft.AspNetCore.Mvc;
using NBA.NextGen.CMSPlatform.VODService.Application.Services;

namespace Vod.Api.Controllers;

[Route("api/video-index")]
[ApiController]
public class VideoIndexerController 
{
    private readonly ILogger logger;
    private readonly IAzureVideoIndexerService videoIndexerService;
    private readonly IAWSMediaConvertService mediaConvertService;


    /// <summary>
    /// Initializes a new instance of the <see cref="VideoIndexerFunctions" /> class.
    /// </summary>
    public VideoIndexerController(
        ILogger<VideoIndexerController> logger,
        IAzureVideoIndexerService videoIndexerService,
        IAWSMediaConvertService mediaConvertService)
    {
        this.logger = logger;
        this.videoIndexerService = videoIndexerService;
        this.mediaConvertService = mediaConvertService;
    }


    [HttpGet("lookup/{videoId}")]
    public async Task<IActionResult> VideoIndexerLookupAsync(string videoId)
    {
        this.logger.LogInformation($"Received Video Indexer Lookup - Video ID: {videoId}");
        var externalId = await this.videoIndexerService.GetExternalIdAsync(videoId).ConfigureAwait(false);
        return new OkObjectResult(externalId);
    }

    [HttpGet("list")]
    public async Task<IActionResult> VideoIndexerListAsync()
    {
        this.logger.LogInformation($"Received Video Indexer List");
        var entries = await this.videoIndexerService.ListExistingVideosAsync().ConfigureAwait(false);
        return new OkObjectResult(entries);
    }

    [HttpGet("captions/{videoId}")]
    public async Task<IActionResult> VideoIndexerGetCaptionsAsync(string videoId)
    {
        this.logger.LogInformation($"Received Video Indexer Get Captions - Video ID: {videoId}");
        var captions = await this.videoIndexerService.GetVttCaptionsAsync(videoId).ConfigureAwait(false);
        return new OkObjectResult(captions);
    }

    [HttpPost("trigger")]
    public async Task<IActionResult> VideoIndexerTriggerAsync([FromBody] string inputAsset)
    {
        this.logger.LogInformation($"Received Video Indexer Trigger - Filename: {inputAsset}");
        await Task.CompletedTask;
        return new OkObjectResult("");
    }
}
