using Microsoft.AspNetCore.Mvc;
using NBA.NextGen.CMSPlatform.VODService.Application.UseCases.FreeWheel;
using NBA.NextGen.CMSPlatform.VODService.Domain.FreeWheel.Request;
using NBA.NextGen.CMSPlatform.VODService.Domain.Utilities;

namespace Vod.Api.Controllers;

[Route("api/freewheel")]
[ApiController]
public class FreewheelController : Controller
{
    private readonly StartVcmsIngestionCommandHandler handler;
    private readonly ILogger logger;

    public FreewheelController(
        StartVcmsIngestionCommandHandler handler,
        ILogger<FreewheelController> logger
    )
    {
        this.handler = handler;
        this.logger = logger;
    }

    [HttpPost("ingest-ad")]
    public async Task<IActionResult> IngestAd()
    {
        this.logger.LogInformation("FreeWheel Ad Ingestion Trigger");

        using StreamReader st = new StreamReader(this.Request.Body);
        var body = await st.ReadToEndAsync();

        Transcode? transcodeRequest = null;

        this.logger.LogInformation(body);
        transcodeRequest = XmlHelper.DeserializeXML<Transcode>(body);

        var command = new StartVcmsIngestionCommand();
        command.InternalId = $"FW|{Guid.NewGuid().ToString()}";
        command.TranscodeRequest = transcodeRequest;

        // process the query for the flow based on file path
        await handler.Handle(command, CancellationToken.None);

        this.logger.LogInformation($"FreeWheelHttpFunction Responded to incoming request.");

        return new OkResult();
    }
}
