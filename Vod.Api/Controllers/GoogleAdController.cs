using Microsoft.AspNetCore.Mvc;
using NBA.NextGen.CMSPlatform.VODService.Application.UseCases.Gam;

namespace Vod.Api.Controllers;

[Route("api/gam")]
[ApiController]
public class GoogleAdController: Controller
{
    private readonly PrepareGamToVcmsCommandHandler handler;
    private readonly GetCompanyListQueryHandler companyListQueryHandler;
    private readonly ILogger logger;

    public GoogleAdController(PrepareGamToVcmsCommandHandler handler, GetCompanyListQueryHandler companyListQueryHandler, ILogger<GoogleAdController> logger)
    {
        this.handler = handler;
        this.companyListQueryHandler = companyListQueryHandler;
        this.logger = logger;
    }

    [HttpPost("ingest-ad")]
    public async Task<IActionResult> IngestGamAd()
    {
        this.logger.LogInformation("GAM Ad Ingestion Trigger");

        using StreamReader st = new StreamReader(this.Request.Body);
        var body = await st.ReadToEndAsync();
        
        NBA.NextGen.CMSPlatform.VODService.Domain.Gam.IngestionRequest? request = null;

        this.logger.LogInformation(body);
        try
        {
            request = Newtonsoft.Json.JsonConvert.DeserializeObject<NBA.NextGen.CMSPlatform.VODService.Domain.Gam.IngestionRequest>(body);
        }
        catch (Exception e)
        {
            return new BadRequestObjectResult($"Failure Deserializing Input Model: {e.Message}");
        }

        if (request is null)
        {
            return new BadRequestObjectResult("Error: Invalid Request.");
        }

        if (request.CompanyId < 1)
        {
            return new BadRequestObjectResult("Error: Company Id is Invalid.");
        }

        if (string.IsNullOrWhiteSpace(request.Title))
        {
            return new BadRequestObjectResult("Error: Title is Invalid.");
        }

        if (string.IsNullOrWhiteSpace(request.Url))
        {
            return new BadRequestObjectResult("Error: Url is Invalid.");
        }

        var command = new PrepareGamToVcmsCommand()
        {
            Title = request.Title,
            AssetLocation = request.Url,
            CompanyId = request.CompanyId,
            ClickThroughDestination = request.ClickThroughUrl,
        };

        await handler.Handle(command, CancellationToken.None);

        return new OkResult();
    }

    [HttpPost("gam-list-companies")]
    public async Task<IActionResult> ListGamCompanies()
    {
        this.logger.LogInformation("Attempting to get the list of company details!");

        var request = new GetCompanyListQuery();

        var results = await this.companyListQueryHandler.Handle(request, CancellationToken.None);

        return results != null
            ? (ActionResult)new OkObjectResult(results)
            : new BadRequestObjectResult("Error: Could not Get list of Companies!");
    }
}