using Microsoft.AspNetCore.Mvc;
using MST.Common.Messaging;
using NBA.NextGen.CMSPlatform.VODService.Application.Dispatcher;
using NBA.NextGen.CMSPlatform.VODService.Application.Services;
using NBA.NextGen.CMSPlatform.VODService.Domain.Common;
using NBA.NextGen.CMSPlatform.VODService.Domain.Utilities;
using NBA.NextGen.CMSPlatform.VODService.Domain.Vcms;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Vod.Common.Models;
using Vod.Common.Services;

namespace Vod.Api.Controllers;

[Route("api/manual")]
[ApiController]
public class ManualIngestController : Controller
{
    private readonly IServiceProvider serviceProvider;

    public ManualIngestController(IServiceProvider serviceProvider)
    {
        this.serviceProvider = serviceProvider;
    }

    [HttpPost("incoming")]
    public async Task<IActionResult> SubmitToIncomingQueueAsync()
    {
        using StreamReader st = new StreamReader(this.Request.Body);
        var body = await st.ReadToEndAsync();

        var factory = serviceProvider.GetService<IMessageSenderFactory>();

        if (factory is null)
        {
            throw new NullReferenceException(nameof(factory));
        }

        var sender = factory.Resolve<IncomingBlobEvent>();
        var eventData = System.Text.Json.JsonSerializer.Deserialize<IncomingBlobEvent>(body);

        if (sender is null)
        {
            throw new NullReferenceException(nameof(sender));
        }
        await sender.SendAsync(eventData ?? new IncomingBlobEvent());

        return Ok();
    }

    [HttpPost("nf")]
    public async Task<IActionResult> SubmitToNormalFlowAsync()
    {
        using StreamReader st = new StreamReader(this.Request.Body);
        var body = await st.ReadToEndAsync();

        var dispatcher = serviceProvider.GetService<NormalFlowDispatcher>();

        if (dispatcher is null)
        {
            throw new NullReferenceException(nameof(dispatcher));
        }

        var resp = await dispatcher.SimpleProcessingAsync(body);

        return Ok(resp);
    }

    [HttpPost("df")]
    public async Task<IActionResult> SubmitToDirectFlowAsync()
    {
        using StreamReader st = new StreamReader(this.Request.Body);
        var body = await st.ReadToEndAsync();

        var dispatcher = serviceProvider.GetService<DirectFlowDispatcher>();

        if (dispatcher is null)
        {
            throw new NullReferenceException(nameof(dispatcher));
        }

        var resp = await dispatcher.SimpleProcessingAsync(body);

        return Ok(resp);
    }

    [HttpPost("l2v")]
    public async Task<IActionResult> SubmitToL2VFlowAsync()
    {
        using StreamReader st = new StreamReader(this.Request.Body);
        var body = await st.ReadToEndAsync();

        var dispatcher = serviceProvider.GetService<LiveToVodDispatcher>();

        if (dispatcher is null)
        {
            throw new NullReferenceException(nameof(dispatcher));
        }

        var resp = await dispatcher.SimpleProcessingAsync(body);

        return Ok(resp);
    }

    [HttpPost("ecms-submit")]
    public async Task<IActionResult> SubmitToEcmsAsync()
    {
        using StreamReader st = new StreamReader(this.Request.Body);
        var body = await st.ReadToEndAsync();

        var wrapper = JsonConvert.DeserializeObject<SubmitToEcmsWrapper>(body);
        var service = serviceProvider.GetService<IEcmsService>();

        if (service is null)
        {
            throw new NullReferenceException(nameof(service));
        }

        if (wrapper is null)
        {
            throw new NullReferenceException(nameof(wrapper));
        }

        var resp = await service.SubmitToEcmsAsync(wrapper);

        return Ok(resp);
    }

    [HttpPost("vcms-submit")]
    public async Task<IActionResult> SubmitToVcmsAsync()
    {
        using StreamReader st = new StreamReader(this.Request.Body);
        var body = await st.ReadToEndAsync();

        ADI vcmsAdi = XmlHelper.DeserializeXML<ADI>(body);
        var service = serviceProvider.GetService<IVcmsService>();

        if (service is null)
        {
            throw new NullReferenceException(nameof(service));
        }

        if (vcmsAdi is null)
        {
            throw new NullReferenceException(nameof(vcmsAdi));
        }

        var resp = await service.SubmitToVcmsAsync(vcmsAdi);

        return Ok(resp);
    }
}