
using Azure.Core;
using Microsoft.AspNetCore.Mvc;
using MST.Common.Data;
using MST.Common.Messaging;
using NBA.NextGen.CMSPlatform.VODService.Application.Services;
using NBA.NextGen.CMSPlatform.VODService.Domain.CmsStatusNotifications;
using NBA.NextGen.CMSPlatform.VODService.Domain.Common;
using NBA.NextGen.CMSPlatform.VODService.Domain.Common.Enums;
using NBA.NextGen.CMSPlatform.VODService.Domain.Utilities;
using NBA.NextGen.CMSPlatform.VODService.Domain.Vcms;
using Newtonsoft.Json;

namespace Vod.Api.Controllers;

[Route("api/vod")]
[ApiController]
public class VodController : Controller
{
    private readonly ILogger logger;
    private readonly IMessageSender<AdiMessageWrapper> vcmsQueueClient;
    private readonly IMessageSender<UpdateToEcmsWrapper> ecmsQueueClient;
    private readonly IVodAssetRegistryService vodAssetRegistryService;

    public VodController(IMessageSenderFactory messageFactory, ILogger<VodController> logger, IVodAssetRegistryService vodAssetRegistryService)
    {
        this.vcmsQueueClient = messageFactory.Resolve<AdiMessageWrapper>();
        this.ecmsQueueClient = messageFactory.Resolve<UpdateToEcmsWrapper>();
        this.logger = logger;
        this.vodAssetRegistryService = vodAssetRegistryService;
    }

    [HttpGet("heartbeat")]
    public IActionResult HeartBeat()
    {
        return new OkResult();
    }

    [HttpPost("submit")]
    public async Task<IActionResult> SubmitToVcmsAsync()
    {
        using StreamReader st = new StreamReader(this.Request.Body);
        var body = await st.ReadToEndAsync();

        this.logger.LogInformation("Starting Invoke HTTP VOD Service Function.");
        ADI vcmsAdi;
        this.logger.LogInformation(body);
        vcmsAdi = XmlHelper.DeserializeXML<ADI>(body);

        var request = new AdiMessageWrapper()
        {
            Adi = vcmsAdi,
        };

        await this.vcmsQueueClient.SendAsync(request);
        await this.vodAssetRegistryService.AddAssetEventAsync(vcmsAdi.GetAdiAssetId(), AssetEventTypes.QueuedForVcmsSubmission, DateTime.UtcNow, body).ConfigureAwait(false);

        return new OkResult();
    }

    [HttpPost("update")]
    public async Task<IActionResult> UpdateEcmsAsync()
    {
        using StreamReader st = new StreamReader(this.Request.Body);
        var body = await st.ReadToEndAsync();
        this.logger.LogInformation("C# HTTP trigger CmsStatusNotificationFunction processed a request.");

        StatusNotification? statusNotification = JsonConvert.DeserializeObject<StatusNotification>(body);
        this.logger.LogInformation("mediaKindMessage body for CmsStatusNotificationFunction is " + body);
        this.logger.LogInformation("statusNotification status is " + statusNotification?.Status?.ToString().ToLowerInvariant());

        var message = new UpdateToEcmsWrapper()
        {
            StatusNotification = statusNotification,
            MediaKindMessage = body,
            ShouldMock = false,
        };

        var identifier = statusNotification?.Identifier;

        await this.ecmsQueueClient.SendAsync(message).ConfigureAwait(false);

        await this.vodAssetRegistryService.AddAssetEventAsync(identifier, AssetEventTypes.QueuedForEcmsUpdateSubmission, System.DateTime.UtcNow, body).ConfigureAwait(false);
        return new OkResult();
    }
}