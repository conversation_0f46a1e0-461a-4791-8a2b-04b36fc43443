using Microsoft.AspNetCore.Mvc;
using NBA.NextGen.CMSPlatform.VODService.Application.Services;

namespace Vod.Api.Controllers;

[Route("api/registry")]
[ApiController]
public class RegistryController
{
    private readonly ILogger<RegistryController> logger;
    private readonly IVodFileRegistryService vodFileRegistryService;
    private readonly IVodAssetRegistryService vodAssetRegistryService;
    
    public RegistryController(ILogger<RegistryController> logger, IVodFileRegistryService vodFileRegistryService, IVodAssetRegistryService vodAssetRegistryService)
    {
        this.logger = logger; 
        this.vodFileRegistryService = vodFileRegistryService;
        this.vodAssetRegistryService = vodAssetRegistryService;
    }

    [HttpGet("files/byName/{fileName}")]
    public async Task<IActionResult> RunFileRegistryNameSearchAsync(string fileName)
    {
        if (string.IsNullOrWhiteSpace(fileName))
        {
            return new BadRequestObjectResult("Filename is missing!");
        }

        var escapedFilename = Uri.UnescapeDataString(fileName);
        var results = await vodFileRegistryService.GetFileDetailsByNameAsync(escapedFilename).ConfigureAwait(false);

        return new OkObjectResult(results);
    }
    
    [HttpGet("files/byAssetId/{assetId}")]
    public async Task<IActionResult> Test(string assetId)
    {
        if (string.IsNullOrWhiteSpace(assetId))
        {
            return new BadRequestObjectResult("AssetId is missing!");
        }

        var results = await vodFileRegistryService.GetFileDetailsByAssetIdAsync(assetId).ConfigureAwait(false);

        return new OkObjectResult(results);
    }

    [HttpGet("assets/{assetId}")]
    public async Task<IActionResult> RunAssetRegistryIdSearchAsync(string assetId)
    {
        if (string.IsNullOrWhiteSpace(assetId))
        {
            return new BadRequestObjectResult("AssetId is missing!");
        }

        var results = await vodAssetRegistryService.GetAssetDetailsByAssetIdAsync(assetId).ConfigureAwait(false);

        return new OkObjectResult(results);
    }

    [HttpGet("assets/{assetId}/events")]
    public async Task<IActionResult> RunAssetEventRegistryIdSearchAsync(string assetId)
    {
        if (string.IsNullOrWhiteSpace(assetId))
        {
            return new BadRequestObjectResult("AssetId is missing!");
        }

        var results = await vodAssetRegistryService.GetEventsForAssetIdAsync(assetId).ConfigureAwait(false);

        return new OkObjectResult(results);
    }
}