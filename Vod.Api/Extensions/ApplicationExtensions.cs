using System.Diagnostics.CodeAnalysis;
using Vod.Common.Extensions;
using NBA.NextGen.CMSPlatform.VODService.Application.UseCases.FreeWheel;
using NBA.NextGen.CMSPlatform.VODService.Application.UseCases.Gam;

namespace Vod.Api.Extensions;

[ExcludeFromCodeCoverage]
public static class ApplicationExtensions
{
    public static IServiceCollection AddApplicationDependencies(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddSharedApplicationDependencies(configuration);
        return services;
    }

    public static IServiceCollection AddApplicationHandlers(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddSingleton<StartVcmsIngestionCommandHandler>();
        services.AddSingleton<PrepareGamToVcmsCommandHandler>();
        services.AddSingleton<GetCompanyListQueryHandler>();

        return services;
    }

    public static IServiceCollection AddApplicationFactories(this IServiceCollection services)
    {
        return services;
    }

    public static IServiceCollection AddApplicationConfiguration(this IServiceCollection services, IConfiguration configuration)
    {
        return services;
    }

    public static IServiceCollection AddApplicationRepositories(this IServiceCollection services)
    {
        return services;
    }
}