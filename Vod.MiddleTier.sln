
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 16
VisualStudioVersion = 25.0.1705.6
MinimumVisualStudioVersion = 10.0.40219.1
Project("{E53339B2-1760-4266-BCC7-CA923CBCF16C}") = "docker-compose", "docker-compose.dcproj", "{9466AD57-AA14-4559-BB5D-C207C6BAFA5A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Vod.Api", "Vod.Api\Vod.Api.csproj", "{A14EEAE3-068B-4D70-BC71-DBB016236B85}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "NBA.NextGen.CMSPlatform.VODService.Domain", "NBA.NextGen.CMSPlatform.VODService.Domain\NBA.NextGen.CMSPlatform.VODService.Domain.csproj", "{7F090EB6-8235-47C8-A289-C6A5179C78DA}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "NBA.NextGen.CMSPlatform.VODService.Application", "NBA.NextGen.CMSPlatform.VODService.Application\NBA.NextGen.CMSPlatform.VODService.Application.csproj", "{02379586-6ABD-4EE4-98F3-AA81FBABA655}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "NBA.NextGen.CMSPlatform.VODService.Infrastructure", "NBA.NextGen.CMSPlatform.VODService.Infrastructure\NBA.NextGen.CMSPlatform.VODService.Infrastructure.csproj", "{A36E8096-89A6-4292-80A6-0C70C51C1B0B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Vod.EcmsProcessor", "Vod.EcmsProcessor\Vod.EcmsProcessor.csproj", "{23CA5BE4-AE70-49A0-AF5B-6CAF52553C90}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Vod.Common", "Vod.Common\Vod.Common.csproj", "{5B95A2C0-8158-4F47-A5C2-AB7F6C9408AA}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Vod.VcmsProcessor", "Vod.VcmsProcessor\Vod.VcmsProcessor.csproj", "{28127793-8FC8-4274-AAE1-C8DA5F64C649}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Vod.IncomingAssetProcessor", "Vod.IncomingAssetProcessor\Vod.IncomingAssetProcessor.csproj", "{FC540E37-9333-491A-9443-89A7DD662C64}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Vod.VideoIndexerStateChecker", "Vod.VideoIndexerStateChecker\Vod.VideoIndexerStateChecker.csproj", "{191F6AC8-0BD8-49E6-80DA-4613D27CDB9A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Vod.DelayedResourceMonitor", "Vod.DelayedResourceMonitor\Vod.DelayedResourceMonitor.csproj", "{45078B4A-0EAF-49B9-AD11-3344D32B0870}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Vod.MediaConvertCron", "Vod.MediaConvertCron\Vod.MediaConvertCron.csproj", "{C4EEAC15-8A3C-47E2-A13A-60B7C9D258B8}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Vod.TranscribeCron", "Vod.TranscribeCron\Vod.TranscribeCron.csproj", "{EA495F1C-2F35-40EE-AE2D-F43E4563FE8C}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Vod.IncomingAssetCopyProcessor", "Vod.IncomingAssetCopyProcessor\Vod.IncomingAssetCopyProcessor.csproj", "{C07A55CA-ACA5-44B3-9877-442B0757BEFA}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{9466AD57-AA14-4559-BB5D-C207C6BAFA5A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9466AD57-AA14-4559-BB5D-C207C6BAFA5A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9466AD57-AA14-4559-BB5D-C207C6BAFA5A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9466AD57-AA14-4559-BB5D-C207C6BAFA5A}.Release|Any CPU.Build.0 = Release|Any CPU
		{A14EEAE3-068B-4D70-BC71-DBB016236B85}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A14EEAE3-068B-4D70-BC71-DBB016236B85}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A14EEAE3-068B-4D70-BC71-DBB016236B85}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A14EEAE3-068B-4D70-BC71-DBB016236B85}.Release|Any CPU.Build.0 = Release|Any CPU
		{7F090EB6-8235-47C8-A289-C6A5179C78DA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7F090EB6-8235-47C8-A289-C6A5179C78DA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7F090EB6-8235-47C8-A289-C6A5179C78DA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7F090EB6-8235-47C8-A289-C6A5179C78DA}.Release|Any CPU.Build.0 = Release|Any CPU
		{02379586-6ABD-4EE4-98F3-AA81FBABA655}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{02379586-6ABD-4EE4-98F3-AA81FBABA655}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{02379586-6ABD-4EE4-98F3-AA81FBABA655}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{02379586-6ABD-4EE4-98F3-AA81FBABA655}.Release|Any CPU.Build.0 = Release|Any CPU
		{A36E8096-89A6-4292-80A6-0C70C51C1B0B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A36E8096-89A6-4292-80A6-0C70C51C1B0B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A36E8096-89A6-4292-80A6-0C70C51C1B0B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A36E8096-89A6-4292-80A6-0C70C51C1B0B}.Release|Any CPU.Build.0 = Release|Any CPU
		{23CA5BE4-AE70-49A0-AF5B-6CAF52553C90}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{23CA5BE4-AE70-49A0-AF5B-6CAF52553C90}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{23CA5BE4-AE70-49A0-AF5B-6CAF52553C90}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{23CA5BE4-AE70-49A0-AF5B-6CAF52553C90}.Release|Any CPU.Build.0 = Release|Any CPU
		{5B95A2C0-8158-4F47-A5C2-AB7F6C9408AA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5B95A2C0-8158-4F47-A5C2-AB7F6C9408AA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5B95A2C0-8158-4F47-A5C2-AB7F6C9408AA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5B95A2C0-8158-4F47-A5C2-AB7F6C9408AA}.Release|Any CPU.Build.0 = Release|Any CPU
		{28127793-8FC8-4274-AAE1-C8DA5F64C649}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{28127793-8FC8-4274-AAE1-C8DA5F64C649}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{28127793-8FC8-4274-AAE1-C8DA5F64C649}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{28127793-8FC8-4274-AAE1-C8DA5F64C649}.Release|Any CPU.Build.0 = Release|Any CPU
		{FC540E37-9333-491A-9443-89A7DD662C64}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FC540E37-9333-491A-9443-89A7DD662C64}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FC540E37-9333-491A-9443-89A7DD662C64}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FC540E37-9333-491A-9443-89A7DD662C64}.Release|Any CPU.Build.0 = Release|Any CPU
		{191F6AC8-0BD8-49E6-80DA-4613D27CDB9A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{191F6AC8-0BD8-49E6-80DA-4613D27CDB9A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{191F6AC8-0BD8-49E6-80DA-4613D27CDB9A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{191F6AC8-0BD8-49E6-80DA-4613D27CDB9A}.Release|Any CPU.Build.0 = Release|Any CPU
		{45078B4A-0EAF-49B9-AD11-3344D32B0870}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{45078B4A-0EAF-49B9-AD11-3344D32B0870}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{45078B4A-0EAF-49B9-AD11-3344D32B0870}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{45078B4A-0EAF-49B9-AD11-3344D32B0870}.Release|Any CPU.Build.0 = Release|Any CPU
		{C4EEAC15-8A3C-47E2-A13A-60B7C9D258B8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C4EEAC15-8A3C-47E2-A13A-60B7C9D258B8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C4EEAC15-8A3C-47E2-A13A-60B7C9D258B8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C4EEAC15-8A3C-47E2-A13A-60B7C9D258B8}.Release|Any CPU.Build.0 = Release|Any CPU
		{EA495F1C-2F35-40EE-AE2D-F43E4563FE8C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{EA495F1C-2F35-40EE-AE2D-F43E4563FE8C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{EA495F1C-2F35-40EE-AE2D-F43E4563FE8C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{EA495F1C-2F35-40EE-AE2D-F43E4563FE8C}.Release|Any CPU.Build.0 = Release|Any CPU
		{C07A55CA-ACA5-44B3-9877-442B0757BEFA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C07A55CA-ACA5-44B3-9877-442B0757BEFA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C07A55CA-ACA5-44B3-9877-442B0757BEFA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C07A55CA-ACA5-44B3-9877-442B0757BEFA}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {DD66C8D5-FEEA-46A6-9552-40B8C3297293}
	EndGlobalSection
EndGlobal
