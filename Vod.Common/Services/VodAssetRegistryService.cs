using System.Reflection;
using Azure.Messaging.EventGrid.SystemEvents;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using MST.Common.Data;
using MST.Common.Messaging;
using NBA.NextGen.CMSPlatform.VODService.Application.Services;
using NBA.NextGen.CMSPlatform.VODService.Domain.Common;
using NBA.NextGen.CMSPlatform.VODService.Domain.Common.Enums;
using NBA.NextGen.CMSPlatform.VODService.Domain.Config;
using NBA.NextGen.CMSPlatform.VODService.Infrastructure.Models;
using NBA.NextGen.CMSPlatform.VODService.Infrastructure.Models.Events;

namespace Vod.Common.Services;

public class VodAssetRegistryService : IVodAssetRegistryService
{
    private readonly IObjectRepository<AssetEventEntry> _eventRepository;
    private readonly IObjectRepository<AssetRegistryEntry> _repository;
    private readonly IQueryableRepository<AssetEventEntry> _queryableEventRepository;
    private readonly IQueryableRepository<AssetRegistryEntry> _queryableRepository;
    private readonly ILogger _Logger;
    private readonly ISlackNotificationService _slackNotificationService;
    private readonly string _envName;
    private readonly IMessageSenderFactory _messageSenderFactory;
    private readonly NormalFlowSettings _normalFlowSettings;

    public VodAssetRegistryService(
        IObjectRepositoryFactory factory,
        IQueryableRepositoryFactory queryableFactory,
        ILogger<VodAssetRegistryService> logger,
        IMessageSenderFactory messageSenderFactory,
        ISlackNotificationService slackNotificationService,
        IConfiguration configuration,
        IOptions<NormalFlowSettings> settings
    )
    {
        if (factory is null)
        {
            throw new ArgumentNullException(nameof(factory));
        }
        _repository = factory.Resolve<AssetRegistryEntry>();
        _eventRepository = factory.Resolve<AssetEventEntry>();
        _queryableRepository = queryableFactory.Resolve<AssetRegistryEntry>();
        _queryableEventRepository = queryableFactory.Resolve<AssetEventEntry>();
        _Logger = logger;
        _slackNotificationService = slackNotificationService;
        _messageSenderFactory = messageSenderFactory;
        _envName = configuration.GetValue<string>("ENV") ?? "uknown";
        _normalFlowSettings = settings.Value;
    }

    /// <inheritdoc />
    public async Task CreateAssetEntryAsync(
        string assetId,
        string fileName,
        IngestionFlowTypes flowType
    )
    {
        if (assetId == null)
        {
            _Logger.LogError("Cannot add entry to null asset id!");
            return;
        }

        var entry = await FindAssetRegistryEntryByIdAsync(assetId).ConfigureAwait(false);

        if (entry != null)
        {
            await AddAssetEventAsync(
                    assetId,
                    AssetEventTypes.IngestionRestarted,
                    System.DateTime.UtcNow,
                    string.Empty
                )
                .ConfigureAwait(false);
            return;
        }

        var dt = DateTime.UtcNow;

        entry = new AssetRegistryEntry()
        {
            Id = assetId,
            AssetState = AssetStates.Processing,
            TriggerFileName = fileName,
            IngestionFlowName = ResolveFlowName(flowType),
            LastEventUtc = dt,
        };

        try
        {
            await _repository.CreateItemAsync(entry).ConfigureAwait(false);
            await AddAssetEventAsync(assetId, AssetEventTypes.IngestionStarted, dt)
                .ConfigureAwait(false);
        }
        catch (Exception e)
        {
            _Logger.LogError(e, $"Failed to create Asset Registry Entry for AssetId: {assetId}");
        }
    }

    /// <inheritdoc/>
    public async Task AddAssetEventAsync(
        string assetId,
        AssetEventTypes eventType,
        DateTime timestampUtc,
        string message = ""
    )
    {
        if (assetId == null)
        {
            _Logger.LogError("Cannot add event to null asset id!");
            return;
        }

        var evt = new AssetEventEntry()
        {
            Id = Guid.NewGuid().ToString(),
            AssetId = assetId,
            EventType = eventType,
            TimeStampUtc = timestampUtc,
            Message = message,
        };

        var resolvedAssetState = DetermineAssetStateFromEvent(eventType);

        var isError = CheckForErrorState(evt.EventType);
        var json = CreateUpdateMessage(evt, resolvedAssetState, false);
        await _slackNotificationService.WriteJsonAsync(json);

        var existingEvents = await _queryableEventRepository
            .GetItemsAsync(a => a.AssetId == assetId && a.EventType == eventType)
            .ConfigureAwait(false);

        if (isError && existingEvents is not null && !existingEvents.Any())
        {
            var errorJson = CreateUpdateMessage(evt, resolvedAssetState, isError);
            await _slackNotificationService.WriteErrorJsonAsync(errorJson).ConfigureAwait(false);
        }

        try
        {
            await _eventRepository.CreateItemAsync(evt).ConfigureAwait(false);
            await _repository
                .OptimisticUpdateItemAsync(
                    assetId,
                    a =>
                    {
                        a.AssetState = resolvedAssetState;
                        return a;
                    }
                )
                .ConfigureAwait(false);
            if (_normalFlowSettings.EnableEventNotifier)
            {
                switch (eventType)
                {
                    case AssetEventTypes.SuccessfullySentToEcms:
                        var ecsender = _messageSenderFactory.Resolve<ECMSPostCreationSuccess>();
                        await ecsender.SendAsync(new ECMSPostCreationSuccess()
                        {
                            Id = assetId,
                            Content = message
                        });
                        break;
                    case AssetEventTypes.IngestionStarted:
                        var spsender = _messageSenderFactory.Resolve<StartProcessing>();
                        await spsender.SendAsync(new StartProcessing()
                        {
                            Id = assetId,
                            Content = message
                        });
                        break;
                    case AssetEventTypes.VcmsProgressResponseReceived:
                        var mkpsender = _messageSenderFactory.Resolve<MediaKindProgress>();
                        await mkpsender.SendAsync(new MediaKindProgress()
                        {
                            Id = assetId,
                            Content = message
                        });
                        break;
                    case AssetEventTypes.VcmsCompletedResponseReceived:
                        var mkcsender = _messageSenderFactory.Resolve<MediaKindProgressComplete>();
                        await mkcsender.SendAsync(new MediaKindProgressComplete()
                        {
                            Id = assetId,
                            Content = message
                        });

                        break;
                }
            }
        }
        catch (Exception e)
        {
            _Logger.LogError(e, $"Could not add Event Type: {eventType} for AssetId: {assetId}");
        }
    }

    /// <inheritdoc />
    public async Task<AssetRegistryDetails?> GetAssetDetailsByAssetIdAsync(string assetId)
    {
        var entry = await FindAssetRegistryEntryByIdAsync(assetId).ConfigureAwait(false);

        if (entry != null)
        {
            return MapToDetails(entry);
        }

        return null;
    }

    /// <inheritdoc />
    public async Task<List<AssetEventDetails>> GetEventsForAssetIdAsync(string assetId)
    {
        var entries = await _queryableEventRepository
            .GetItemsAsync(a => a.AssetId == assetId, x => x.TimeStampUtc, false);
        return entries?.Select(MapToDetails).ToList() ?? new List<AssetEventDetails>();
    }

    /// <summary>
    /// Checks the status and writes and ships and extra log statement.
    /// </summary>
    /// <param name="state">The entry.</param>
    /// <returns>A Task.</returns>
    private bool CheckForErrorState(AssetEventTypes state)
    {
        return state switch
        {
            AssetEventTypes.LibertyLiveMissingCaptions => true,
            AssetEventTypes.IngestionPendingRetry => true,
            AssetEventTypes.IngestionFailed => true,
            AssetEventTypes.TranscribingFailed => true,
            AssetEventTypes.EcmsResponseIndicatesFailure => true,
            AssetEventTypes.FailedToSendEcms => true,
            AssetEventTypes.FailedToSendVcmsResponseToEcms => true,
            AssetEventTypes.VcmsFailureResponseReceived => true,
            AssetEventTypes.GamVcmsFailureResponseReceived => true,
            AssetEventTypes.FreeWheelVcmsFailureResponseReceived => true,
            AssetEventTypes.IngestionBlockedThresholdAssetMissing => true,
            AssetEventTypes.IngestionFailedGMSIssue => true,
            AssetEventTypes.AssetStillMissingAfterScan => true,
            AssetEventTypes.IngestionFailedAssetMissingTimeout => true,
            AssetEventTypes.GamResponseFailedToSend => true,
            AssetEventTypes.FreeWheelResponseFailedToSend => true,
            AssetEventTypes.EcmsSubmitMessageDeadLettered => true,
            AssetEventTypes.EcmsUpdateMessageDeadLettered => true,
            AssetEventTypes.VcmsSubmitMessageDeadLettered => true,
            AssetEventTypes.EcmsAssetCacheNotFound => true,
            _ => false,
        };
    }

    /// <summary>
    /// Maps a data model to a dto.
    /// </summary>
    /// <param name="entry">The data model.</param>
    /// <returns>The dto.</returns>
    private AssetRegistryDetails MapToDetails(AssetRegistryEntry entry)
    {
        return new AssetRegistryDetails()
        {
            AssetId = entry.Id,
            IngestionFlowName = entry.IngestionFlowName,
            AssetState = entry.AssetState,
            LastEventUtc = entry.LastEventUtc,
            TriggerFileName = entry.TriggerFileName,
        };
    }

    /// <summary>
    /// Maps a data model to a dto.
    /// </summary>
    /// <param name="entry">The data model.</param>
    /// <returns>The dto.</returns>
    private AssetEventDetails MapToDetails(AssetEventEntry entry)
    {
        return new AssetEventDetails()
        {
            AssetId = entry.AssetId,
            EventType = entry.EventType,
            EventTypeName = entry.EventType.ToString(),
            TimeStampUtc = entry.TimeStampUtc,
            Message = entry.Message,
        };
    }

    /// <summary>
    /// Returns an asset entry by id.
    /// </summary>
    /// <param name="assetId">The asset id.</param>
    /// <returns>An entry.</returns>
    private async Task<AssetRegistryEntry?> FindAssetRegistryEntryByIdAsync(string assetId)
    {
        try
        {
            var entries = await _queryableRepository
                .GetItemsAsync(e => e.Id == assetId)
                .ConfigureAwait(false);

            if (entries is null || !entries.Any())
            {
                _Logger.LogWarning($"No entry was found from query, AssetId: {assetId}");
                return null;
            }

            if (entries.Count() > 1)
            {
                _Logger.LogError(
                    $"More than one asset was returned from query, AssetId: {assetId}"
                );
                return null;
            }

            return entries.First();
        }
        catch (Exception e)
        {
            _Logger.LogError(e, $"Failed to query for entry for asset id {assetId}");
        }

        return null;
    }

    /// <summary>
    /// Determines the Asset State by the given event type.
    /// </summary>
    /// <param name="eventType">The event type.</param>
    /// <returns>The asset state.</returns>
    private AssetStates DetermineAssetStateFromEvent(AssetEventTypes eventType)
    {
        return eventType switch
        {
            AssetEventTypes.IngestionStarted => AssetStates.Processing,
            AssetEventTypes.IngestionCompleted => AssetStates.Processing,
            AssetEventTypes.IngestionPendingRetry => AssetStates.Processing,
            AssetEventTypes.IngestionRestarted => AssetStates.Processing,
            AssetEventTypes.IngestionBlockedAssetMissing => AssetStates.Processing,
            AssetEventTypes.IngestionBlockedThresholdAssetMissing => AssetStates.Processing,
            AssetEventTypes.IngestionFailedGMSIssue => AssetStates.Processing,
            AssetEventTypes.AssetStillMissingAfterScan => AssetStates.Processing,
            AssetEventTypes.GamRequestReceived => AssetStates.Processing,
            AssetEventTypes.GamResponseFailedToSend => AssetStates.Processing,
            AssetEventTypes.GamResponseSentSuccessfully => AssetStates.Completed,
            AssetEventTypes.GamSentToVcms => AssetStates.AwaitingVcms,
            AssetEventTypes.GamVcmsResponseReceived => AssetStates.Processing,
            AssetEventTypes.GamVcmsFailureResponseReceived => AssetStates.Failed,
            AssetEventTypes.FreeWheelRequestReceived => AssetStates.Processing,
            AssetEventTypes.FreeWheelResponseFailedToSend => AssetStates.Processing,
            AssetEventTypes.FreeWheelResponseSentSuccessfully => AssetStates.Completed,
            AssetEventTypes.FreeWheelSentToVcms => AssetStates.AwaitingVcms,
            AssetEventTypes.FreeWheelVcmsResponseReceived => AssetStates.Processing,
            AssetEventTypes.FreeWheelVcmsFailureResponseReceived => AssetStates.Failed,
            AssetEventTypes.IngestionFailedAssetMissingTimeout => AssetStates.Processing,
            AssetEventTypes.EcmsSubmitMessageDeadLettered => AssetStates.Failed,
            AssetEventTypes.EcmsAssetCacheNotFound => AssetStates.Failed,
            AssetEventTypes.EcmsUpdateMessageDeadLettered => AssetStates.Failed,
            AssetEventTypes.VcmsSubmitMessageDeadLettered => AssetStates.Failed,
            AssetEventTypes.IngestionFailed => AssetStates.Failed,
            AssetEventTypes.TranscribingRequested => AssetStates.AwaitingTranscribing,
            AssetEventTypes.TranscribingAdvanceToTranscribe => AssetStates.AwaitingTranscribing,
            AssetEventTypes.TranscribingBackflowCompleted => AssetStates.AwaitingTranscribing,
            AssetEventTypes.TranscribingFailed => AssetStates.AwaitingTranscribing,
            AssetEventTypes.TranscribingCompleted => AssetStates.AwaitingTranscribing,
            AssetEventTypes.QueuedForEcmsSubmission => AssetStates.AwaitingEcms,
            AssetEventTypes.SuccessfullySentToEcms => AssetStates.AwaitingEcms,
            AssetEventTypes.EcmsResponseIndicatesFailure => AssetStates.AwaitingEcms,
            AssetEventTypes.FailedToSendEcms => AssetStates.AwaitingEcms,
            AssetEventTypes.SuccessfullySentVcmsResponseToEcms => AssetStates.AwaitingVcms,
            AssetEventTypes.FailedToSendVcmsResponseToEcms => AssetStates.AwaitingVcms,
            AssetEventTypes.QueuedForEcmsUpdateSubmission => AssetStates.AwaitingEcms,
            AssetEventTypes.QueuedForVcmsSubmission => AssetStates.AwaitingVcms,
            AssetEventTypes.SuccessfullySentToVcms => AssetStates.AwaitingVcms,
            AssetEventTypes.VcmsProgressResponseReceived => AssetStates.AwaitingVcms,
            AssetEventTypes.VcmsCompletedResponseReceived => AssetStates.AwaitingVcms,
            AssetEventTypes.VcmsFailureResponseReceived => AssetStates.AwaitingVcms,
            AssetEventTypes.WorkflowCompleted => AssetStates.Completed,
            _ => AssetStates.Unknown,
        };
    }

    private string ResolveFlowName(IngestionFlowTypes type)
    {
        return type switch
        {
            IngestionFlowTypes.NormalFlow => "normal-flow",
            IngestionFlowTypes.DirectFlow => "direct-flow",
            IngestionFlowTypes.LiveToVod => "livetovod-flow",
            IngestionFlowTypes.NoOp => "no-op",
            IngestionFlowTypes.Gam => "gam",
            IngestionFlowTypes.FreeWheel => "freewheel",
            _ => "unknown-flow",
        };
    }

    /// <summary>
    /// Creates a nicely formatted slack message.
    /// </summary>
    /// <param name="entry">Entry.</param>
    /// <param name="resolvedState">The resolved state.</param>
    /// <param name="isErrorState">Indicates an error state.</param>
    /// <returns>A formatted slack message.</returns>
    private string CreateUpdateMessage(
        AssetEventEntry entry,
        AssetStates resolvedState,
        bool isErrorState
    )
    {
        var serviceName = Assembly.GetEntryAssembly()?.GetName()?.Name ?? "Uknown";
        var errorBlock = string.Empty;
        var safeError = string.IsNullOrWhiteSpace(entry.Message)
            ? entry.EventType.ToString()
            : entry.Message;
        if (isErrorState)
        {
            errorBlock =
                $@"
,
            {{
                ""type"": ""section"",
                ""text"": {{
                    ""type"": ""mrkdwn"",
                    ""text"": "":warning: *Error Message:*""
                }}
            }},
            {{
                ""type"": ""rich_text"",
                ""elements"": [
                    {{
                        ""type"": ""rich_text_preformatted"",
                        ""border"": 0,
                        ""elements"": [
                            {{
                                ""type"": ""text"",
                                ""text"": ""{safeError}""
                            }}
                        ]
                    }}
                ]
            }},
            {{
                ""type"": ""actions"",
                ""elements"": [
                    {{
                        ""type"": ""button"",
                        ""text"": {{
                            ""type"": ""plain_text"",
                            ""text"": ""View VOD Service Playbook  :atlassian-confluence:"",
                            ""emoji"": true
                        }},
                        ""url"": ""https://nba.atlassian.net/wiki/spaces/DMSVMT/pages/8976040100/VOD+Service+Playbook""
                    }}
                ]
            }}
";
        }

        return $@"
{{
	""blocks"": [
		{{
			""type"": ""header"",
			""text"": {{
				""type"": ""plain_text"",
				""text"": "":vod-video-play: Asset Event Update"",
				""emoji"": true
			}}
		}},
		{{
			""type"": ""section"",
			""fields"": [
				{{
					""type"": ""mrkdwn"",
					""text"": ""*Asset Id:*\n{entry.AssetId}""
				}},
				{{
					""type"": ""mrkdwn"",
					""text"": ""*Event Type:*\n{entry.EventType} ({(int)entry.EventType})""
				}},
				{{
					""type"": ""mrkdwn"",
					""text"": ""*When UTC:*\n{entry.TimeStampUtc}""
				}},
				{{
					""type"": ""mrkdwn"",
					""text"": ""*Asset State:*\n{resolvedState}""
				}},
                {{
					""type"": ""mrkdwn"",
					""text"": ""*Service:*\n{serviceName}""
				}},
                {{
					""type"": ""mrkdwn"",
					""text"": ""*Environment:*\nAWS-{_envName}""
				}}
			]
		}}{errorBlock}
	]
}}
";
    }
}
