using System;
using Microsoft.Extensions.DependencyInjection;
using MST.Common.Messaging;

namespace Vod.Common.Services;

public static class DummySenderExtensions
{
    public static IServiceCollection RegisterDummySender<T>(
        this IServiceCollection services
    )
        where T : class
    {

        services.AddKeyedSingleton<IMessageSender<T>, DummySender<T>>(
            typeof(T).Name,
            (sp, o) =>
            {

                return new DummySender<T>();
            });

        return services;
    }

}

public class DummySender<T> : IMessageSender<T> where T : class
{

    public Task<bool> SendAsync(T message)
    {
        return Task.FromResult(true);
    }
}
