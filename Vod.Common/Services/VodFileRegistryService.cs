using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using MST.Common.Data;
using NBA.NextGen.CMSPlatform.VODService.Application.Services;
using NBA.NextGen.CMSPlatform.VODService.Domain.Common;
using NBA.NextGen.CMSPlatform.VODService.Domain.Common.Enums;
using NBA.NextGen.CMSPlatform.VODService.Infrastructure.Models;
using Vod.Common.Extensions;

namespace Vod.Common.Services;

public class VodFileRegistryService : IVodFileRegistryService
{
    protected IObjectRepository<FileRegistryEntry> _repository { get; }
    protected IQueryableRepository<FileRegistryEntry> _queryableRepository { get; }
    protected ILogger Logger { get; }
    protected ISlackNotificationService SlackNotificationService { get; }
    private readonly string _envName;

    public VodFileRegistryService(IObjectRepositoryFactory factory, IQueryableRepositoryFactory queryableFactory, ILogger<VodFileRegistryService> logger, ISlackNotificationService slackNotificationService, IConfiguration configuration)
    {
        if (factory is null)
        {
            throw new ArgumentNullException(nameof(factory));
        }

        _repository = factory.Resolve<FileRegistryEntry>();
        _queryableRepository = queryableFactory.Resolve<FileRegistryEntry>();
        Logger = logger;
        SlackNotificationService = slackNotificationService;
        _envName = configuration.GetValue<string>("ENV") ?? "uknown";
    }

    /// <inheritdoc />
    public async Task RecordFileArrivalAsync(string fileName, string parentFolder, IngestionFlowTypes flowType, DateTime arrivalTimestampUtc)
    {
        var entry = await FindEntryForFileAsync(fileName).ConfigureAwait(false);

        if (entry != null)
        {
            await _repository.OptimisticUpdateItemAsync(entry.Id, e =>
            {
                e.ArrivalTimeUtc = arrivalTimestampUtc;
                return e;
            });

            return;
        }

        entry = new FileRegistryEntry()
        {
            Id = Guid.NewGuid().ToString(),
            IngestionFlowName = ResolveFlowName(flowType),
            ArrivalTimeUtc = arrivalTimestampUtc,
            ParentFolderName = parentFolder,
            FileName = fileName,
            AssetId = string.Empty,
            IsWorkflowTriggerFile = false,
        };

        var json = CreateUpdateMessage(fileName, $"Arrived Via {flowType}", arrivalTimestampUtc);
        await SlackNotificationService.WriteJsonAsync(json).ConfigureAwait(false);

        try
        {
            await _repository.CreateItemAsync(entry).ConfigureAwait(false);
        }
        catch (Exception e)
        {
            Logger.LogError(e, $"Failed to create File Registry Entry for {fileName}");
        }
    }

    public Task LogFileCopyAsync(string relativePath, IngestionFlowTypes flowType, DateTime arrivalTimestampUtc, string sourceContainer, string destinationContainer, double sizeInBytes, TimeSpan duration)
    {
        var json = CreateFileCopyMessage(relativePath, flowType, sourceContainer, destinationContainer, arrivalTimestampUtc, sizeInBytes, duration);
        return SlackNotificationService.WriteJsonAsync(json);
    }

    /// <inheritdoc />
    public async Task RegisterFileProcessedAsync(string fileName, bool triggeredWorkflow, DateTime processedTimestampUtc, Exception? exception)
    {
        var entry = await FindEntryForFileAsync(fileName).ConfigureAwait(false);
        if (entry is null)
        {
            return;
        }

        await _repository.OptimisticUpdateItemAsync(entry.Id, e =>
        {
            e.IsWorkflowTriggerFile = triggeredWorkflow;
            e.ProcessedTimeUtc = processedTimestampUtc;

            if (exception != null)
            {
                e.IsFailure = true;
                e.ExceptionMessage = exception.Message;
            }

            return e;
        });
    }

    /// <inheritdoc />
    public async Task CorrelateFileToAssetIdAsync(string fileName, string assetId)
    {
        var entry = await FindEntryForFileAsync(fileName).ConfigureAwait(false);
        if (entry is null)
        {
            return;
        }

        var json = CreateUpdateMessage(fileName, $"Assigned AssetId {assetId}", DateTime.UtcNow);
        await SlackNotificationService.WriteJsonAsync(json).ConfigureAwait(false);

        await _repository.OptimisticUpdateItemAsync(entry.Id, e =>
        {
            e.AssetId = assetId;
            return e;
        });
    }

    /// <inheritdoc />
    public async Task<FileRegistryDetails?> GetFileDetailsByNameAsync(string fileName)
    {
        var entry = await FindEntryForFileAsync(fileName).ConfigureAwait(false);
        if (entry != null)
        {
            return MapToDetails(entry);
        }

        return null;
    }

    /// <inheritdoc />
    public async Task<List<FileRegistryDetails>> GetFileDetailsByAssetIdAsync(string assetId)
    {
        var entries = await _queryableRepository.GetItemsAsync(a => a.AssetId == assetId).ConfigureAwait(false);
        return entries?.Select(MapToDetails).ToList() ?? new List<FileRegistryDetails>();
    }

    /// <summary>
    /// Maps a data model to a dto.
    /// </summary>
    /// <param name="entry">The data model.</param>
    /// <returns>The dto.</returns>
    private FileRegistryDetails MapToDetails(FileRegistryEntry entry)
    {
        return new FileRegistryDetails()
        {
            IngestionFlowName = entry.IngestionFlowName,
            ParentFolderName = entry.ParentFolderName,
            ArrivalTimeUtc = entry.ArrivalTimeUtc,
            IsWorkflowTriggerFile = entry.IsWorkflowTriggerFile,
            AssetId = entry.AssetId,
            FileName = entry.FileName,
            ExceptionMessage = entry.ExceptionMessage,
            IsFailure = entry.IsFailure,
            ProcessedTimeUtc = entry.ProcessedTimeUtc,
        };
    }

    private string ResolveFlowName(IngestionFlowTypes type)
    {
        return type switch
        {
            IngestionFlowTypes.NormalFlow => "normal-flow",
            IngestionFlowTypes.DirectFlow => "direct-flow",
            IngestionFlowTypes.LiveToVod => "livetovod-flow",
            IngestionFlowTypes.NoOp => "no-op",
            IngestionFlowTypes.Gam => "gam",
            IngestionFlowTypes.FreeWheel => "freewheel",
            _ => "unknown-flow",
        };
    }

    /// <summary>
    /// FindEntryForFileAsync.
    /// </summary>
    /// <param name="fileName">Filename.</param>
    /// <returns>An entry or null.</returns>
    private async Task<FileRegistryEntry?> FindEntryForFileAsync(string fileName)
    {
        try
        {
            var entries = await _queryableRepository.GetItemsAsync(e => e.FileName == fileName).ConfigureAwait(false);

            if (entries is null || !entries.Any())
            {
                Logger.LogWarning($"No entry was found from query, Filename: {fileName}");
                return null;
            }

            if (entries.Count() > 1)
            {
                Logger.LogError($"More than one filename was returned from query, Filename: {fileName}");
                return null;
            }

            return entries.First();
        }
        catch (Exception e)
        {
            Logger.LogError(e, $"Failed to query for entry for file {fileName}");
        }

        return null;
    }

    /// <summary>
    /// Creates a nicely formatted slack message.
    /// </summary>
    /// <param name="filename">The filename.</param>
    /// <param name="message">The message.</param>
    /// <param name="timestamp">The timestamp.</param>
    /// <returns>A formatted slack message.</returns>
    private string CreateUpdateMessage(string filename, string message, DateTime timestamp)
    {
        return $@"
{{
	""blocks"": [
		{{
			""type"": ""header"",
			""text"": {{
				""type"": ""plain_text"",
				""text"": "":vod-video-play: Incoming File Update"",
				""emoji"": true
			}}
		}},
		{{
			""type"": ""section"",
			""fields"": [
				{{
					""type"": ""mrkdwn"",
					""text"": ""*File Name: *\n{filename}""
				}}
			]
		}},
		{{
			""type"": ""section"",
			""fields"": [
				{{
					""type"": ""mrkdwn"",
					""text"": ""*When UTC:*\n{timestamp}""
				}}
			]
		}},
		{{
			""type"": ""section"",
			""fields"": [
				{{
					""type"": ""mrkdwn"",
					""text"": ""*Details:*\n{message}""
				}},
                {{
					""type"": ""mrkdwn"",
					""text"": ""*Environment:*\nAWS-{_envName}""
				}}
			]
		}}
	]
}}
";
    }

    private string CreateFileCopyMessage(string filename, IngestionFlowTypes flowType, string source, string destination, DateTime timestamp, double sizeInBytes, TimeSpan duration)
    {
        var formattedDuration = duration.ToExpandedFormatString();
        return $@"
{{
	""blocks"": [
		{{
			""type"": ""header"",
			""text"": {{
				""type"": ""plain_text"",
				""text"": "":vod-video-play: File Copy Update"",
				""emoji"": true
			}}
		}},
		{{
			""type"": ""section"",
			""fields"": [
				{{
					""type"": ""mrkdwn"",
					""text"": ""*File Name: *\n{filename}""
				}}
			]
		}},
		{{
			""type"": ""section"",
			""fields"": [
				{{
					""type"": ""mrkdwn"",
					""text"": ""*When UTC:*\n{timestamp}""
				}}
			]
		}},
		{{
			""type"": ""section"",
			""fields"": [
				{{
					""type"": ""mrkdwn"",
					""text"": ""*Source:*\n{source}""
				}},
                {{
					""type"": ""mrkdwn"",
					""text"": ""*Destination:*\n{destination}""
				}},
                {{
					""type"": ""mrkdwn"",
					""text"": ""*Size:*\n{sizeInBytes.ToSizeString()}""
				}},
                {{
					""type"": ""mrkdwn"",
					""text"": ""*Duration:*\n{formattedDuration}""
				}},
                {{
					""type"": ""mrkdwn"",
					""text"": ""*Flow:*\n{flowType}""
				}},
                {{
					""type"": ""mrkdwn"",
					""text"": ""*Environment:*\n{_envName}""
				}}
			]
		}}
	]
}}
";
    }
}
