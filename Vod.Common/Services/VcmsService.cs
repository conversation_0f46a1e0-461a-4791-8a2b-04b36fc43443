using System.Diagnostics.CodeAnalysis;
using System.Net.Http.Headers;
using System.Text;
using AutoMapper;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using NBA.NextGen.CMSPlatform.VODService.Application.Services;
using NBA.NextGen.CMSPlatform.VODService.Domain.Common.Enums;
using NBA.NextGen.CMSPlatform.VODService.Domain.Config;
using NBA.NextGen.CMSPlatform.VODService.Domain.Utilities;
using NBA.NextGen.CMSPlatform.VODService.Domain.Vcms;

namespace Vod.Common.Services;

public class VcmsService : IVcmsService
{
    private readonly IMapper mapper;
    private readonly HttpClient httpClient;
    private readonly VcmsSettings vcmsSettings;
    private readonly ILogger logger;
    private readonly IVodAssetRegistryService vodAssetRegistryService;
    private readonly string? _vcmsIngestKey;
    private readonly IConfiguration _configuration;

    public VcmsService(IMapper mapper, [NotNull] IOptions<VcmsSettings> options, ILogger<VcmsService> logger, IVodAssetRegistryService vodAssetRegistryService, IConfiguration configuration, HttpClient httpClient)
    {
        this.logger = logger;
        this.mapper = mapper;
        this.httpClient = httpClient;
        this.vcmsSettings = options.Value;
        this.vodAssetRegistryService = vodAssetRegistryService;
        _configuration = configuration;
        _vcmsIngestKey = configuration["vcms_ingest_key"];
    }

    public async Task<bool> SubmitToVcmsAsync(ADI adi)
    {
        // if (this.vcmsSettings.EnableEventNotifier)
        // {
        //     var eventNotifier = this.eventNotifierProvider.GetNotifier(this.vcmsSettings.EventGridTopic);
        //     var assetTitle = adi?.Asset?.Metadata?.AppData?.ToList().Find(appData => appData.Name.Equals("Title", StringComparison.OrdinalIgnoreCase))?.Value;
        //     await eventNotifier.NotifyAsync(this.vcmsSettings.VodIngestionStartEventType, assetTitle ?? string.Empty, adi).ConfigureAwait(false);
        // }

        bool.TryParse(_configuration.GetSection("AssetDelivery")["UseAzure"], out var useAzure);
        var endpointUrl = useAzure
            ? vcmsSettings.AzureEndpoint
            : vcmsSettings.Endpoint;

        var urlBuilder = new System.Text.StringBuilder();
        urlBuilder.Append(endpointUrl != null ? endpointUrl.TrimEnd('/') : "").Append("/services/titles/ingest?");
        urlBuilder.Append(System.Uri.EscapeDataString("providerId") + "=").Append(System.Uri.EscapeDataString(this.vcmsSettings.ProviderId)).Append("&");
        if (this.vcmsSettings.ContentClass != null)
        {
            urlBuilder.Append(System.Uri.EscapeDataString("contentClass") + "=").Append(System.Uri.EscapeDataString(this.vcmsSettings.ContentClass)).Append("&");
        }

        urlBuilder.Append(System.Uri.EscapeDataString("sendWorkflowMsg") + "=").Append(Uri.EscapeDataString(this.vcmsSettings.SendWorkflowMsg.ToString())).Append("&");
        urlBuilder.Length--;

        var vcmsADI = this.mapper.Map<ADI>(adi);
        var body = XmlHelper.SerializeXML(vcmsADI, Encoding.UTF8);

        EnsureHttpClient();
        using (var content = new StringContent(body, Encoding.UTF8, "application/xml"))
        {
            var uri = new Uri(urlBuilder.ToString());
            this.logger.LogInformation($"VCMS Endpoint: {uri}");
            var result = await this.httpClient.PostAsync(uri, content);

            var statusCode = (int)result.StatusCode;
            var resultBody = await result.Content.ReadAsStringAsync();
            if (statusCode >= 200 && statusCode < 300)
            {
                await this.vodAssetRegistryService.AddAssetEventAsync(adi.GetAdiAssetId(), AssetEventTypes.SuccessfullySentToVcms, DateTime.UtcNow, resultBody);
            }
            else
            {
                await this.vodAssetRegistryService.AddAssetEventAsync(adi.GetAdiAssetId(), AssetEventTypes.FailedToSendVcms, DateTime.UtcNow, resultBody);
            }
            result.EnsureSuccessStatusCode();
        }

        return true;
    }

    private void EnsureHttpClient()
    {
        if (this.httpClient is not null && this.httpClient.DefaultRequestHeaders.Authorization is null)
        {
            this.httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", Convert.ToBase64String(Encoding.ASCII.GetBytes($"{vcmsSettings.IngestContentUser}:{_vcmsIngestKey}")));
        }
    }
}
