using System;
using Newtonsoft.Json;

namespace Vod.Common.Data;

public class DelayedResource
{
    [JsonProperty("id")]
    public string Id { get; set; } = string.Empty;
    public string? AssetId { get; set; }
    public string? MissingFile { get; set; }
    public string? ContainerName { get; set; }
    public string? Payload { get; set; }
    public DateTime CreatedAtUtc { get; set; }
    public DateTime LastCheckedAtUtc { get; set; }
    public bool ThresholdAlertSent { get; set; }
}
