using System.Diagnostics.CodeAnalysis;
using NBA.NextGen.CMSPlatform.VODService.Infrastructure;
using Azure.Messaging.ServiceBus;
using Azure.Identity;
using Vod.Common.Services;
using NBA.NextGen.CMSPlatform.VODService.Application.Services;
using Azure.Storage.Blobs;
using Microsoft.Azure.Cosmos;
using NBA.NextGen.CMSPlatform.VODService.Infrastructure.Models;
using MST.Common.Azure.Extensions;
using MST.Common.Extensions;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using NBA.NextGen.CMSPlatform.VODService.Domain.FreeWheel;
using NBA.NextGen.CMSPlatform.VODService.Domain.Gam;
using NBA.NextGen.CMSPlatform.VODService.Domain.Utilities;
using NBA.NextGen.CMSPlatform.VODService.Domain.Common;
using Azure.Extensions.AspNetCore.Configuration.Secrets;
using Azure.Security.KeyVault.Secrets;
using MST.Common.Azure.Data;
using Vod.Common.Data;
using NBA.NextGen.CMSPlatform.VODService.Infrastructure.Models.Events;
using NBA.NextGen.CMSPlatform.VODService.Infrastructure.Services;
using MST.Common.AWS.Extensions;
using Amazon.SQS;
using MST.Common.MongoDB.Extensions;
using MongoDB.Driver;
using Amazon.S3;

namespace Vod.Common.Extensions;

[ExcludeFromCodeCoverage]
public static class ApplicationExtensions
{

    public static IConfigurationBuilder AddSharedConfiguration(this IConfigurationBuilder configuration)
    {
        // var config = configuration.Build();
        // var url = config.GetSection("KeyVault")["ConnectionString"] ?? throw new ArgumentNullException("Could not find KeyVault URL");
        // var client = new SecretClient(new Uri(url), new DefaultAzureCredential());
        // configuration.AddAzureKeyVault(client, new AzureKeyVaultConfigurationOptions());
        return configuration;
    }

    public static IServiceCollection AddSharedApplicationDependencies(this IServiceCollection services, IConfiguration configuration)
    {
        //TelemetryDebugWriter.IsTracingDisabled = true;

        services.AddInfrastructure(configuration);
        System.Text.Encoding.RegisterProvider(System.Text.CodePagesEncodingProvider.Instance);

        services.AddMSTInfrastructure();

        bool.TryParse(configuration.GetSection("VcmsSettings")["IgnoreCertificateValidation"], out var vcmsIgnoreSSL);

        services.AddHttpClient<IEcmsService, EcmsService>();
        services.AddHttpClient<IVcmsService, VcmsService>()
            .ConfigurePrimaryHttpMessageHandler(() => new HttpClientHandler
            {
                ServerCertificateCustomValidationCallback =
                vcmsIgnoreSSL
                    ? HttpClientHandler.DangerousAcceptAnyServerCertificateValidator
                    : null
            });

        services.AddSingleton<IVodFileRegistryService, VodFileRegistryService>();
        services.AddSingleton<IVodAssetRegistryService, VodAssetRegistryService>();

        services.AddDefaultAWSOptions(configuration.GetAWSOptions());
        services.AddAWSService<IAmazonSQS>();

        // Event Grid
        // Needs to stay evg for MCD!
        var eventGridUri = configuration.GetSection("NormalFlowSettings")["EventGridEndpoint"] ?? throw new NullReferenceException();
        var eventGridKey = configuration.GetValue<string>("event_grid_key") ?? throw new NullReferenceException();
        var egClient = new Azure.Messaging.EventGrid.EventGridPublisherClient(new Uri(eventGridUri), new Azure.AzureKeyCredential(eventGridKey));
        services.AddSingleton(egClient);
        services.RegisterEventGridSender<StartProcessing>("StartProcessing", a => a.Id, c => c.Content);
        services.RegisterEventGridSender<ECMSPostCreationSuccess>("ECMSPostCreationSuccess", a => a.Id, c => c.Content);
        services.RegisterEventGridSender<MediaKindProgress>("MediaKindProgress", a => a.Id, c => c.Content);
        services.RegisterEventGridSender<MediaKindProgressComplete>("MediaKindProgressComplete", a => a.Id, c => c.Content);
        services.RegisterEventGridSender<MediaKindProgressDelay>("MediaKindProgressDelay", a => a.Id, c => c.Content);
        // services.RegisterDummySender<StartProcessing>();
        // services.RegisterDummySender<ECMSPostCreationSuccess>();
        // services.RegisterDummySender<MediaKindProgress>();
        // services.RegisterDummySender<MediaKindProgressComplete>();
        // services.RegisterDummySender<MediaKindProgressDelay>();

        // SQS Senders
        var vcmsQueue = configuration.GetSection("AWSQueueNames")["SubmitToVcms"] ?? throw new NullReferenceException();
        var ecmsSubmitQueue = configuration.GetSection("AWSQueueNames")["SubmitToEcms"] ?? throw new NullReferenceException();
        var ecmsUpdateQueue = configuration.GetSection("AWSQueueNames")["UpdateEcms"] ?? throw new NullReferenceException();
        var assetCopyQueue = configuration.GetSection("AWSQueueNames")["AssetCopy"] ?? throw new NullReferenceException();

        services.RegisterSQSSender<AdiMessageWrapper>(vcmsQueue, _ => typeof(AdiMessageWrapper).Name);
        services.RegisterSQSSender<UpdateToEcmsWrapper>(ecmsUpdateQueue, _ => typeof(UpdateToEcmsWrapper).Name);
        services.RegisterSQSSender<SubmitToEcmsWrapper>(ecmsSubmitQueue, _ => typeof(SubmitToEcmsWrapper).Name);
        services.RegisterSQSSender<AssetCopyMessage>(assetCopyQueue, _ => typeof(AssetCopyMessage).Name);

        // S3 and Blob Storage
        var normalFlowContainerName = configuration.GetSection("NormalFlowSettings").GetValue<string>("ContainerName");
        var directFlowContainerName = configuration.GetSection("FileTransformationSettings").GetValue<string>("ContainerName");
        var live2vodContainerName = configuration.GetSection("LiveToVodSettings").GetValue<string>("ContainerName");
        var vcmsStorageContainerName = configuration.GetSection("VcmsStorageSettings").GetValue<string>("ContainerName");

        var transcriptionStorageContainerName = configuration.GetSection("TranscriptionEngine").GetValue<string>("WorkingBucket");

        services.AddAWSService<IAmazonS3>();

        services.RegisterS3DataContainer(normalFlowContainerName);
        services.RegisterS3DataContainer(directFlowContainerName);
        services.RegisterS3DataContainer(live2vodContainerName);
        services.RegisterS3DataContainer(vcmsStorageContainerName);
        services.RegisterS3DataContainer(transcriptionStorageContainerName);

        var mongoDbClient = new MongoClient(
            configuration["docdb_connection_string"] ??
            throw new InvalidOperationException("MongoDB connection string not found"));

        services.AddSingleton(mongoDbClient);

        var mongoDbName = configuration.GetSection("MongoSettings").GetValue<string>("DatabaseName") ?? throw new NullReferenceException();
        services.RegisterMongoDBRepository<AssetRegistryEntry>(mongoDbName, "assetregistry", x => x.Id);
        services.RegisterMongoDBRepository<AssetEventEntry>(mongoDbName, "assetevents", x => x.Id);
        services.RegisterMongoDBRepository<FileRegistryEntry>(mongoDbName, "fileregistry", x => x.Id);
        services.RegisterMongoDBRepository<GamRequestCache>(mongoDbName, "gamcache", x => x.Id);
        services.RegisterMongoDBRepository<AssetCacheItem>(mongoDbName, "bwassetcache", x => x.Id);
        services.RegisterMongoDBRepository<DelayedResource>(mongoDbName, "missingassetregistry", x => x.Id);
        services.RegisterMongoDBRepository<EcmsCacheRecord>(mongoDbName, "ecmscache", x => x.Id);
        services.RegisterMongoDBRepository<TranscribeJob>(mongoDbName, "transcribejobs", x => x.Id);
        services.RegisterMongoDBRepository<MediaConvertJob>(mongoDbName, "mediaconvertjobs", x => x.Id);

        // Cosmos 
        var cosmosConnectionString = configuration["cosmos_connection_string"] ??
            throw new InvalidOperationException("Cosmos connection string not found");
        var cosmosClient = new CosmosClient(cosmosConnectionString, new CosmosClientOptions
        {
            Serializer = new CosmosDataSerializer(),
            ConnectionMode = ConnectionMode.Gateway
        });
        services.AddSingleton(cosmosClient);

        var cosmosdDbName = configuration.GetSection("CosmosSettings")["DatabaseName"] ?? throw new NullReferenceException("Could not find Cosmos Database Name");
        services.RegisterCosmosContainer<RequestCache>(cosmosdDbName, "freewheelcache", x => x.Id);

        return services;
    }
}