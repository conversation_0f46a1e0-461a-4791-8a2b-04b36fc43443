using System;
using System.Text;
using Google.Api.Ads.AdManager.v202402;

namespace Vod.Common.Extensions;

public static class TimeSpanExentensions
{
    public static string ToExpandedFormatString(this TimeSpan ts)
    {
        var sb = new StringBuilder();
        if (ts.TotalHours >= 1)
        {
            sb.Append($"{ts:%h}h ");
        }
        if (ts.Minutes > 0)
        {
            sb.Append($"{ts:%m}m ");
        }
        if (ts.Seconds > 0)
        {
            sb.Append($"{ts:%s}s ");
        }
        if (ts.Milliseconds > 0)
        {
            sb.Append($"{ts:%fff}ms");
        }
        return sb.ToString().Trim();
    }
}
