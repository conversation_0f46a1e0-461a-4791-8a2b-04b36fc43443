using System;

namespace Vod.Common.Extensions;

public static class DoubleExtensions
{
    private static string[] suffixes = [" B", " KB", " MB", " GB", " TB", " PB"];

    public static string ToSizeString(this double number, int precision = 2)
    {
        const double unit = 1024;
        int i = 0;
        while (number > unit)
        {
            number /= unit;
            i++;
        }
        return Math.Round(number, precision) + suffixes[i];
    }
}
