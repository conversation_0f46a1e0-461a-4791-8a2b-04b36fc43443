using System;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Options;
using MST.Common.Data;
using NBA.NextGen.CMSPlatform.VODService.Domain.Config;

namespace Vod.Common.Health;

public class AzureBlobHealthCheck(IDataStoreFactory dataStoreFactory, IOptions<NormalFlowSettings> options) : IHealthCheck
{
    private const string KNOWN_FILE = "test/DO_NOT_DELETE.TXT";

    public async Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
    {
        var containerName = options.Value.CopyContainerName;
        var repository = dataStoreFactory.Resolve(containerName);
        var existsCheck = await repository.ExistsAsync(KNOWN_FILE);

        var status = existsCheck ? HealthStatus.Healthy : HealthStatus.Unhealthy;

        var data = new Dictionary<string, object>
        {
            { "Container", containerName },
            { "File", KNOWN_FILE }
        };

        return new HealthCheckResult(status, "Reports degraded status if known file does not exist!", null, data);
    }
}
