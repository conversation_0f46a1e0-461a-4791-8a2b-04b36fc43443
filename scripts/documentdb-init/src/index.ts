
import { MongoClient, Db } from "mongodb";
import * as fs from "fs";
import * as path from "path";
import * as https from "https";
import * as dotenv from "dotenv";
 
dotenv.config();
 
interface IndexConfig {
  key: Record<string, number>;
  unique?: boolean;
  expireAfterSeconds?: number;
}
 
interface CollectionConfig {
  name: string;
  indexes?: IndexConfig[];
}
 
interface DbConfig {
  collections: CollectionConfig[];
}
 
async function downloadCACertificate(): Promise<string> {
  const certPath = path.join(__dirname, "global-bundle.pem");
 
  if (fs.existsSync(certPath)) {
    console.log("CA certificate already exists, skipping download");
    return certPath;
  }
 
  console.log("Downloading Amazon DocumentDB CA certificate...");
 
  const file = fs.createWriteStream(certPath);
 
  return new Promise((resolve, reject) => {
    https
      .get(
        "https://truststore.pki.rds.amazonaws.com/global/global-bundle.pem",
        (response) => {
          response.pipe(file);
          file.on("finish", () => {
            file.close();
            console.log("CA certificate downloaded successfully");
            resolve(certPath);
          });
        }
      )
      .on("error", (err) => {
        fs.unlinkSync(certPath);
        reject(err);
      });
  });
}
 
async function connectToDocumentDB(): Promise<MongoClient> {
  if (!process.env.DOCUMENTDB_USERNAME || !process.env.DOCUMENTDB_PASSWORD || !process.env.DOCUMENTDB_ENDPOINT) {
    throw new Error(
      "Required environment variables DOCUMENTDB_USERNAME, DOCUMENTDB_PASSWORD, or DOCUMENTDB_ENDPOINT not set"
    );
  }

  const username = process.env.DOCUMENTDB_USERNAME;
  const password = encodeURIComponent(process.env.DOCUMENTDB_PASSWORD);
  const host = process.env.DOCUMENTDB_ENDPOINT;
 
  const caFilePath = await downloadCACertificate();
 
  const connectionString = `mongodb://${username}:${password}@${host}:27017/?ssl=true&replicaSet=rs0&readPreference=secondaryPreferred&retryWrites=false`;
 
  console.log(`Connecting to DocumentDB at ${host}...`);
 
  const client = new MongoClient(connectionString, {
    tls: true,
    tlsCAFile: caFilePath,
    connectTimeoutMS: 10000,
    serverSelectionTimeoutMS: 10000,
  });
 
  await client.connect();
  console.log("Successfully connected to DocumentDB");
 
  return client;
}
 
async function createCollectionIfNotExists(
  db: Db,
  collectionName: string
): Promise<void> {
  const collections = await db
    .listCollections({ name: collectionName })
    .toArray();
  if (collections.length === 0) {
    console.log(`Creating collection: ${collectionName}`);
    await db.createCollection(collectionName);
  } else {
    console.log(`Collection ${collectionName} already exists`);
  }
}
 
async function createOrUpdateIndex(
  collection: any,
  indexConfig: IndexConfig
): Promise<void> {
  const indexName = Object.entries(indexConfig.key)
    .map(([field, direction]) => `${field}_${direction}`)
    .join("_");
 
  const indexOptions: any = {
    name: indexName,
  };
 
  if (indexConfig.unique) {
    indexOptions.unique = true;
  }
 
  if (indexConfig.expireAfterSeconds !== undefined) {
    indexOptions.expireAfterSeconds = indexConfig.expireAfterSeconds;
  }
 
  console.log(
    `Creating/updating index ${indexName} on collection ${collection.collectionName}`
  );
  await collection.createIndex(indexConfig.key, indexOptions);
}
 
async function initializeCollections(): Promise<void> {
  let client: MongoClient | null = null;
 
  try {
    const configPath =
      process.env.CONFIG_PATH ||
      path.join(__dirname, "../collections-config.json");
    console.log(`Loading configuration from ${configPath}`);
 
    if (!fs.existsSync(configPath)) {
      throw new Error(`Configuration file not found at ${configPath}`);
    }
 
    const configContent = fs.readFileSync(configPath, "utf-8");
    const config: DbConfig = JSON.parse(configContent);
 
    const databaseName = process.env.DOCUMENTDB_DATABASE;
    if (!databaseName) {
      throw new Error(
        "Database name not specified in config or DOCUMENTDB_DATABASE environment variable"
      );
    }
 
    client = await connectToDocumentDB();
    const db = client.db(databaseName);
 
    console.log(
      `Initializing ${config.collections.length} collections in database ${databaseName}`
    );
 
    for (const collectionConfig of config.collections) {
      await createCollectionIfNotExists(db, collectionConfig.name);
 
      const collection = db.collection(collectionConfig.name);
 
      if (collectionConfig.indexes != null) {
        for (const indexConfig of collectionConfig.indexes) {
          await createOrUpdateIndex(collection, indexConfig);
        }
      }
 
      console.log(
        `Successfully configured collection: ${collectionConfig.name}`
      );
    }
 
    console.log("All collections initialized successfully!");
  } catch (error) {
    console.error("Error initializing collections:", error);
    process.exit(1);
  } finally {
    if (client) {
      await client.close();
      console.log("Closed connection to DocumentDB");
    }
  }
}
 
initializeCollections().catch((err) => {
  console.error("Fatal error:", err);
  process.exit(1);
});