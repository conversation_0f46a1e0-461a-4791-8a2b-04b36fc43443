// "//-----------------------------------------------------------------------".
// <copyright file="AdiMetadataResolver.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Application.Converters.FileTransformation
{
    using System.Collections.ObjectModel;
    using System.Diagnostics.CodeAnalysis;
    using System.Globalization;
    using AutoMapper;
    using NBA.NextGen.CMSPlatform.VODService.Domain.Common;
    using NBA.NextGen.CMSPlatform.VODService.Domain.Vcms;
    using Newtonsoft.Json.Linq;

    /// <summary>
    /// ADIMetadataResolver.
    /// </summary>
    public class AdiMetadataResolver : IValueResolver<JObject, ADI, ADIMetadata>
    {
        /// <summary>
        /// The Product.
        /// </summary>
        public const string Product = "SVOD";

        /// <summary>
        /// The Provider.
        /// </summary>
        public const string Provider = "NBA";

        /// <inheritdoc/>
        public ADIMetadata Resolve([NotNull] JObject source, ADI destination, ADIMetadata destMember, ResolutionContext context)
        {
            ADIMetadata adiMetadata = new ADIMetadata(this.GetADIAppData())
            {
                AMS = new ADIMetadataAMS
                {
                    AssetClass = "package",
                    AssetID = AdiAssetResolver.GetAssetId(source, "PACKAGE"),
                    ProviderID = Provider,
                    CreationDate = AdiAssetResolver.GetFormatDate(source),
                    Description = AdiAssetResolver.GetDescription(source),
                    VersionMajor = 1,
                    VersionMinor = 0,
                    AssetName = string.Format(CultureInfo.InvariantCulture, "{0} {1}", source[JsonKeys.Title], "PackageAsset"),
                    Product = Product,
                    Provider = Provider,
                },
            };

            return adiMetadata;
        }

        /// <summary>
        /// Gets the adi app data.
        /// </summary>
        /// <param name="source">The source.</param>
        /// <returns>Collection of ADIMetadataAppData.</returns>
        private Collection<ADIMetadataAppData> GetADIAppData()
        {
            return new Collection<ADIMetadataAppData>
                {
                    new ADIMetadataAppData
                    {
                        App = Product,
                        Name = "Metadata_Spec_Version",
                        Value = "CableLabsVOD1.1",
                    },
                };
        }
    }
}
