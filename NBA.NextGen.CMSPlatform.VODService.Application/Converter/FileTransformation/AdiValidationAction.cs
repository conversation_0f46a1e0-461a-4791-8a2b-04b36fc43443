// "//-----------------------------------------------------------------------".
// <copyright file="AdiValidationAction.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Application.Converters.FileTransformation
{
    using System;
    using System.Diagnostics.CodeAnalysis;
    using AutoMapper;
    using Microsoft.Extensions.Logging;
    using NBA.NextGen.CMSPlatform.VODService.Domain.Common;
    using NBA.NextGen.CMSPlatform.VODService.Domain.Vcms;
    using Newtonsoft.Json.Linq;

    /// <summary>
    /// Validation Action for ADI transformation.
    /// </summary>
    public class AdiValidationAction : IMappingAction<JObject, ADI>
    {
        /// <summary>
        /// Error message for File Transformation validation.
        /// </summary>
        private const string Message = "Unable to generate ADI Object due to missing fields";

        /// <inheritdoc/>
        public void Process([NotNull] JObject source, ADI destination, ResolutionContext context)
        {
            if (!(source.TryGetValue(JsonKeys.Id, StringComparison.OrdinalIgnoreCase, out JToken _) &&
                source.TryGetValue(JsonKeys.Title, StringComparison.OrdinalIgnoreCase, out JToken _) &&
                source.TryGetValue(JsonKeys.PublishDate, StringComparison.OrdinalIgnoreCase, out JToken _)))
            {
                throw new FormatException(Message);
            }
        }
    }
}
