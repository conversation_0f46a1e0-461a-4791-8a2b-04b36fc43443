// "//-----------------------------------------------------------------------".
// <copyright file="AdiAssetResolver.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Application.Converters.FileTransformation
{
    using System;
    using System.Collections.Generic;
    using System.Collections.ObjectModel;
    using System.Diagnostics.CodeAnalysis;
    using System.Globalization;
    using System.Linq;
    using System.Numerics;
    using AutoMapper;
    using NBA.NextGen.CMSPlatform.VODService.Domain.Common;
    using NBA.NextGen.CMSPlatform.VODService.Domain.Vcms;
    using Newtonsoft.Json.Linq;

    /// <summary>
    /// ADIAssetResolver.
    /// </summary>
    public class AdiAssetResolver : IValueResolver<JObject, ADI, ADIAsset>
    {
        /// <summary>
        /// The Product.
        /// </summary>
        public const string Product = "SVOD";

        /// <summary>
        /// The Provider.
        /// </summary>
        public const string Provider = "NBA";

        /// <summary>
        /// Get Date Section Only.
        /// </summary>
        /// <param name="source">The source.</param>
        /// <returns>DateTime.</returns>
        public static DateTime GetFormatDate([NotNull] JObject source)
        {
            string dateBase = source[JsonKeys.PublishDate].ToObject<string>().Substring(0, 10);
            DateTime dateTime;
            var isValid = DateTime.TryParseExact(dateBase, "MM/dd/yyyy", CultureInfo.InvariantCulture, DateTimeStyles.None, out dateTime);
            if (!isValid)
            {
                DateTime.TryParseExact(dateBase, "yyyy-MM-dd", CultureInfo.InvariantCulture, DateTimeStyles.None, out dateTime);
            }

            return dateTime.Date;
        }

        /// <summary>
        /// Get Date Section with pattern Only.
        /// </summary>
        /// <param name="source">The source.</param>
        /// <param name="pattern">The pattern.</param>
        /// <returns>DateTime.</returns>
        public static string GetFormatDateWithPattern([NotNull] JObject source, string pattern)
        {
            DateTime dateTime = GetFormatDate(source);
            return dateTime.ToString(pattern, DateTimeFormatInfo.InvariantInfo);
        }

        /// <summary>
        /// Get Date Section Only.
        /// </summary>
        /// <param name="source">The source.</param>
        /// <returns>DateTime.</returns>
        public static string GetProductionId([NotNull] JObject source)
        {
            if (source["mediaId"]?.ToString() != null)
            {
                string mediaId = source["mediaId"]?.ToString();
                string gameId = source["gameId"]?.ToString();
                JToken homeTeam = source["homeTeam"];
                JToken awayTeam = source["awayTeam"];
                string homeTeamName = homeTeam["teamName"].ToObject<string>();
                string awayTeamName = awayTeam["teamName"].ToObject<string>();
                string productionId = "g" + gameId + awayTeamName + mediaId + homeTeamName;
                return productionId.ToLower(CultureInfo.CurrentCulture);
            }

            return string.Empty;
        }

        /// <summary>
        /// Get Date Section Only.
        /// </summary>
        /// <param name="source">The source.</param>
        /// <param name="sufix">The sufix.</param>
        /// <returns>DateTime.</returns>
        public static string GetAssetId([NotNull] JObject source, string sufix)
        {
            if (source["mediaId"]?.ToString() != null)
            {
                string productionId = AdiAssetResolver.GetProductionId(source);
                return sufix == "PACKAGE" ? productionId + "V_CFG" : productionId + "V_CFG_" + sufix;
            }

            if (sufix == "PACKAGE")
            {
                return source[JsonKeys.Id] + string.Empty;
            }

            string assetId = source[JsonKeys.Id] + "_" + sufix;
            return assetId;
        }

        /// <summary>
        /// Get Title information Only.
        /// </summary>
        /// <param name="source">The source.</param>
        /// <returns>DateTime.</returns>
        public static string GetTitleBrief([NotNull] JObject source)
        {
            if (source["mediaId"]?.ToString() != null)
            {
                string role = source["role"]?.ToString();
                string titleBrief = source[JsonKeys.Title]?.ToString() + "-" + source["mediaName"]?.ToString();
                if (role != null)
                {
                    titleBrief = titleBrief + "-" + role;
                }

                return titleBrief;
            }

            string title = source[JsonKeys.Title].ToString();
            Dictionary<string, string> gameData = GetGameData(source);
            if (gameData.ContainsKey("Away_Team"))
            {
                string awayTeamName = gameData["Away_Team"];
                string homeTeamName = gameData["Home_Team"];
                if (title.Contains("_condensed_all_possessions", System.StringComparison.Ordinal))
                {
                    return "All Possessions: " + awayTeamName + " @ " + homeTeamName;
                }
                else if (title.Contains("_condensed_away_bestplays", System.StringComparison.OrdinalIgnoreCase))
                {
                    return awayTeamName + " Best Plays vs. " + homeTeamName;
                }
                else if (title.Contains("_condensed_home_bestplays", System.StringComparison.Ordinal))
                {
                    return homeTeamName + " Best Plays vs. " + awayTeamName;
                }
                else if (title.Contains("_condensed_home9min", System.StringComparison.Ordinal))
                {
                    return "Full Game Highlights: " + awayTeamName + " @ " + homeTeamName;
                }
            }

            return source[JsonKeys.Title]?.ToString();
        }

        /// <summary>
        /// Get Asset Name Section Only.
        /// </summary>
        /// <param name="source">The source.</param>
        /// <param name="sufix">The sufix.</param>
        /// <returns>string.</returns>
        public static string GetAssetName([NotNull] JObject source, string sufix)
        {
            string assetName = source[JsonKeys.Title] + " " + sufix;
            return assetName;
        }

        /// <summary>
        /// Get Asset Name Section Only.
        /// </summary>
        /// <param name="source">The source.</param>
        /// <param name="extension">The extension.</param>
        /// <returns>string.</returns>
        public static string GetContentName([NotNull] JObject source, string extension)
        {
            if (source["mediaId"]?.ToString() != null)
            {
                return source["fileName"] + "." + extension;
            }

            string assetName = source[JsonKeys.Title] + "." + extension;
            return assetName;
        }

        /// <summary>
        /// Get Description, WSC have specific handling.
        /// </summary>
        /// <param name="source">The source.</param>
        /// <returns>DateTime.</returns>
        public static string GetDescription([NotNull] JObject source)
        {
            string title = source[JsonKeys.Title].ToString();
            Dictionary<string, string> gameData = GetGameData(source);
            string datePattern = "yyyy-MM-dd";
            if (gameData.ContainsKey("Away_Team"))
            {
                string awayTeamName = gameData["Away_Team"];
                string homeTeamName = gameData["Home_Team"];
                if (title.Contains("_condensed_all_possessions", System.StringComparison.Ordinal))
                {
                    return "Watch highlights from the " + awayTeamName + " vs. " + homeTeamName + " from their matchup on " + GetFormatDateWithPattern(source, datePattern);
                }
                else if (title.Contains("_condensed_away_bestplays", System.StringComparison.OrdinalIgnoreCase))
                {
                    return "Watch the top plays from the " + homeTeamName + " vs the " + awayTeamName + " from their matchup on " + GetFormatDateWithPattern(source, datePattern);
                }
                else if (title.Contains("_condensed_home_bestplays", System.StringComparison.Ordinal))
                {
                    return "Watch the top plays from the " + homeTeamName + " vs the " + awayTeamName + " from their matchup on " + GetFormatDateWithPattern(source, datePattern);
                }
                else if (title.Contains("_condensed_home9min", System.StringComparison.Ordinal))
                {
                    return "Watch highlights from " + awayTeamName + " vs. " + homeTeamName + " from their matchup on " + GetFormatDateWithPattern(source, datePattern);
                }
            }

            string description = source[JsonKeys.Description]?.ToString();
            if (description == null)
            {
                description = source[JsonKeys.Title]?.ToString();
            }

            return description;
        }

        /// <summary>
        /// Get Description.
        /// </summary>
        /// <param name="source">The source.</param>
        /// <returns>DateTime.</returns>
        public static string GetSummary([NotNull] JObject source)
        {
            return GetDescription(source);
        }

        /// <summary>
        /// GetGameData.
        /// </summary>
        /// <param name="source">Json Object.</param>
        /// <returns>String.</returns>
        public static Dictionary<string, string> GetGameData(JObject source)
        {
            Dictionary<string, string> gameData = new Dictionary<string, string>();
            var events = source[JsonKeys.Events]?.AsJEnumerable();
            var title = source[JsonKeys.Title].ToObject<string>();
            AppendGameExtension(title, gameData, source);
            if (events != null)
            {
                var firstEvent = events.FirstOrDefault(e => e["game"] != null);

                if (firstEvent is null)
                {
                    return gameData;
                }

                JToken gameNode = firstEvent["game"];
                if (gameNode != null)
                {
                    JObject[] gameProviderList = gameNode["providerGameId"].ToObject<JObject[]>();
                    for (int i = 0; i < gameProviderList.Length; i++)
                    {
                        JObject oneItem = gameProviderList[i];
                        string provider = oneItem["provider"].ToObject<string>();
                        if (provider == "NBA")
                        {
                            string gameId = oneItem["id"].ToObject<string>();
                            gameData["Game_Id"] = gameId;
                            string seasonSection = gameId.Substring(2, 2);
                            int seasonInt = Convert.ToInt16(seasonSection, CultureInfo.InvariantCulture);
                            gameData["Season"] = "20" + (seasonInt - 1) + "-" + "20" + seasonInt;
                        }
                    }

                    JObject[] teamList = gameNode["teams"].ToObject<JObject[]>();
                    for (int i = 0; i < teamList.Length; i++)
                    {
                        JObject oneItem = teamList[i];

                        JObject[] providerTeamIdList = oneItem["providerTeamId"].ToObject<JObject[]>();
                        for (int j = 0; j < providerTeamIdList.Length; j++)
                        {
                            JObject oneTeam = providerTeamIdList[j];
                            string provider = oneTeam["provider"].ToObject<string>();
                            string teamId = oneTeam["id"].ToObject<string>();
                            if (provider == "NBA")
                            {
                                if (i == 0)
                                {
                                    gameData["Team_1"] = teamId;
                                }
                                else if (i == 1)
                                {
                                    gameData["Team_2"] = teamId;
                                }
                            }
                        }

                        string locale = oneItem["locale"].ToObject<string>();
                        string teamname = oneItem["name"].ToObject<string>();
                        if ("Visitor".Equals(locale, System.StringComparison.OrdinalIgnoreCase))
                        {
                            locale = "Away";
                        }

                        if (locale == "Away")
                        {
                            gameData["Away_Team"] = teamname;
                        }
                        else
                        {
                            gameData["Home_Team"] = teamname;
                        }

                        if (i == 0)
                        {
                            gameData["Team_1_Role"] = locale;
                        }
                        else if (i == 1)
                        {
                            gameData["Team_2_Role"] = locale;
                        }
                    }
                }
            }

            return gameData;
        }

        /// <summary>
        /// AppendGameExtension.
        /// </summary>
        /// <param name="title">title.</param>
        /// <param name="gameData">gameData.</param>
        /// <param name="source">source.</param>
        public static void AppendGameExtension(string title, Dictionary<string, string> gameData, JObject source)
        {
            string videoType = "Highlights";
            string videoCategory = "Condensed Games";
            string videoFranchise = string.Empty;
            string videoFranchiseName = string.Empty;

            if (title.Contains("_condensed_all_possessions", System.StringComparison.Ordinal))
            {
                videoFranchise = "All Possessions";
                videoFranchiseName = "All Possessions Condensed Game";
            }
            else if (title.Contains("_condensed_away_bestplays", System.StringComparison.OrdinalIgnoreCase))
            {
                videoFranchise = "Away Condensed Game";
                videoFranchiseName = "Away Best Plays";
            }
            else if (title.Contains("_condensed_home_bestplays", System.StringComparison.OrdinalIgnoreCase))
            {
                videoFranchise = "Home Condensed Game";
                videoFranchiseName = "Home Best Plays";
            }
            else if (title.Contains("_condensed_home9min", System.StringComparison.OrdinalIgnoreCase))
            {
                videoFranchise = "10 Minute Condensed";
                videoFranchiseName = "Full Game Highlights";
            }

            if (source["videoType"]?.ToString() != null)
            {
                videoType = source["videoType"]?.ToString();
            }

            if (source["videoCategory"]?.ToString() != null)
            {
                string category = source["videoCategory"].ToString();
                videoCategory = char.ToUpper(category[0], CultureInfo.CurrentCulture) + category.Substring(1);
            }

            if (source["videoFranchise"]?.ToString() != null)
            {
                videoFranchise = source["videoFranchise"]?.ToString();
            }

            if (source["videoFranchiseName"]?.ToString() != null)
            {
                videoFranchiseName = source["videoFranchiseName"]?.ToString();
            }

            gameData["Video_Type"] = videoType;
            gameData["Video_Category"] = videoCategory;
            gameData["Video_Franchise"] = videoFranchise;
            gameData["Video_Franchise_Name"] = videoFranchiseName;

            if (source["homeTeam"]?.ToString() != null && source["mediaName"]?.ToString() != null)
            {
                JToken homeTeam = source["homeTeam"];
                JToken awayTeam = source["awayTeam"];
                string homeTeamName = homeTeam["teamName"].ToObject<string>();
                string awayTeamName = awayTeam["teamName"].ToObject<string>();
                gameData["Away_Team"] = awayTeamName;
                gameData["Home_Team"] = homeTeamName;
                gameData["Team_1_Role"] = "Away";
                gameData["Team_2_Role"] = "Home";
                gameData["Team_1"] = awayTeam["teamId"].ToObject<string>();
                gameData["Team_2"] = homeTeam["teamId"].ToObject<string>();
                string productionId = GetProductionId(source);
                gameData["ProductionExternalId_PARAM_id"] = productionId;
                gameData["Game_Id"] = source["gameId"].ToString();
                gameData["NativeId_PARAM_id"] = source["gameId"].ToString();
                gameData["Event_Id"] = source["gameId"].ToString();
                gameData["Game_Start_Time"] = source["gameStartTime"].ToString();
                gameData["Sports_Season"] = source["year"].ToString();
                gameData["Production_Role"] = source["role"]?.ToString();
                gameData["Production_Name"] = source["mediaName"].ToString();
                gameData["Video_Camera_Alias"] = source["broadcastType"].ToString();
            }
        }

        /// <inheritdoc/>
        public ADIAsset Resolve([NotNull] JObject source, ADI destination, ADIAsset destMember, ResolutionContext context)
        {
            ADIAsset adiAsset = new ADIAsset(this.GetADIAssetAssetCollection(source))
            {
                Metadata = new ADIAssetMetadata(this.GetADIAssetMetadataAppDataCollection(source))
                {
                    AMS = this.GetADIAssetMetadataAMS(source),
                },
            };

            return adiAsset;
        }

        /// <summary>
        /// Gets the adi asset metadata ams.
        /// </summary>
        /// <param name="source">The source.</param>
        /// <returns>ADIAssetMetadataAMS.</returns>
        private ADIAssetMetadataAMS GetADIAssetMetadataAMS(JObject source)
        {
            return new ADIAssetMetadataAMS
            {
                AssetClass = "title",
                AssetID = AdiAssetResolver.GetAssetId(source, "title"),
                ProviderID = Provider,
                CreationDate = AdiAssetResolver.GetFormatDate(source),
                Description = AdiAssetResolver.GetDescription(source),
                VersionMajor = 1,
                VersionMinor = 0,
                AssetName = AdiAssetResolver.GetAssetName(source, "MetadataAsset"),
                Product = Product,
                Provider = Provider,
            };
        }

        /// <summary>
        /// Gets the adi asset metadata application data collection.
        /// </summary>
        /// <param name="source">The source.</param>
        /// <returns>Collection of ADIAssetMetadataAppData.</returns>
        private Collection<ADIAssetMetadataAppData> GetADIAssetMetadataAppDataCollection(JObject source)
        {
            TimeSpan time = TimeSpan.FromSeconds(Math.Round((double)source[JsonKeys.Duration] / 1000.0));
            string displayTime = time.ToString(@"hh\:mm\:ss", CultureInfo.InvariantCulture);
            Collection<ADIAssetMetadataAppData> appData = new Collection<ADIAssetMetadataAppData>
            {
                new ADIAssetMetadataAppData
                {
                    App = Product,
                    Name = "Type",
                    Value = source[JsonKeys.Title]?.ToString(),
                },
                new ADIAssetMetadataAppData
                {
                    App = Product,
                    Name = "Show_Type",
                    Value = "Series",
                },
                new ADIAssetMetadataAppData
                {
                    App = Product,
                    Name = "Title_Brief",
                    Value = AdiAssetResolver.GetTitleBrief(source),
                },
                new ADIAssetMetadataAppData
                {
                    App = Product,
                    Name = "Title",
                    Value = AdiAssetResolver.GetTitleBrief(source),
                },
                new ADIAssetMetadataAppData
                {
                    App = Product,
                    Name = "Advisories",
                    Value = string.Empty,
                },
                new ADIAssetMetadataAppData
                {
                    App = Product,
                    Name = "Summary_Short",
                    Value = AdiAssetResolver.GetSummary(source),
                },
                new ADIAssetMetadataAppData
                {
                    App = Product,
                    Name = "Summary_Medium",
                    Value = AdiAssetResolver.GetSummary(source),
                },
                new ADIAssetMetadataAppData
                {
                    App = Product,
                    Name = "Summary_Long",
                    Value = AdiAssetResolver.GetDescription(source),
                },
                new ADIAssetMetadataAppData
                {
                    App = Product,
                    Name = "Rating",
                    Value = string.Empty,
                },
                new ADIAssetMetadataAppData
                {
                    App = Product,
                    Name = "Entitlement",
                    Value = "league-pass",
                },
                new ADIAssetMetadataAppData
                {
                    App = Product,
                    Name = "Closed_Captioning",
                    Value = "Y",
                },
                new ADIAssetMetadataAppData
                {
                    App = Product,
                    Name = "Run_Time",
                    Value = displayTime,
                },
                new ADIAssetMetadataAppData
                {
                    App = Product,
                    Name = "Year",
                    Value = ((System.DateTime?)source[JsonKeys.PublishDate])?.Year.ToString(CultureInfo.InvariantCulture),
                },
                new ADIAssetMetadataAppData
                {
                    App = Product,
                    Name = "Country_of_Origin",
                    Value = "US",
                },
                new ADIAssetMetadataAppData
                {
                    App = Product,
                    Name = "Category",
                    Value = "Subscription/Sports/NBA On Demand",
                },
                new ADIAssetMetadataAppData
                {
                    App = Product,
                    Name = "Genre",
                    Value = "Series/All",
                },
                new ADIAssetMetadataAppData
                {
                    App = Product,
                    Name = "Licensing_Window_Start",
                    Value = string.Empty,
                },
                new ADIAssetMetadataAppData
                {
                    App = Product,
                    Name = "Licensing_Window_End",
                    Value = string.Empty,
                },
                new ADIAssetMetadataAppData
                {
                    App = Product,
                    Name = "Provider_QA_Contact",
                    Value = string.Empty,
                },
                new ADIAssetMetadataAppData
                {
                    App = Product,
                    Name = "Contract_Name",
                    Value = string.Empty,
                },
                new ADIAssetMetadataAppData
                {
                    App = Product,
                    Name = "Suggested_Price",
                    Value = string.Empty,
                },
                new ADIAssetMetadataAppData
                {
                    App = Product,
                    Name = "Maximum_Viewing_Length",
                    Value = string.Empty,
                },
                new ADIAssetMetadataAppData
                {
                    App = Product,
                    Name = "Billing_ID",
                    Value = string.Empty,
                },
                new ADIAssetMetadataAppData
                {
                    App = Product,
                    Name = "Actors",
                    Value = string.Empty,
                },
            };

            Dictionary<string, string> gameData = GetGameData(source);
            if (gameData.ContainsKey("Game_Id"))
            {
                foreach (KeyValuePair<string, string> entry in gameData)
                {
                    var newData = new ADIAssetMetadataAppData
                    {
                        App = Product,
                        Name = entry.Key,
                        Value = entry.Value,
                    };
                    appData.Add(newData);
                }
            }

            return appData;
        }

        /// <summary>
        /// Gets List of players.
        /// </summary>
        /// <param name="source">Json Object.</param>
        /// <returns>String.</returns>
        private string GetPlayersList(JObject source)
        {
            HashSet<string> names = new HashSet<string>();
            var events = source[JsonKeys.Events]?.AsJEnumerable();

            if (events != null)
            {
                foreach (JToken @event in events)
                {
                    var players = @event[JsonKeys.Players]?.AsJEnumerable();

                    if (players != null)
                    {
                        foreach (JToken player in players)
                        {
                            names.Add(player[JsonKeys.Name]?.ToString());
                        }
                    }
                }
            }

            return string.Join(",", names.Where(x => !string.IsNullOrWhiteSpace(x)));
        }

        /// <summary>
        /// Gets the adi asset asset collection.
        /// </summary>
        /// <param name="source">The source.</param>
        /// <returns>Collection of ADIAssetAsset.</returns>
        private Collection<ADIAssetAsset> GetADIAssetAssetCollection(JObject source)
        {
            Collection<ADIAssetAsset> adiAssetAsset = new Collection<ADIAssetAsset>
            {
                new ADIAssetAsset
                {
                    Metadata = new ADIAssetAssetMetadata(this.GetADIAssetAssetAppData())
                    {
                         AMS = new ADIAssetAssetMetadataAMS
                         {
                             AssetClass = "movie",
                             AssetID = AdiAssetResolver.GetAssetId(source, "movie"),
                             ProviderID = Provider,
                             CreationDate = AdiAssetResolver.GetFormatDate(source),
                             Description = AdiAssetResolver.GetDescription(source),
                             VersionMajor = 1,
                             VersionMinor = 0,
                             AssetName = AdiAssetResolver.GetAssetName(source, "MetadataMovieAsset"),
                             Product = Product,
                             Provider = Provider,
                         },
                    },
                    Content = new ADIAssetAssetContent
                    {
                        Value = AdiAssetResolver.GetContentName(source, "mp4"),
                    },
                },
            };
            if (source["mediaId"]?.ToString() == null)
            {
                ADIAssetAsset posterAsset = new ADIAssetAsset
                {
                    Metadata = new ADIAssetAssetMetadata()
                    {
                        AMS = new ADIAssetAssetMetadataAMS
                        {
                            AssetClass = "poster",
                            AssetID = AdiAssetResolver.GetAssetId(source, "poster"),
                            ProviderID = Provider,
                            CreationDate = AdiAssetResolver.GetFormatDate(source),
                            Description = AdiAssetResolver.GetDescription(source),
                            VersionMajor = 1,
                            VersionMinor = 0,
                            AssetName = AdiAssetResolver.GetAssetName(source, "MetadataPosterAsset"),
                            Product = Product,
                            Provider = Provider,
                        },
                    },
                    Content = new ADIAssetAssetContent
                    {
                        Value = AdiAssetResolver.GetContentName(source, "jpg"),
                    },
                };
                adiAssetAsset.Add(posterAsset);
            }

            return adiAssetAsset;
        }

        /// <summary>
        /// Gets the adi asset asset app data.
        /// </summary>
        /// <param name="source">The source.</param>
        /// <returns>Collection of ADIAssetAssetMetadataAppData.</returns>
        private Collection<ADIAssetAssetMetadataAppData> GetADIAssetAssetAppData()
        {
            return new Collection<ADIAssetAssetMetadataAppData>
                         {
                             new ADIAssetAssetMetadataAppData
                             {
                                 Value = "movie",
                                 Name = "Type",
                                 App = "SVOD",
                             },
                             new ADIAssetAssetMetadataAppData
                             {
                                 Value = "stereo",
                                 Name = "Audio_type",
                                 App = "SVOD",
                             },
                             new ADIAssetAssetMetadataAppData
                             {
                                 Value = "en",
                                 Name = "Languages",
                                 App = "SVOD",
                             },
                         };
        }
    }
}