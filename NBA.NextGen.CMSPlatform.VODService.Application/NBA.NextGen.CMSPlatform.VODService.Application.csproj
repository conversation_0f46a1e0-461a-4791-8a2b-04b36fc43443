<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <DebugType>pdbonly</DebugType>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <GenerateAssemblyTitleAttribute>true</GenerateAssemblyTitleAttribute>
  </PropertyGroup>
  
  <ItemGroup>
    <PackageReference Include="Azure.Storage.Blobs" Version="12.19.1" />
    <PackageReference Include="Google.Dfp" Version="24.29.0" />
    <PackageReference Include="Microsoft.Azure.Storage.Blob" Version="11.2.3" />
    <PackageReference Include="AutoMapper" Version="13.0.1" />
    <PackageReference Include="MST.Common" Version="2.3.5" />
    <PackageReference Include="AWSSDK.MediaConvert" Version="4.0.1" />
    <PackageReference Include="AWSSDK.TranscribeService" Version="4.0.0.2" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\NBA.NextGen.CMSPlatform.VODService.Domain\NBA.NextGen.CMSPlatform.VODService.Domain.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Common\" />
  </ItemGroup>

  <ItemGroup>
    <None Remove="Dispatcher\" />
  </ItemGroup>
</Project>
