// "//-----------------------------------------------------------------------".
// <copyright file="PrepareGamToVcmsCommand.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Application.UseCases.Gam
{

    /// <summary>
    /// PrepareGamToVcmsCommand.
    /// </summary>
    public class PrepareGamToVcmsCommand
    {
        /// <summary>
        /// Gets or sets the asset url.
        /// </summary>
        public string AssetLocation { get; set; }

        /// <summary>
        /// Gets or sets the title.
        /// </summary>
        public string Title { get; set; }

        /// <summary>
        /// Gets or sets the company id.
        /// </summary>
        public long CompanyId { get; set; }

        /// <summary>
        /// Gets or sets the click through url.
        /// </summary>
        public string ClickThroughDestination { get; set; }
    }
}