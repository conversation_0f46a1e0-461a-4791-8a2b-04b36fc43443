namespace NBA.NextGen.CMSPlatform.VODService.Application.UseCases.Gam;

using System;
using System.Net;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using MST.Common.Data;
using MST.Common.Messaging;
using NBA.NextGen.CMSPlatform.VODService.Application.Services;
using NBA.NextGen.CMSPlatform.VODService.Domain;
using NBA.NextGen.CMSPlatform.VODService.Domain.Common;
using NBA.NextGen.CMSPlatform.VODService.Domain.Config;
using NBA.NextGen.CMSPlatform.VODService.Domain.Gam;
using NBA.NextGen.CMSPlatform.VODService.Domain.Utilities;

public class PrepareGamToVcmsCommandHandler
{
    private readonly IObjectRepository<GamRequestCache> repository;
    private readonly ILogger logger;
    private readonly IMessageSender<AdiMessageWrapper> queueClient;
    private readonly IVodAssetRegistryService vodAssetRegistryService;

    public PrepareGamToVcmsCommandHandler(
        IObjectRepositoryFactory factory,
        IMessageSenderFactory queueClientProvider,
        ILogger<PrepareGamToVcmsCommandHandler> logger,
        IVodAssetRegistryService vodAssetRegistryService
    )
    {
        this.repository = factory.Resolve<GamRequestCache>();
        this.logger = logger;
        this.queueClient = queueClientProvider.Resolve<AdiMessageWrapper>();
        this.vodAssetRegistryService = vodAssetRegistryService;
    }

    /// <summary>
    /// Handles the incoming Prepare Gam to VCMS request.
    /// </summary>
    /// <param name="request">The request.</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>A Unit.</returns>
    public async Task Handle(PrepareGamToVcmsCommand request, CancellationToken cancellationToken)
    {
        var assetId = Guid.NewGuid().ToString();
        var gamId = $"GAM|{assetId}";

        await this.vodAssetRegistryService.CreateAssetEntryAsync(
            gamId,
            request.Title,
            Domain.Common.Enums.IngestionFlowTypes.Gam
        );
        await this.vodAssetRegistryService.AddAssetEventAsync(
            gamId,
            Domain.Common.Enums.AssetEventTypes.GamRequestReceived,
            DateTime.UtcNow
        );

        this.logger.LogInformation("Beginning PrepareGamToVcmsCommand...");
        try
        {
            var requestCache = new GamRequestCache()
            {
                Id = gamId,
                Title = request.Title,
                CompanyId = request.CompanyId,
                ClickThroughDestination = request.ClickThroughDestination,
                SourceFileLocation = request.AssetLocation,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.MinValue,
                VcmsResultMessage = string.Empty,
            };

            await repository.CreateItemAsync(requestCache);

            this.logger.LogInformation($"Committed Request Id {gamId} to Cosomos...");

            var adi = AdiFactory.CreateADI(assetId, request.AssetLocation, request.Title, gamId);

            this.logger.LogInformation("Generated ADI for VCMS");

            var msg = new AdiMessageWrapper() { Adi = adi };

            await this.queueClient.SendAsync(msg);
            string content = XmlHelper.SerializeXML(adi, Encoding.UTF8);
            await this.vodAssetRegistryService.AddAssetEventAsync(
                assetId,
                Domain.Common.Enums.AssetEventTypes.GamSentToVcms,
                DateTime.UtcNow,
                content
            );
            this.logger.LogInformation("GAM ADI delivered to SubmitToVCMS Queue");
        }
        catch (Exception e)
        {
            this.logger.LogError(e, e.Message);
            throw;
        }
    }
}
