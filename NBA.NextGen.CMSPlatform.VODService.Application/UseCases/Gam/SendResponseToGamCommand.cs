// "//-----------------------------------------------------------------------".
// <copyright file="SendResponseToGamCommand.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Application.UseCases.Gam
{
    using System;

    /// <summary>
    /// SendResponseToGamCommand.
    /// </summary>
    public class SendResponseToGamCommand
    {
        /// <summary>
        /// Gets or sets the Internal Id.
        /// </summary>
        public string InternalId { get; set; }

        /// <summary>
        /// Gets or sets the Output asset id.
        /// </summary>
        public string OutputAssetId { get; set; }

        /// <summary>
        /// Gets or sets the duration.
        /// </summary>
        public TimeSpan Duration { get; set; }

        /// <summary>
        /// Gets or sets a value that specifies the MediaKind ENV Key.
        /// </summary>
        public string MediaKindEnvKey { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether this request has failed.
        /// </summary>
        public bool IsError { get; set; }

        /// <summary>
        /// Gets or sets the error message.
        /// </summary>
        public string ErrorMessage { get; set; }
    }
}