// "//-----------------------------------------------------------------------".
// <copyright file="GetCompanyListQueryHandler.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Application.UseCases.Gam
{
    using System.Collections.Generic;
    using System.Diagnostics.CodeAnalysis;
    using System.Linq;
    using System.Threading;
    using System.Threading.Tasks;
    using Google.Api.Ads.AdManager.Lib;
    using Google.Api.Ads.AdManager.Util.v202408;
    using Google.Api.Ads.AdManager.v202408;
    using Google.Api.Ads.Common.Lib;
    using Microsoft.Extensions.Azure;
    using Microsoft.Extensions.Logging;
    using Microsoft.Extensions.Options;
    using NBA.NextGen.CMSPlatform.VODService.Domain.Config;
    using NBA.NextGen.CMSPlatform.VODService.Domain.Gam;

    /// <summary>
    /// GetCompanyListQueryHandler.
    /// </summary>
    public class GetCompanyListQueryHandler
    {
        /// <summary>
        /// The settings for gam.
        /// </summary>
        private readonly GamSettings settings;

        /// <summary>
        /// The logger.
        /// </summary>
        private readonly ILogger logger;

        /// <summary>
        /// Initializes a new instance of the <see cref="GetCompanyListQueryHandler"/> class.
        /// </summary>
        /// <param name="settings">The settings.</param>
        /// <param name="logger">The logger.</param>
        public GetCompanyListQueryHandler([NotNull] IOptions<GamSettings> settings, ILogger<GetCompanyListQueryHandler> logger)
        {
            this.settings = settings.Value;
            this.logger = logger;
        }

        /// <summary>
        /// Handles the request to get companies.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns>The list of company details.</returns>
        public async Task<List<CompanyDetails>> Handle(GetCompanyListQuery request, CancellationToken cancellationToken)
        {
            var user = this.GetUser();
            var list = new List<CompanyDetails>();

            using (CompanyService companyService = user.GetService<CompanyService>())
            {
                // Create a statement to select companies.
                int pageSize = StatementBuilder.SUGGESTED_PAGE_LIMIT;
                StatementBuilder statementBuilder =
                    new StatementBuilder().OrderBy("id ASC").Limit(pageSize);

                // Retrieve a small amount of companies at a time, paging through until all
                // companies have been retrieved.
                int totalResultSetSize = 0;
                do
                {
                    CompanyPage page =
                        await companyService.getCompaniesByStatementAsync(statementBuilder.ToStatement()).ConfigureAwait(false);

                    // Print out some information for each company.
                    if (page.results != null)
                    {
                        totalResultSetSize = page.totalResultSetSize;
                        var details = page.results.Select(c => new CompanyDetails()
                        {
                            Id = c.id,
                            Name = c.name,
                        });
                        list.AddRange(details);
                    }

                    statementBuilder.IncreaseOffsetBy(pageSize);
                }
                while (statementBuilder.GetOffset() < totalResultSetSize);
            }

            return list;
        }

        /// <summary>
        /// Gets the GAM User Object.
        /// </summary>
        /// <returns>The user.</returns>
        private AdManagerUser GetUser()
        {
            var config = new AdManagerAppConfig()
            {
                ApplicationName = this.settings.ApplicationName,
                NetworkCode = this.settings.NetworkCode,
                OAuth2Mode = OAuth2Flow.SERVICE_ACCOUNT,
                OAuth2SecretsJsonPath = this.settings.SecretsJsonPath,
            };

            return new AdManagerUser(config);
        }
    }
}