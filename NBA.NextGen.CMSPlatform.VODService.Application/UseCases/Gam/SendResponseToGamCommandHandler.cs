namespace NBA.NextGen.CMSPlatform.VODService.Application.UseCases.Gam;

using System;
using System.Diagnostics.CodeAnalysis;
using System.Threading;
using System.Threading.Tasks;
using Google.Api.Ads.AdManager.Lib;
using Google.Api.Ads.AdManager.v202408;
using Google.Api.Ads.Common.Lib;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using MST.Common.Data;
using NBA.NextGen.CMSPlatform.VODService.Application.Services;
using NBA.NextGen.CMSPlatform.VODService.Domain.Config;
using NBA.NextGen.CMSPlatform.VODService.Domain.Gam;

public class SendResponseToGamCommandHandler
{
    private readonly IObjectRepository<GamRequestCache> repository;
    private readonly ILogger logger;
    private readonly GamSettings settings;
    private readonly IVodAssetRegistryService vodAssetRegistryService;

    public SendResponseToGamCommandHandler(
        [NotNull] IOptions<GamSettings> settings,
        IObjectRepositoryFactory factory,
        ILogger<SendResponseToGamCommandHandler> logger,
        IVodAssetRegistryService vodAssetRegistryService
    )
    {
        this.settings = settings.Value;
        this.repository = factory.Resolve<GamRequestCache>();
        this.logger = logger;
        this.vodAssetRegistryService = vodAssetRegistryService;
    }

    public async Task<bool> Handle(
        SendResponseToGamCommand request,
        CancellationToken cancellationToken
    )
    {
        this.logger.LogInformation("Preparing to respond to Gam...");
        var assetId = request.InternalId.Substring(4);

        var requestCache = await repository.GetItemAsync(request.InternalId);
        this.logger.LogInformation($"Loaded record id {request.InternalId} from cosmos cache");

        var vcmsAssetEvent = Domain.Common.Enums.AssetEventTypes.GamVcmsResponseReceived;

        if (request.IsError)
        {
            vcmsAssetEvent = Domain.Common.Enums.AssetEventTypes.GamVcmsFailureResponseReceived;
            this.logger.LogError(
                $"Error From VCMS for {request.InternalId}, Message: {request.ErrorMessage}"
            );

            requestCache.UpdatedAt = System.DateTime.UtcNow;
            requestCache.VcmsResultMessage = request.ErrorMessage;
            await repository.UpdateItemAsync(requestCache);
        }

        await this.vodAssetRegistryService.AddAssetEventAsync(assetId, vcmsAssetEvent, System.DateTime.UtcNow);

        var duration = (int)request.Duration.TotalMilliseconds;

        var cmaf = request.MediaKindEnvKey switch
        {
            "proda" =>
                $"https://nbalpng.akamaized.net/vod/a/cmaf/{request.OutputAssetId}/index.m3u8",
            "prodb" =>
                $"https://nbablpng.akamaized.net/vod/a/cmaf/{request.OutputAssetId}/index.m3u8",
            _ => $"https://aks-dev-vod.aas.mediakind.com/cmaf/{request.OutputAssetId}/index.m3u8",
        };

        var videoCreative = new VideoRedirectCreative()
        {
            name = requestCache.Title,
            advertiserId = requestCache.CompanyId,
            destinationUrl = requestCache.ClickThroughDestination,
            destinationUrlType = string.IsNullOrWhiteSpace(requestCache.ClickThroughDestination)
                ? DestinationUrlType.NONE
                : DestinationUrlType.CLICK_TO_WEB,
            skippableAdType = SkippableAdType.DISABLED,
            size = new Size() { width = 1280, height = 720 },
            videoAssets = new VideoRedirectAsset[]
            {
                new VideoRedirectAsset
                {
                    redirectUrl = cmaf,
                    metadata = new VideoMetadata()
                    {
                        duration = duration,
                        size = new Size() { width = 1280, height = 720 },

                        scalableType = ScalableType.NOT_SCALABLE,
                        mimeType = MimeType.M3U8,
                        minimumBitRate = 150,
                        maximumBitRate = 8000,
                        deliveryType = VideoDeliveryType.STREAMING,
                    },
                },
            },
            duration = duration,
        };

        var user = this.GetUser();

        using var creativeService = user.GetService<CreativeService>();
        using var creativeSetService = user.GetService<CreativeSetService>();
        try
        {
            // Create the video creative on the server.
            Creative[] videoCreatives = await creativeService
                .createCreativesAsync(new Creative[] { videoCreative })
                .ConfigureAwait(false);

            if (videoCreatives != null)
            {
                foreach (Creative creative in videoCreatives)
                {
                    this.logger.LogInformation(
                        $"GAM Successfully Created New Creative Id: {creative.id} Name:{creative.name}!"
                    );
                    var creativeSet = new CreativeSet
                    {
                        masterCreativeId = creative.id,
                        name = creative.name,
                    };
                    var result = await creativeSetService
                        .createCreativeSetAsync(creativeSet)
                        .ConfigureAwait(false);
                    if (result.id != 0)
                    {
                        await this.vodAssetRegistryService.AddAssetEventAsync(
                            assetId,
                            Domain.Common.Enums.AssetEventTypes.GamResponseSentSuccessfully,
                            System.DateTime.UtcNow
                        );
                        this.logger.LogInformation(
                            $"GAM Successfully Created New Creative Set: {result.id}"
                        );
                    }
                    else
                    {
                        await this.vodAssetRegistryService.AddAssetEventAsync(
                            assetId,
                            Domain.Common.Enums.AssetEventTypes.GamResponseFailedToSend,
                            System.DateTime.UtcNow
                        );
                        this.logger.LogInformation($"GAM Failed to Create A New Creative Set!");
                    }
                }
            }
            else
            {
                this.logger.LogWarning("GAM Returned No New Creatives!");
            }

            await repository.DeleteItemAsync(requestCache.Id);
        }
        catch (Exception e)
        {
            await this.vodAssetRegistryService.AddAssetEventAsync(
                assetId,
                Domain.Common.Enums.AssetEventTypes.GamResponseFailedToSend,
                System.DateTime.UtcNow
            );
            if (e is AdManagerApiException sdkException)
            {
                requestCache.UpdatedAt = System.DateTime.UtcNow;
                requestCache.GamResultMessage =
                    "GAM Exception: " + sdkException.ApiException.ToString();
                await repository.UpdateItemAsync(requestCache);
                this.logger.LogError(
                    e,
                    "Call to GAM failed when trying to create new creatives... API Exception:"
                        + sdkException.ApiException
                );
            }

            this.logger.LogError(e, "Call to GAM failed when trying to create new creatives");
            return false;
        }
        return true;
    }

    private AdManagerUser GetUser()
    {
        var config = new AdManagerAppConfig()
        {
            ApplicationName = this.settings.ApplicationName,
            NetworkCode = this.settings.NetworkCode,
            OAuth2Mode = OAuth2Flow.SERVICE_ACCOUNT,
            OAuth2SecretsJsonPath = this.settings.SecretsJsonPath,
        };

        return new AdManagerUser(config);
    }
}
