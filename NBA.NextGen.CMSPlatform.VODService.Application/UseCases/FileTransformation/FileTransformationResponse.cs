// "//-----------------------------------------------------------------------".
// <copyright file="FileTransformationResponse.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Application.UseCases.FileTransformation
{
    using NBA.NextGen.CMSPlatform.VODService.Domain.Vcms;

    /// <summary>
    /// The File Transformation response.
    /// </summary>
    public class FileTransformationResponse
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="FileTransformationResponse"/> class.
        /// </summary>
        /// <param name="adi">The ADI object.</param>
        public FileTransformationResponse(ADI adi)
        {
            this.Adi = adi;
        }

        /// <summary>
        /// Gets or sets the Adi object.
        /// </summary>
        public ADI Adi { get; set; }
    }
}
