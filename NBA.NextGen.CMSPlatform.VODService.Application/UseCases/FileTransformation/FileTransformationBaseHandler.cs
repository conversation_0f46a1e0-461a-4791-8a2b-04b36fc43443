using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using NBA.NextGen.CMSPlatform.VODService.Application.Dispatcher.ContentHandler;
using NBA.NextGen.CMSPlatform.VODService.Application.Services;
using NBA.NextGen.CMSPlatform.VODService.Domain.Common;
using NBA.NextGen.CMSPlatform.VODService.Domain.Config;
using NBA.NextGen.CMSPlatform.VODService.Domain.Utilities;
using NBA.NextGen.CMSPlatform.VODService.Domain.Vcms;

namespace NBA.NextGen.CMSPlatform.VODService.Application.UseCases.FileTransformation;


public class FileTransformationBaseHandler
{
    public FileTransformationBaseHandler([NotNull] IOptions<FileTransformationSettings> blobSettings, IFileTransformationBlobService blobHandlerService)
    {
        this.BlobSettings = blobSettings.Value;
        this.BlobHandlerService = blobHandlerService;
    }

    protected FileTransformationSettings BlobSettings { get; private set; }

    protected IFileTransformationBlobService BlobHandlerService { get; private set; }

    protected void Transform([NotNull] ADI adi, [NotNull] string fileName, [NotNull] string fileType, [NotNull] IFileTransformationBlobService blobHandlerService)
    {
        if (!fileType.Equals(FileTypeConstants.InhouseXML, StringComparison.OrdinalIgnoreCase) && !fileType.Equals(FileTypeConstants.WSC, StringComparison.OrdinalIgnoreCase))
        {
            adi.Asset.Asset.Remove(adi.Asset.Asset.First(y => y.Metadata.AMS.AssetClass.Equals(AssetClassConstants.Movie, StringComparison.OrdinalIgnoreCase)));
        }

        this.UpdateFilePaths(adi, fileName, blobHandlerService);

        if (fileType.Equals(FileTypeConstants.InhouseXML, StringComparison.OrdinalIgnoreCase))
        {
            this.UpdateUniqueAssetIds(adi);
        }
    }

    protected void UpdateFilePaths([NotNull] ADI adi, [NotNull] string fileName, [NotNull] IFileTransformationBlobService blobHandlerService)
    {
        List<string> filenames = adi.Asset.Asset.Select(selector: x => x.Content.Value).ToList();
        filenames.Remove(fileName.Split('.', StringSplitOptions.None).First() + ".ts");
        foreach (string filename in filenames)
        {
            string filePath = fileName.GenerateFilePath(filename);
            ADIAssetAsset adiasset = adi.Asset.Asset.Select(x => x).First(y => y.Content.Value.Equals(filename, StringComparison.OrdinalIgnoreCase));
            adiasset.Content.Value = blobHandlerService.GetBlobLocation(filePath);
        }
    }

    protected async Task<bool> CheckIfAllFilesExistsAsync(ADI adi, [NotNull] string fileName, [NotNull] IFileTransformationBlobService blobHandlerService, ILogger logger)
    {
        bool status = false;
        if (adi != null)
        {
            List<string> filenames = adi.Asset.Asset.Select(selector: x => x.Content.Value).ToList();
            filenames.Remove(fileName.Split('.', StringSplitOptions.None).First() + ".ts");
            foreach (string filename in filenames)
            {
                if (filename.EndsWith(".jpg", System.StringComparison.OrdinalIgnoreCase))
                {
                    continue;
                }

                bool isValidUri = this.IsValidUri(filename);
                if (!isValidUri)
                {
                    string filePath = fileName.GenerateFilePath(filename);
                    if (!await blobHandlerService.BlobExistsAsync(filePath).ConfigureAwait(false))
                    {
                        status = false;
                        logger.LogError($"File {filename} does not exist in the container.");
                        throw new MissingResourceException(this.BlobSettings.ContainerName, filePath);
                    }
                    else
                    {
                        status = true;
                        logger.LogInformation($"File {filename} found in the container.");
                    }
                }
                else
                {
                    status = true;
                }
            }
        }

        return status;
    }

    private bool IsValidUri(string filePath)
    {
        return Uri.TryCreate(filePath, UriKind.Absolute, out _);
    }

    private void UpdateUniqueAssetIds(ADI ecmsAdi)
    {
        foreach (var obj in ecmsAdi.Asset.Asset.Where(y => (y.Metadata.AMS.AssetClass == AssetClassConstants.Mezzanine || y.Metadata.AMS.AssetClass == AssetClassConstants.Captioning)).ToList())
        {
            obj.Metadata.AMS.AssetClass = AssetClassConstants.Movie;
        }

        var assetAssetId = ecmsAdi.Asset.Metadata.AMS.AssetID;
        var adiAssetId = ecmsAdi.Metadata.AMS.AssetID;
        List<int> fistDigits = new List<int>();
        List<string> assetidlist = ecmsAdi.Asset.Asset.Select(x => x.Metadata.AMS.AssetID).ToList();
        assetidlist.Add(assetAssetId);
        assetidlist.Add(adiAssetId);

        foreach (var assetid in assetidlist)
        {
            var regexMatch = Regex.Match(assetid, "\\d");
            fistDigits.Add((int)char.GetNumericValue(assetid[regexMatch.Index]));
        }

        var counter = fistDigits.Max() + 1;

        foreach (var obj in ecmsAdi.Asset.Asset)
        {
            if (obj.Metadata.AMS.AssetID == adiAssetId)
            {
                obj.Metadata.AMS.AssetID = this.GetNewAssetId(obj.Metadata.AMS.AssetID, counter);
                counter++;
            }

            if (obj.Metadata.AMS.AssetID == assetAssetId)
            {
                obj.Metadata.AMS.AssetID = this.GetNewAssetId(obj.Metadata.AMS.AssetID, counter);
                counter++;
            }

            if (ecmsAdi.Asset.Asset.Where(x => x.Metadata.AMS.AssetID == obj.Metadata.AMS.AssetID).ToList().Count > 1)
            {
                foreach (ADIAssetAsset assetObj in ecmsAdi.Asset.Asset.Where(x => x.Metadata.AMS.AssetID == obj.Metadata.AMS.AssetID).ToList())
                {
                    assetObj.Metadata.AMS.AssetID = this.GetNewAssetId(obj.Metadata.AMS.AssetID, counter);
                    counter++;
                }
            }
        }
    }

    private string GetNewAssetId(string assetId, int counter)
    {
        var regexMatch = Regex.Match(assetId, "\\d");

        if (regexMatch.Success)
        {
            var alphabet = assetId.Substring(0, regexMatch.Index);
            var digit = assetId.Substring(regexMatch.Index + 1);
            assetId = alphabet + counter + digit;
        }

        return assetId;
    }
}
