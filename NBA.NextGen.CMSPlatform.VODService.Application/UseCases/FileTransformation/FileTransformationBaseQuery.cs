// "//-----------------------------------------------------------------------".
// <copyright file="FileTransformationBaseQuery.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Application.UseCases.FileTransformation
{
    using System.Diagnostics.CodeAnalysis;

    /// <summary>
    /// FileTransformationBaseQuery.
    /// </summary>
    [ExcludeFromCodeCoverage]
    public class FileTransformationBaseQuery
    {
        /// <summary>
        /// Gets or sets the Name of the file to be uploaded.
        /// </summary>
        public string FileName { get; set; }
    }
}
