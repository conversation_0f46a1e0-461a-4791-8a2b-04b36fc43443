// "//-----------------------------------------------------------------------".
// <copyright file="WscJsonFileTransformationQuery.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Application.UseCases.FileTransformation.WscJson.Queries
{
    using System.Diagnostics.CodeAnalysis;

    /// <summary>
    /// WscJsonFileTransformationQuery.
    /// </summary>
    [ExcludeFromCodeCoverage]
    public class WscJsonFileTransformationQuery : FileTransformationBaseQuery
    {
    }
}
