using System;
using System.Diagnostics.CodeAnalysis;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using NBA.NextGen.CMSPlatform.VODService.Application.Services;
using NBA.NextGen.CMSPlatform.VODService.Domain.Common;
using NBA.NextGen.CMSPlatform.VODService.Domain.Config;
using NBA.NextGen.CMSPlatform.VODService.Domain.Vcms;

namespace NBA.NextGen.CMSPlatform.VODService.Application.UseCases.FileTransformation.WscJson.Queries;


public class WscJsonFileTransformationQueryHandler : FileTransformationBaseHandler
{
    private readonly ILogger logger;

    private readonly Action<string> setResourceId;

    public WscJsonFileTransformationQueryHandler([NotNull] IOptions<FileTransformationSettings> blobSettings, IFileTransformationBlobService blobHandlerService, ILogger logger, Action<string> setResourceId)
                : base(blobSettings, blobHandlerService)
    {
        this.logger = logger;
        this.setResourceId = setResourceId;
    }

    public async Task<FileTransformationResponse> Handle([NotNull] WscJsonFileTransformationQuery request, CancellationToken cancellationToken)
    {
        string filePath = request.FileName.Replace(".json", string.Empty, System.StringComparison.InvariantCultureIgnoreCase);
        this.logger.LogInformation("processing file name with path is " + filePath);

        // get Cable Labs format object
        ADI adi = await this.GetADIObjectAsync(filePath).ConfigureAwait(false);

        this.setResourceId(adi.GetAdiAssetId());

        await this.CheckIfAllFilesExistsAsync(adi, filePath, this.BlobHandlerService, this.logger);

        // apply transforms to Cable Labs format object
        this.Transform(adi, $"{filePath}.json", FileTypeConstants.WSC, this.BlobHandlerService);

        return new FileTransformationResponse(adi);
    }

    /// <summary>
    /// Gets the ADI Object.
    /// </summary>
    /// <param name="fileName">The File name.</param>
    /// <returns>ADI.</returns>
    private async Task<ADI> GetADIObjectAsync(string fileName)
    {
        ADI adi = null;
        adi = await this.BlobHandlerService.ReadJsonAsync(fileName, this.logger).ConfigureAwait(false);
        return adi;
    }
}
