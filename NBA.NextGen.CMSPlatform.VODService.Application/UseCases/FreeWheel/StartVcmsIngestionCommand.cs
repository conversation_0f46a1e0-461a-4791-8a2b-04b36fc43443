// "//-----------------------------------------------------------------------".
// <copyright file="StartVcmsIngestionCommand.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Application.UseCases.FreeWheel
{
    using System;
    using System.Collections.Generic;
    using System.Text;

    /// <summary>
    /// StartVcmsIngestionCommandHandler.
    /// </summary>
    public class StartVcmsIngestionCommand
    {
        /// <summary>
        /// Gets or sets the id of the request.
        /// </summary>
        public string InternalId { get; set; }

        /// <summary>
        /// Gets or sets the TranscodeRequest.
        /// </summary>
        public Domain.FreeWheel.Request.Transcode TranscodeRequest { get; set; }
    }
}
