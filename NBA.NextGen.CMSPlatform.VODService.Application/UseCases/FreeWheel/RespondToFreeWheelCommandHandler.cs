namespace NBA.NextGen.CMSPlatform.VODService.Application.UseCases.FreeWheel;

using System;
using System.Diagnostics.CodeAnalysis;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using MST.Common.Data;
using NBA.NextGen.CMSPlatform.VODService.Application.Services;
using NBA.NextGen.CMSPlatform.VODService.Domain.FreeWheel;
using NBA.NextGen.CMSPlatform.VODService.Domain.FreeWheel.Response;

public class RespondToFreeWheelCommandHandler
{
    private readonly IObjectRepository<RequestCache> repository;
    private readonly IFreeWheelRestClientService freeWheelRestClientService;
    private readonly ILogger logger;
    private readonly IVodAssetRegistryService vodAssetRegistryService;

    public RespondToFreeWheelCommandHandler(
        IFreeWheelRestClientService freeWheelRestClientService,
        IObjectRepositoryFactory factory,
        ILogger<RespondToFreeWheelCommandHandler> logger,
        IVodAssetRegistryService vodAssetRegistryService
    )
    {
        this.freeWheelRestClientService = freeWheelRestClientService;
        this.repository = factory.Resolve<RequestCache>();
        this.logger = logger;
        this.vodAssetRegistryService = vodAssetRegistryService;
    }

    public async Task<bool> Handle(
        [NotNull] RespondToFreeWheelCommand request,
        CancellationToken cancellationToken
    )
    {
        this.logger.LogInformation("Preparing to respond to Freewheel...");

        var requestCache = await repository.GetItemAsync(request.InternalId);
        this.logger.LogInformation($"Loaded record id {request.InternalId} from cosmos cache");

        var assetId = $"{requestCache.NetworkId}.{requestCache.SourceCreativeRenditionId}";

        var vcmsAssetEvent = request.IsError
            ? Domain.Common.Enums.AssetEventTypes.FreeWheelVcmsFailureResponseReceived 
            : Domain.Common.Enums.AssetEventTypes.FreeWheelVcmsResponseReceived;
        
        await this.vodAssetRegistryService.AddAssetEventAsync(assetId, vcmsAssetEvent, DateTime.UtcNow, request.ErrorMessage);

        var response = new Transcode()
        {
            Status = request.IsError ? "fail" : "success",
            Message = request.IsError ? request.ErrorMessage : "Transcoding Pass",
            SessionId = requestCache.SessionId,
        };

        response.Creative = new Creative()
        {
            Name = requestCache.Name,
            NetworkId = requestCache.NetworkId,
        };

        response.Creative.SourceCreativeRendition = new SourceCreativeRendition
        {
            Id = requestCache.SourceCreativeRenditionId,
        };

        var cmaf = string.Empty;
        var hlsts = string.Empty;

        switch (request.MediaKindEnvKey)
        {
            case "proda":
                cmaf =
                    $"https://nbalpng.akamaized.net/vod/a/cmaf/{request.OutputAssetId}/index.m3u8";
                hlsts =
                    $"https://nbalpng.akamaized.net/vod/a/hls-ts/{request.OutputAssetId}/index.m3u8";
                break;
            case "prodb":
                cmaf =
                    $"https://nbablpng.akamaized.net/vod/a/cmaf/{request.OutputAssetId}/index.m3u8";
                hlsts =
                    $"https://nbablpng.akamaized.net/vod/a/hls-ts/{request.OutputAssetId}/index.m3u8";
                break;
            default:
                hlsts =
                    $"https://aks-dev-vod.aas.mediakind.com/hls-ts/{request.OutputAssetId}/index.m3u8";
                cmaf =
                    $"https://aks-dev-vod.aas.mediakind.com/cmaf/{request.OutputAssetId}/index.m3u8";
                break;
        }

        response.Creative.CreativeRendition.Add(
            new CreativeRendition()
            {
                ContentType = "video/wm-nba-hls",
                Location = hlsts,
                Duration = request.Duration.TotalSeconds,
                DurationType = "GUARANTEED",
                Mediainfo = new Mediainfo()
                {
                    Width = 1920,
                    Height = 1080,
                    Bitrate = "10000",
                    Fps = "29.97",
                },
            }
        );

        response.Creative.CreativeRendition.Add(
            new CreativeRendition()
            {
                ContentType = "video/wm-nba-hls-cmaf",
                Location = cmaf,
                Duration = request.Duration.TotalSeconds,
                DurationType = "GUARANTEED",
                Mediainfo = new Mediainfo()
                {
                    Width = 1920,
                    Height = 1080,
                    Bitrate = "10000",
                    Fps = "29.97",
                },
            }
        );


        this.logger.LogInformation("Preparing to call Freewheel...");

        var result = await this.freeWheelRestClientService.SendResponseToFreeWheelAsync(
            response,
            this.logger,
            cancellationToken
        );

        this.logger.LogInformation("Completed Respond to Freewheel Callback...");

        return result;
        ;
    }
}
