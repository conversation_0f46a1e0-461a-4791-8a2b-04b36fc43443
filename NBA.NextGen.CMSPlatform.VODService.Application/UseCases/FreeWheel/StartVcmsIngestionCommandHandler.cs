namespace NBA.NextGen.CMSPlatform.VODService.Application.UseCases.FreeWheel;

using System;
using System.Diagnostics.CodeAnalysis;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using MST.Common.Data;
using MST.Common.Messaging;
using NBA.NextGen.CMSPlatform.VODService.Application.Services;
using NBA.NextGen.CMSPlatform.VODService.Domain;
using NBA.NextGen.CMSPlatform.VODService.Domain.Common;
using NBA.NextGen.CMSPlatform.VODService.Domain.FreeWheel;
using NBA.NextGen.CMSPlatform.VODService.Domain.Utilities;

public class StartVcmsIngestionCommandHandler
{
    private readonly IObjectRepository<RequestCache> repository;
    private readonly ILogger logger;

    private readonly IMessageSender<AdiMessageWrapper> queueClient;
    private readonly IVodAssetRegistryService vodAssetRegistryService;

    public StartVcmsIngestionCommandHandler(
        IMessageSenderFactory queueClientProvider,
        IObjectRepositoryFactory factory,
        ILogger<StartVcmsIngestionCommandHandler> logger,
        IVodAssetRegistryService vodAssetRegistryService
    )
    {
        this.queueClient = queueClientProvider.Resolve<AdiMessageWrapper>();
        this.repository = factory.Resolve<RequestCache>();
        this.logger = logger;
        this.vodAssetRegistryService = vodAssetRegistryService;
    }

    public async Task Handle(
        [NotNull] StartVcmsIngestionCommand request,
        CancellationToken cancellationToken
    )
    {
        this.logger.LogInformation("Beginning StartVcmsIngestionCommand...");

        var assetId =
            $"{request.TranscodeRequest.Creative.NetworkId}.{request.TranscodeRequest.Creative.SourceCreativeRendition.Id}";

        await this.vodAssetRegistryService.CreateAssetEntryAsync(
            assetId,
            request.TranscodeRequest.Creative.Name,
            Domain.Common.Enums.IngestionFlowTypes.FreeWheel
        );
        await this.vodAssetRegistryService.AddAssetEventAsync(
            assetId,
            Domain.Common.Enums.AssetEventTypes.FreeWheelRequestReceived,
            DateTime.UtcNow
        );
        try
        {
            var requestCache = new RequestCache()
            {
                Id = request.InternalId,
                SessionId = request.TranscodeRequest.SessionId,
                Name = request.TranscodeRequest.Creative.Name,
                NetworkId = request.TranscodeRequest.Creative.NetworkId,
                SourceCreativeRenditionId = request
                    .TranscodeRequest
                    .Creative
                    .SourceCreativeRendition
                    .Id,
                Id3Key = request.TranscodeRequest.Creative.CreativeRendition.Id3.Key,
                Id3Value = request.TranscodeRequest.Creative.CreativeRendition.Id3.Value,
            };

            await this.repository.CreateItemAsync(requestCache).ConfigureAwait(false);

            this.logger.LogInformation($"Committed Request Id {request.InternalId} to Cosomos...");

            var adi = AdiFactory.CreateADI(
                assetId,
                request.TranscodeRequest.Creative.SourceCreativeRendition.Location,
                request.TranscodeRequest.Creative.Name,
                request.InternalId
            );

            this.logger.LogInformation("Generated ADI for VCMS");

            var msg = new AdiMessageWrapper() { Adi = adi };

            await this.queueClient.SendAsync(msg);
            string content = XmlHelper.SerializeXML(adi, Encoding.UTF8);
            await this.vodAssetRegistryService.AddAssetEventAsync(
                assetId,
                Domain.Common.Enums.AssetEventTypes.FreeWheelSentToVcms,
                DateTime.UtcNow,
                content
            );
            this.logger.LogInformation("Freewheel ADI delivered to SubmitToVCMS Queue");
        }
        catch (Exception e)
        {
            this.logger.LogError(e, e.Message);
            throw;
        }
    }
}
