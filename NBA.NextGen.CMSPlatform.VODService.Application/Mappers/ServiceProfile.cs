namespace NBA.NextGen.CMSPlatform.VODService.Application.Mappers;

using AutoMapper;
using NBA.NextGen.CMSPlatform.VODService.Application.Converters.FileTransformation;
using NBA.NextGen.CMSPlatform.VODService.Domain.Vcms;
using Newtonsoft.Json.Linq;

public class ServiceProfile : Profile
{
    public ServiceProfile()
    {
        this.CreateMap<JObject, ADI>()
           .ForMember(x => x.Metadata, y => y.MapFrom(new AdiMetadataResolver()))
           .ForMember(x => x.Asset, y => y.MapFrom(new AdiAssetResolver()))
           .BeforeMap<AdiValidationAction>();
    }
}
