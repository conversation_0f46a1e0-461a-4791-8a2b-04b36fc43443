namespace NBA.NextGen.CMSPlatform.VODService.Application.Services;

/// <summary>
/// Service for resolving ECMS API endpoints based on league information
/// </summary>
public interface IEcmsEndpointResolver
{
    /// <summary>
    /// Resolves the appropriate API domain based on the league ID from JSON payload
    /// </summary>
    /// <param name="jsonPayload">The JSON payload containing league information</param>
    /// <returns>The API domain to use for ECMS requests</returns>
    Task<string> ResolveApiDomainAsync(string jsonPayload);

    /// <summary>
    /// Resolves the appropriate API domain based on a specific league ID
    /// </summary>
    /// <param name="leagueId">The league identifier</param>
    /// <returns>The API domain to use for ECMS requests</returns>
    string ResolveApiDomain(string leagueId);
}
