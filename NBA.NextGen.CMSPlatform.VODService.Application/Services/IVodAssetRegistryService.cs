// "//-----------------------------------------------------------------------".
// <copyright file="IVodAssetRegistryService.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Application.Services
{
    using System;
    using System.Collections.Generic;
    using System.Threading.Tasks;
    using NBA.NextGen.CMSPlatform.VODService.Domain.Common;
    using NBA.NextGen.CMSPlatform.VODService.Domain.Common.Enums;

    /// <summary>
    /// IVodAssetRegistryService.
    /// </summary>
    public interface IVodAssetRegistryService
    {
        /// <summary>
        /// Creates an Asset Entry.
        /// </summary>
        /// <param name="assetId">Asset Id.</param>
        /// <param name="fileName">Source File name.</param>
        /// <param name="flowType">Flow Type..</param>
        /// <returns>A Task.</returns>
        Task CreateAssetEntryAsync(string assetId, string fileName, IngestionFlowTypes flowType);

        /// <summary>
        /// Adds an event to an asset.
        /// </summary>
        /// <param name="assetId">The asset id.</param>
        /// <param name="eventType">The event type.</param>
        /// <param name="timestampUtc">The time stamp in utc.</param>
        /// <param name="message">The optional message.</param>
        /// <returns>A task.</returns>
        Task AddAssetEventAsync(string assetId, AssetEventTypes eventType, DateTime timestampUtc, string message = "");

        /// <summary>
        /// Gets a matching file details record by file name.
        /// </summary>
        /// <param name="assetId">The file name.</param>
        /// <returns>The details record or null.</returns>
        public Task<AssetRegistryDetails> GetAssetDetailsByAssetIdAsync(string assetId);

        /// <summary>
        /// Gets a list of event details by asset id.
        /// </summary>
        /// <param name="assetId">The asset id.</param>
        /// <returns>The list of file details or empty list.</returns>
        public Task<List<AssetEventDetails>> GetEventsForAssetIdAsync(string assetId);
    }
}