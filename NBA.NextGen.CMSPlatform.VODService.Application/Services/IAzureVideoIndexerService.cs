// "//-----------------------------------------------------------------------".
// <copyright file="IAzureVideoIndexerService.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Application.Services
{
    using System.Collections.Generic;
    using System.Threading.Tasks;
    using NBA.NextGen.CMSPlatform.VODService.Domain.Common;
    using NBA.NextGen.CMSPlatform.VODService.Domain.VideoIndexer;

    /// <summary>
    /// IAzureVideoIndexerService.
    /// </summary>
    public interface IAzureVideoIndexerService
    {
        /// <summary>
        /// SubmitJobRequest.
        /// </summary>
        /// <returns>string.</returns>
        Task<string> SubmitJobAsync<T>(string containerName, string resourcePath, string correlationKey, T payload, string trigger, string resourceId) where T : class;

        /// <summary>
        /// List all videos in indexer.
        /// </summary>
        /// <returns>A list of videos.</returns>
        Task<List<VideoIndexerEntry>> ListExistingVideosAsync();

        /// <summary>
        /// Gets the external id for a given video id.
        /// </summary>
        /// <param name="videoId">The video id.</param>
        /// <returns>The external id.</returns>
        Task<string> GetExternalIdAsync(string videoId);

        /// <summary>
        /// Gets the captions for a given video id.
        /// </summary>
        /// <param name="videoId">The video id.</param>
        /// <returns>The vtt formated captions.</returns>
        Task<string> GetVttCaptionsAsync(string videoId);

        /// <summary>
        /// Deletes the video and insights for a given video id.
        /// </summary>
        /// <param name="videoId">The video id.</param>
        /// <returns>Success or Failure.</returns>
        Task<bool> DeleteVideoAsync(string videoId);
    }
}