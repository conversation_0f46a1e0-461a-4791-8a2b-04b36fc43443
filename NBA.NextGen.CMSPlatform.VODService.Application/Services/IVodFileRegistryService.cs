using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using NBA.NextGen.CMSPlatform.VODService.Domain.Common;
using NBA.NextGen.CMSPlatform.VODService.Domain.Common.Enums;

namespace NBA.NextGen.CMSPlatform.VODService.Application.Services;

public interface IVodFileRegistryService
{
    Task RecordFileArrivalAsync(string fileName, string parentFolder, IngestionFlowTypes flowType, DateTime arrivalTimestampUtc);
    Task LogFileCopyAsync(string relativePath, IngestionFlowTypes flowType, DateTime arrivalTimestampUtc, string sourceContainer, string destinationContainer, double sizeInBytes, TimeSpan duration);
    Task RegisterFileProcessedAsync(string fileName, bool triggeredWorkflow, DateTime processedTimestampUtc, Exception exception = null);
    Task CorrelateFileToAssetIdAsync(string fileName, string assetId);
    Task<FileRegistryDetails> GetFileDetailsByNameAsync(string fileName);
    Task<List<FileRegistryDetails>> GetFileDetailsByAssetIdAsync(string assetId);
}
