using System.Collections.Generic;
using System.Threading.Tasks;
using NBA.NextGen.CMSPlatform.VODService.Domain.Common;
using Amazon.TranscribeService.Model;

namespace NBA.NextGen.CMSPlatform.VODService.Application.Services
{
    public interface IAWSTranscribeService
    {
        Task<string> StartTranscriptionAsync(string outputBucketName, string relativeFolders, string jobName, string inputUrl);
        Task<GetTranscriptionJobResponse> GetTranscriptionJobByIdAsync(string jobName);
    }
}