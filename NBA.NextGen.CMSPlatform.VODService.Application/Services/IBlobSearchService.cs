// "//-----------------------------------------------------------------------".
// <copyright file="IBlobSearchService.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Application.Services
{
    using System;
    using System.Collections.Generic;
    using System.Threading.Tasks;
    using Microsoft.Azure.Storage.Blob;

    /// <summary>
    /// IBlobSearchService.
    /// </summary>
    public interface IBlobSearchService
    {
        /// <summary>
        /// Searches for blobs in a folder.
        /// </summary>
        /// <param name="connectionString">The blob connection string.</param>
        /// <param name="containerName">The container name.</param>
        /// <param name="folderName">The name of the folder.</param>
        /// <param name="from">The starting time.</param>
        /// <param name="to">The ending time.</param>
        /// <returns>The list of blobs found.</returns>
        Task<List<ICloudBlob>> SearchByFolderAndDateTimeRangeAsync(string connectionString, string containerName, string folderName, DateTime from, DateTime to);
    }
}
