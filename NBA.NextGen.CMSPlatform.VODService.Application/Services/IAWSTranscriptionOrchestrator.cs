using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using NBA.NextGen.CMSPlatform.VODService.Domain.Common;

namespace NBA.NextGen.CMSPlatform.VODService.Application.Services;

public interface IAWSTranscriptionOrchestrator
{
    Task<string> StartTranscriptionWorkflowAsync<T>(string bucketName, string assetId, string resourcePath, T payload, string triggerFile, string resourceId) where T : class;
    Task<bool> AdvanceToTranscribeAsync(string jobId);
    Task CleanUpAsync(string jobId);
    string GetRelativePath(string bucketName, string url);
    Task<List<string>> RearrangeCaptionsAsync(string jobId, string srtFile, string vttFile);
}
