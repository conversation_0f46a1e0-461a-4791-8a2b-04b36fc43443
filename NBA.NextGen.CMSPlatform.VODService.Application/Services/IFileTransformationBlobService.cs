// "//-----------------------------------------------------------------------".
// <copyright file="IFileTransformationBlobService.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Application.Services
{
    using System.Threading.Tasks;
    using Microsoft.Extensions.Logging;
    using NBA.NextGen.CMSPlatform.VODService.Domain.Vcms;

    /// <summary>
    /// Interface for handling BLOBs.
    /// </summary>
    public interface IFileTransformationBlobService
    {
        /// <summary>
        /// Checks if xml file exists.
        /// </summary>
        /// <param name="fileName">Name of the file.</param>
        /// <returns>
        /// bool.
        /// </returns>
        Task<bool> CheckIfXMLExistsAsync(string fileName);

        /// <summary>
        /// Checks if json file exists.
        /// </summary>
        /// <param name="fileName">Name of the file.</param>
        /// <returns>boolean value.</returns>
        Task<bool> CheckIfJSONExistsAsync(string fileName);

        /// <summary>
        /// Gets the name of the file.
        /// </summary>
        /// <param name="fileName">Name of the file.</param>
        /// <param name="logger">The logger.</param>
        /// <returns>name.</returns>
        string GetFileName(string fileName, ILogger logger);

        /// <summary>
        /// Reads the XML.
        /// </summary>
        /// <typeparam name="T">Type of object.</typeparam>
        /// <param name="fileName">Name of the file.</param>
        /// <param name="logger">logger.</param>
        /// <returns>Dedired type of Xml object.</returns>
        Task<T> ReadXmlAsync<T>(string fileName, ILogger logger);

        /// <summary>
        /// Reads the XML.
        /// </summary>
        /// <typeparam name="T">Type of object.</typeparam>
        /// <param name="fileName">Name of the file.</param>
        /// <param name="logger">logger.</param>
        /// <returns>Dedired type of Xml object.</returns>
        Task<string> ReadResourceAsStringAsync<T>(string fileName, ILogger logger);

        /// <summary>
        /// Reads the Json.
        /// </summary>
        /// <param name="fileName">Name of the file.</param>
        /// <param name="logger">The logger.</param>
        /// <returns>ADI.</returns>
        Task<ADI> ReadJsonAsync(string fileName, ILogger logger);

        /// <summary>
        /// Writes to the BLOB.
        /// </summary>
        /// <param name="fileName">Name of the file.</param>
        /// <param name="content">Content of the BLOb.</param>
        /// <param name="logger">The logger.</param>
        /// <returns>A <see cref="Task"/> representing the result of the asynchronous operation.</returns>
        Task WriteBlobAsync(string fileName, ADI content, ILogger logger);

        /// <summary>
        /// Checks if BLOB exists.
        /// </summary>
        /// <param name="fileName">Name of the file.</param>
        /// <returns>A <see cref="Task{Boolean}"/> representing the result of the asynchronous operation.</returns>
        Task<bool> BlobExistsAsync(string fileName);

        /// <summary>
        /// Gets BLOB location.
        /// </summary>
        /// <param name="fileName">Name of the file.</param>
        /// <returns>string.</returns>
        string GetBlobLocation(string fileName);
    }
}
