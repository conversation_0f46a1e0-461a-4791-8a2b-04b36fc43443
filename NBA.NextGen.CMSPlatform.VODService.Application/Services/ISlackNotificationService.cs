// "//-----------------------------------------------------------------------".
// <copyright file="ISlackNotificationService.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Application.Services
{
    using System.Threading.Tasks;

    /// <summary>
    /// ISlackNotificationService.
    /// </summary>
    public interface ISlackNotificationService
    {
        /// <summary>
        /// Writes a message to the configured slack channel.
        /// </summary>
        /// <param name="message">The message.</param>
        /// <returns>A Task.</returns>
        Task WriteMessageAsync(string message);

        /// <summary>
        /// Sends an already serialized message to the configured slack channel.
        /// </summary>
        /// <param name="json">The json.</param>
        /// <returns>A Task.</returns>
        Task WriteJsonAsync(string json);

        /// <summary>
        /// Writes a message to the configured error slack channel.
        /// </summary>
        /// <param name="message">The message.</param>
        /// <returns>A Task.</returns>
        Task WriteErrorMessageAsync(string message);

        /// <summary>
        /// Sends an already serialized message to the configured error slack channel.
        /// </summary>
        /// <param name="json">The json.</param>
        /// <returns>A Task.</returns>
        Task WriteErrorJsonAsync(string json);
    }
}