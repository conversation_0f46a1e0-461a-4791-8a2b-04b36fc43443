// "//-----------------------------------------------------------------------".
// <copyright file="WscFeedMessage.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Application.Dispatcher.DomainMessage
{
    using NBA.NextGen.CMSPlatform.VODService.Domain.Common;

    /// <summary>
    /// NbaeInhouseMessage.
    /// </summary>
    public class WscFeedMessage : PayloadMessage
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="WscFeedMessage"/> class.
        /// </summary>
        public WscFeedMessage()
        {
        }

        /// <summary>
        /// Gets or sets Wsc.
        /// </summary>
        public string Wsc { get; set; }

        /// <summary>
        /// Gets or sets <PERSON>son.
        /// </summary>
        public string Json { get; set; }
    }
}
