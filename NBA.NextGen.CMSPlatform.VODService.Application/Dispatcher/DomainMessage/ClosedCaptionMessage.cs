// "//-----------------------------------------------------------------------".
// <copyright file="ClosedCaptionMessage.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Application.Dispatcher.DomainMessage
{
    using NBA.NextGen.CMSPlatform.VODService.Domain.Common;

    /// <summary>
    /// ClosedCaptionMessage.
    /// </summary>
    public class ClosedCaptionMessage : PayloadMessage
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="ClosedCaptionMessage"/> class.
        /// </summary>
        public ClosedCaptionMessage()
        {
        }
    }
}
