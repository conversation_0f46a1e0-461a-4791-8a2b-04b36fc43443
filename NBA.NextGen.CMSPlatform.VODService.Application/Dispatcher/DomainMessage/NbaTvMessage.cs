// "//-----------------------------------------------------------------------".
// <copyright file="NbaTvMessage.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Application.Dispatcher.DomainMessage
{
    using System.Collections.Generic;
    using NBA.NextGen.CMSPlatform.VODService.Domain.Common;

    /// <summary>
    /// NbaeInhouseMessage.
    /// </summary>
    public class NbaTvMessage : PayloadMessage
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="NbaTvMessage"/> class.
        /// </summary>
        public NbaTvMessage()
        {
        }
    }
}
