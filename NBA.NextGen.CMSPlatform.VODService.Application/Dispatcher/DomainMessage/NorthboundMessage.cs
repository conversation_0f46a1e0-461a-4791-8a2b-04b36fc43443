// "//-----------------------------------------------------------------------".
// <copyright file="NorthboundMessage.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Application.Dispatcher.DomainMessage
{
    using NBA.NextGen.CMSPlatform.VODService.Domain.Common;

    /// <summary>
    /// NbaeInhouseMessage.
    /// </summary>
    public class NorthboundMessage : PayloadMessage
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="NorthboundMessage"/> class.
        /// </summary>
        public NorthboundMessage()
        {
        }
    }
}
