namespace NBA.NextGen.CMSPlatform.VODService.Application.Dispatcher;

using System.Diagnostics.CodeAnalysis;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using MST.Common.Data;
using MST.Common.Messaging;
using NBA.NextGen.CMSPlatform.VODService.Application.Dispatcher.ContentHandler;
using NBA.NextGen.CMSPlatform.VODService.Application.Services;
using NBA.NextGen.CMSPlatform.VODService.Domain.Config;

public class DirectFlowDispatcher : DispatcherBase
{
    public DirectFlowDispatcher(IDataStoreFactory objectStorageFactory, ILogger<DirectFlowDispatcher> logger, IOptions<NormalFlowSettings> settings, [NotNull] IOptions<FileTransformationSettings> blobSettings, IFileTransformationBlobService blobHandlerService, IObjectRepositoryFactory repositoryFactory, IMessageSenderFactory queueClientProvider, IVodFileRegistryService vodFileRegistryService, IVodAssetRegistryService vodAssetRegistryService, IConfiguration configuration)
        : base(objectStorageFactory, logger, settings, repositoryFactory, queueClientProvider, vodFileRegistryService, vodAssetRegistryService, configuration)
    {
        var directFlowHandler = new DirectFlowHandler(blobSettings, blobHandlerService, logger);
        this.AddHandler(directFlowHandler);
    }
}
