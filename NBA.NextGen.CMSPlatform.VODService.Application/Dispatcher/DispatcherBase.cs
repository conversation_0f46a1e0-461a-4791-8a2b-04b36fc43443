namespace NBA.NextGen.CMSPlatform.VODService.Application.Dispatcher;

using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using MST.Common.Data;
using MST.Common.Messaging;
using NBA.NextGen.CMSPlatform.VODService.Application.Dispatcher.ContentHandler;
using NBA.NextGen.CMSPlatform.VODService.Application.Services;
using NBA.NextGen.CMSPlatform.VODService.Domain.Common;
using NBA.NextGen.CMSPlatform.VODService.Domain.Config;

public abstract class DispatcherBase
{
    private readonly List<AbsBlobContentHandler> list = new List<AbsBlobContentHandler>();
    private readonly ILogger logger;
    private readonly IDataStoreFactory blobClientProvider;

    private readonly IObjectRepositoryFactory repositoryFactory;
    private readonly IMessageSender<SubmitToEcmsWrapper> ecmsSubmitQueueClient;
    private readonly IMessageSender<AdiMessageWrapper> vcmsSubmitQueueClient;

    private readonly IVodFileRegistryService vodFileRegistryService;
    private readonly IVodAssetRegistryService vodAssetRegistryService;
    private readonly IConfiguration configuration;

    protected DispatcherBase(IDataStoreFactory objectStorageFactory, ILogger logger, IOptions<NormalFlowSettings> settings, IObjectRepositoryFactory repositoryFactory, IMessageSenderFactory queueClientProvider, IVodFileRegistryService vodFileRegistryService, IVodAssetRegistryService vodAssetRegistryService, IConfiguration configuration)
    {
        this.blobClientProvider = objectStorageFactory;
        this.Settings = settings.Value;
        this.logger = logger;
        this.repositoryFactory = repositoryFactory;
        this.ecmsSubmitQueueClient = queueClientProvider.Resolve<SubmitToEcmsWrapper>();
        this.vcmsSubmitQueueClient = queueClientProvider.Resolve<AdiMessageWrapper>();
        this.vodFileRegistryService = vodFileRegistryService;
        this.vodAssetRegistryService = vodAssetRegistryService;
        this.configuration = configuration;

        this.logger.LogInformation("DispatcherBase complete initializing!");
    }

    public string ResourceIdentifier { get; set; }

    protected NormalFlowSettings Settings { get; private set; }

    public AbsBlobContentHandler GetHandler<T>()
    {
        return this.list.FirstOrDefault(h => h.GetType() == typeof(T));
    }

    public AbsBlobContentHandler GetHandler(string filePath)
    {
        this.list.ForEach(h =>
        {
            h.SetContextPath(filePath);
        });

        var handlers = this.list.Where(h => h.Accept());

        if (!handlers.Any())
        {
            return null;
        }

        return handlers.First();
    }

    public async Task<string> SimpleProcessingAsync(string filePath)
    {
        this.ResourceIdentifier = filePath;

        this.list.ForEach(h =>
        {
            h.SetContextPath(filePath);
        });

        var handlers = this.list.Where(h => h.Accept());

        if (!handlers.Any())
        {
            return null;
        }

        if (handlers.Count() > 1)
        {
            this.logger.LogWarning("MORE THAN 1 HANDLER FOUND!");
        }

        foreach (var handler in handlers)
        {
            this.logger.LogInformation($"ResourceId: {this.ResourceIdentifier}, File: {filePath} is being handled by: {handler.GetType().Name}");
            var response = await handler.ProcessingAsync(filePath).ConfigureAwait(false);

            if (!response)
            {
                throw new InvalidProgramException($"ResourceId: {this.ResourceIdentifier}, File: {filePath} handled by: {handler.GetType().Name} Did NOT Return Success");
            }
        }

        return this.ResourceIdentifier;
    }

    protected void AddHandler([NotNull] AbsBlobContentHandler handler)
    {
        handler.Logger = this.logger;
        handler.BlobClientProvider = this.blobClientProvider;
        handler.Settings = this.Settings;
        handler.Dispatcher = this;
        handler.RepositoryFactory = this.repositoryFactory;
        handler.EcmsSubmitQueueClient = this.ecmsSubmitQueueClient;
        handler.VcmsSubmitQueueClient = this.vcmsSubmitQueueClient;
        handler.VodFileRegistryService = this.vodFileRegistryService;
        handler.VodAssetRegistryService = this.vodAssetRegistryService;
        handler.Configuration = this.configuration;
        this.list.Add(handler);
    }
}

