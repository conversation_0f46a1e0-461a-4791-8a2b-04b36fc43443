namespace NBA.NextGen.CMSPlatform.VODService.Application.Dispatcher;

using System.Diagnostics.CodeAnalysis;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using MST.Common.Data;
using MST.Common.Messaging;
using NBA.NextGen.CMSPlatform.VODService.Application.Dispatcher.ContentHandler;
using NBA.NextGen.CMSPlatform.VODService.Application.Services;
using NBA.NextGen.CMSPlatform.VODService.Domain.Config;

public class LiveToVodDispatcher : DispatcherBase
{
    public LiveToVodDispatcher(IDataStoreFactory objectStorageFactory, ILogger<LiveToVodDispatcher> logger, IOptions<NormalFlowSettings> settings, IObjectRepositoryFactory repositoryFactory, IMessageSenderFactory queueClientProvider, IVodFileRegistryService vodFileRegistryService, IVodAssetRegistryService vodAssetRegistryService, IConfiguration configuration)
        : base(objectStorageFactory, logger, settings, repositoryFactory, queueClientProvider, vodFileRegistryService, vodAssetRegistryService, configuration)
    {
        AbsBlobContentHandler liveToVodContentHandler = new LiveToVodContentHandler();
        liveToVodContentHandler.ConsumerEndpoint = "/private/api/v1/manage-nba-video/services/livetovod/from-function/video/create";
        liveToVodContentHandler.ContainerName = this.Settings.VodFeedbackContainer;

        this.AddHandler(liveToVodContentHandler);
    }
}

