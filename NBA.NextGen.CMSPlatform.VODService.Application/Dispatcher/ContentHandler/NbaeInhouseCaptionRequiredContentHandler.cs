using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using NBA.NextGen.CMSPlatform.VODService.Application.Services;
using NBA.NextGen.CMSPlatform.VODService.Domain.Common;
using NBA.NextGen.CMSPlatform.VODService.Domain.Common.Enums;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace NBA.NextGen.CMSPlatform.VODService.Application.Dispatcher.ContentHandler;

public class NbaeInhouseCaptionRequiredContentHandler : NbaeInhouseContentHandler, ISupportAsyncCaptions
{
    public IAWSTranscriptionOrchestrator TranscriptionOrchestrator { get; set; }

    /// <inheritdoc/>
    public override bool Accept()
    {
        if (this.MatchFolder("nbae-inhouse-caption-required") && this.EndsWith(".json"))
        {
            return true;
        }

        return false;
    }

    /// <inheritdoc/>
    public override async Task<bool> HandleContentAsync()
    {
        PayloadMessage payload = await this.BuildPayloadAsync().ConfigureAwait(false);
        if (payload != null)
        {
            return true;
        }

        return false;
    }

    public Task<bool> LinkCaptionsToPayloadAndSubmitAsync(JObject message, string vttUrl, string srtUrl)
    {
        var mp4 = message["assets"]["mp4"].ToString();

        this.Logger.LogInformation($"Finished moving Closed Caption Files to source Container.");

        var safeVtt = EnsureResourceLink(this.Settings.ContainerName, vttUrl);
        var safeSrt = EnsureResourceLink(this.Settings.ContainerName, srtUrl);

        var cc = new JProperty("cc", new JArray
                {
                    new JObject(
                        new JProperty("vtt", safeVtt),
                        new JProperty("srt", safeSrt)),
                });

        var mp4json = new JProperty("mp4", mp4);

        message.Property("assets", System.StringComparison.InvariantCultureIgnoreCase).Value = new JObject(mp4json, cc);

        var json = message.ToString();

        return this.PublishToECMSAsync(this.ContextPath, json);
    }

    /// <inheritdoc/>
    protected override async Task<PayloadMessage> ModifyPayloadAsync([NotNull] PayloadMessage message)
    {
        var assets = message.Root["assets"] as Dictionary<string, object>;

        if (!assets.ContainsKey("mp4"))
        {
            throw new ArgumentNullException("MP4 Was not found in meta-data. File: " + this.ContextPath);
        }

        var mp4 = assets["mp4"].ToString();

        var videoId = await TranscriptionOrchestrator.StartTranscriptionWorkflowAsync(this.Settings.ContainerName, this.ResourceIdentifier, mp4, message.Root, this.ContextPath, this.ResourceIdentifier);
        return message;
    }
}
