// "//-----------------------------------------------------------------------".
// <copyright file="WscContentHandler.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Application.Dispatcher.ContentHandler
{
    using System;
    using System.Collections.Generic;
    using System.Threading.Tasks;
    using Microsoft.Extensions.Logging;
    using NBA.NextGen.CMSPlatform.VODService.Application.Dispatcher.DomainMessage;
    using NBA.NextGen.CMSPlatform.VODService.Domain.Common;
    using Newtonsoft.Json.Linq;

    /// <summary>
    /// WscContentHandler.
    /// </summary>
    public class WscContentHandler : DefaultBlobContentHandler
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="WscContentHandler"/> class.
        /// </summary>
        public WscContentHandler()
        {
        }

        /// <summary>
        /// accept.
        /// </summary>
        /// <returns>bool.</returns>
        public override bool Accept()
        {
            if (this.EndsWith(".json") && this.MatchFolder("wsc-feed"))
            {
                return true;
            }

            return false;
        }

        /// <summary>
        /// Build WscFeedMessage.
        /// </summary>
        /// <returns>PayloadMessage.</returns>
        public override async Task<PayloadMessage> BuildPayloadAsync()
        {
            WscFeedMessage message = new WscFeedMessage();
            string content = await this.GetResourceContentAsync().ConfigureAwait(false);
            JObject jsonObject = JObject.Parse(content);
            string identifier = string.Empty;
            identifier = jsonObject["id"].ToObject<string>();
            this.SetResourceIdentifier(identifier);
            await this.VodFileRegistryService.CorrelateFileToAssetIdAsync(this.ContextPath, identifier).ConfigureAwait(false);
            await this.VodAssetRegistryService.CreateAssetEntryAsync(identifier, this.ContextPath, Domain.Common.Enums.IngestionFlowTypes.NormalFlow).ConfigureAwait(false);
            List<string> processedURLList = new List<string>();
            if (jsonObject["videoSccUrl"] != null)
            {
                string videoSccUrl = jsonObject["videoSccUrl"].ToObject<string>();
                processedURLList.Add(videoSccUrl);
            }

            if (jsonObject["videoVttUrl"] != null)
            {
                string videoVttUrl = jsonObject["videoVttUrl"].ToObject<string>();
                processedURLList.Add(videoVttUrl);
            }

            if (jsonObject["videoSrtUrl"] != null)
            {
                string videoSrtUrl = jsonObject["videoSrtUrl"].ToObject<string>();
                processedURLList.Add(videoSrtUrl);
            }

            if (jsonObject["videoThumbnailUrl"] != null)
            {
                string videoThumbnailUrl = jsonObject["videoThumbnailUrl"].ToObject<string>();
                processedURLList.Add(videoThumbnailUrl);
            }

            if (jsonObject["videoUrl"] != null)
            {
                string videoUrl = jsonObject["videoUrl"].ToObject<string>();
                processedURLList.Add(videoUrl);
            }

            Dictionary<string, object> flatJson = MessageHelper.ConvertWSCHighlightVideoToFlatJSON(jsonObject.ToString());
            Dictionary<string, object> assets = new Dictionary<string, object>();
            string targetPath = this.ContextPath.Replace("upload/trigger", "media", System.StringComparison.InvariantCultureIgnoreCase);
            this.Logger.LogInformation("the relative path to be saved is " + targetPath);
            for (int i = 0; i < processedURLList.Count; i++)
            {
                string url = processedURLList[i];
                string extension = url;
                if (url.Contains('.', System.StringComparison.OrdinalIgnoreCase))
                {
                    extension = url.Substring(url.LastIndexOf(".", System.StringComparison.OrdinalIgnoreCase) + 1);
                }

                string fileName = this.GetResourceWithExtension(this.ContextPath, extension);
                await this.VodFileRegistryService.CorrelateFileToAssetIdAsync(fileName, identifier).ConfigureAwait(false);
                assets[extension] = this.GetResourceLink(this.Settings.ContainerName, fileName);
            }

            this.Logger.LogInformation("associated assets is " + assets);
            message.Root["json"] = flatJson;
            message.Root["wsc"] = jsonObject;
            message.Root["assets"] = assets;
            return message;
        }
    }
}
