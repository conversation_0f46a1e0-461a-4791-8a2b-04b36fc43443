// "//-----------------------------------------------------------------------".
// <copyright file="LiveToVodContentHandler.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Application.Dispatcher.ContentHandler
{
    using System;
    using System.Collections.Generic;
    using System.Collections.ObjectModel;
    using System.Linq;
    using System.Threading.Tasks;
    using NBA.NextGen.CMSPlatform.VODService.Application.Dispatcher.DomainMessage;
    using NBA.NextGen.CMSPlatform.VODService.Domain.Common;
    using NBA.NextGen.CMSPlatform.VODService.Domain.Common.Enums;
    using NBA.NextGen.CMSPlatform.VODService.Domain.Utilities;
    using NBA.NextGen.CMSPlatform.VODService.Domain.Vcms;
    using Newtonsoft.Json;

    /// <summary>
    /// LiveToVodContentHandler.
    /// </summary>
    public class LiveToVodContentHandler : DefaultBlobContentHandler
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="LiveToVodContentHandler"/> class.
        /// </summary>
        public LiveToVodContentHandler()
        {
        }

        /// <summary>
        /// accept.
        /// </summary>
        /// <returns>bool.</returns>
        public override bool Accept()
        {
            if (this.EndsWith(".xml") && (this.MatchFolder(this.Settings.VodFeedbackFolder) || this.MatchFolder("l2v")))
            {
                return true;
            }

            return false;
        }

        /// <summary>
        /// Build PayloadMessage.
        /// </summary>
        /// <returns>PayloadMessage.</returns>
        public override async Task<PayloadMessage> BuildPayloadAsync()
        {
            PayloadMessage message = new PayloadMessage();
            string content = await this.GetResourceContentAsync(this.ContextPath);
            ADI vcmsAdi = XmlHelper.DeserializeXML<ADI>(content);
            var assetId = vcmsAdi.GetAdiAssetId();
            await this.VodFileRegistryService.CorrelateFileToAssetIdAsync(this.ContextPath, assetId);
            message.Root["adi"] = vcmsAdi;
            
            this.SetResourceIdentifier(assetId);
            await this.VodAssetRegistryService.CreateAssetEntryAsync(assetId, this.ContextPath, IngestionFlowTypes.LiveToVod);
            return message;
        }
    }
}
