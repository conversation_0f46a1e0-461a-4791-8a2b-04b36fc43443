// "//-----------------------------------------------------------------------".
// <copyright file="NorthboundMetadataContentHandler.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Application.Dispatcher.ContentHandler
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;
    using Microsoft.Extensions.Logging;
    using NBA.NextGen.CMSPlatform.VODService.Application.Dispatcher.DomainMessage;
    using NBA.NextGen.CMSPlatform.VODService.Application.Services;
    using NBA.NextGen.CMSPlatform.VODService.Domain.Common;
    using NBA.NextGen.CMSPlatform.VODService.Domain.Common.Enums;
    using Newtonsoft.Json;
    using Newtonsoft.Json.Linq;

    /// <summary>
    /// NorthboundMetadataContentHandler.
    /// </summary>
    public class NorthboundMetadataContentHandler : DefaultBlobContentHandler, ISupportAsyncCaptions
    {
        public IAWSTranscriptionOrchestrator TranscriptionOrchestrator { get; set; }

        /// <summary>
        /// accept.
        /// </summary>
        /// <returns>bool.</returns>
        public override bool Accept()
        {
            if (this.MatchFolder("nbad-northbound-meta") && this.EndsWith(".mp4"))
            {
                return true;
            }

            return false;
        }

        public Task<bool> LinkCaptionsToPayloadAndSubmitAsync(JObject message, string vttUrl, string srtUrl)
        {
            this.Logger.LogInformation($"Finished moving Closed Caption Files to source Container.");

            var safeVtt = EnsureResourceLink(this.Settings.ContainerName, vttUrl);
            var safeSrt = EnsureResourceLink(this.Settings.ContainerName, srtUrl);

            var cc = new JProperty("cc", new JArray
                {
                    new JObject(
                        new JProperty("vtt", safeVtt),
                        new JProperty("srt", safeSrt)),
                });

            var assets = message["assets"] as JContainer;

            assets.Add(cc);

            message.Property("assets", System.StringComparison.InvariantCultureIgnoreCase).Value = assets;

            var json = message.ToString();

            return this.PublishToECMSAsync(this.ContextPath, json);
        }

        /// <inheritdoc />
        public override async Task<bool> HandleContentAsync()
        {
            PayloadMessage payload = await this.BuildPayloadAsync().ConfigureAwait(false);
            if (payload != null)
            {
                return true;
            }

            return false;
        }

        /// <summary>
        /// Build NorthboundMetadataMessage.
        /// </summary>
        /// <returns>PayloadMessage.</returns>
        public override async Task<PayloadMessage> BuildPayloadAsync()
        {
            NorthboundMetadataMessage message = new NorthboundMetadataMessage();
            string metadataXml = this.GetResourceWithExtension(this.ContextPath, "xml");
            string content = await this.GetResourceContentAsync(metadataXml).ConfigureAwait(false);
            JObject jsonStructure = MessageHelper.ConvertXMLToJson(content);
            Dictionary<string, object> meta = new Dictionary<string, object>();
            Dictionary<string, object> metaInfo = new Dictionary<string, object>();
            Dictionary<string, object> assets = new Dictionary<string, object>();
            JObject jsonEvsMetadata = jsonStructure["EVS_Metadatas"].ToObject<JObject>();
            message.Root["asset"] = this.GetResourceLink(this.Settings.ContainerName, this.ContextPath);
            meta["parsed"] = jsonEvsMetadata;
            message.Root["meta"] = meta;
            meta["info"] = metaInfo;
            string headline = string.Empty;
            string description = string.Empty;
            IEnumerable<JToken> userFields = jsonEvsMetadata.SelectTokens("Clips_Infos.Clip.Other_Clip_Infos.UserFields.UserField");
            var assetId = jsonEvsMetadata.SelectToken("Clips_Infos.Clip.Other_Clip_Infos.AssetGUID").ToObject<string>();
            await this.VodFileRegistryService.CorrelateFileToAssetIdAsync(this.ContextPath, assetId).ConfigureAwait(false);
            await this.VodFileRegistryService.CorrelateFileToAssetIdAsync(metadataXml, assetId).ConfigureAwait(false);
            await this.VodAssetRegistryService.CreateAssetEntryAsync(assetId, this.ContextPath, Domain.Common.Enums.IngestionFlowTypes.NormalFlow).ConfigureAwait(false);
            this.SetResourceIdentifier(assetId);
            foreach (JToken @oneToken in userFields)
            {
                var fields = @oneToken.AsJEnumerable();
                foreach (JToken oneFiled in fields)
                {
                    string header = oneFiled["@Header"].ToString();
                    string value = oneFiled["#text"]?.ToString();
                    if (header.Equals("Headline", StringComparison.OrdinalIgnoreCase))
                    {
                        headline = value;
                    }

                    if (header.Equals("Description", StringComparison.OrdinalIgnoreCase))
                    {
                        description = value;
                    }
                }
            }

            metaInfo["AssetId"] = assetId;
            metaInfo["Headline"] = headline;
            metaInfo["Description"] = description;
            metaInfo["File_Path_HiRes"] = jsonEvsMetadata.SelectToken("General_Infos.File_Path_HiRes").ToObject<string>();
            metaInfo["XT_Duration_str"] = jsonEvsMetadata.SelectToken("General_Infos.XFile_File_Infos.XT_Duration_str").ToObject<string>();
            metaInfo["XT_Creation_Time_str"] = jsonEvsMetadata.SelectToken("General_Infos.XFile_File_Infos.XT_Creation_Time_str").ToObject<string>();
            metaInfo["XT_Creation_Time_str_EVS"] = jsonEvsMetadata.SelectToken("General_Infos.XFile_File_Infos.XT_Creation_Time_str_EVS").ToObject<string>();
            metaInfo["XT_Creation_Time_GMT"] = jsonEvsMetadata.SelectToken("General_Infos.XFile_File_Infos.XT_Creation_Time_GMT").ToObject<string>();
            metaInfo["XT_Creation_Time_GMT_str"] = jsonEvsMetadata.SelectToken("General_Infos.XFile_File_Infos.XT_Creation_Time_GMT_str").ToObject<string>();
            metaInfo["XT_Creation_Time_GMT_str_EVS"] = jsonEvsMetadata.SelectToken("General_Infos.XFile_File_Infos.XT_Creation_Time_GMT_str_EVS").ToObject<string>();
            metaInfo["XT_ClipName"] = jsonEvsMetadata.SelectToken("Clips_Infos.Clip.XFile_Clip_Infos.XT_ClipName").ToObject<string>();
            meta["body"] = content;
            assets["mp4"] = this.GetResourceLinkWithExtension(this.ContextPath, "mp4");
            assets["xml"] = this.GetResourceLinkWithExtension(this.ContextPath, "xml");

            message.Root["assets"] = assets;

            var fullPath = this.GetResourceLink(this.Settings.ContainerName, this.ContextPath);
            var videoId = await TranscriptionOrchestrator.StartTranscriptionWorkflowAsync(this.Settings.ContainerName, assetId, fullPath, message.Root, this.ContextPath, this.ResourceIdentifier).ConfigureAwait(false);
            return message;
        }
    }
}
