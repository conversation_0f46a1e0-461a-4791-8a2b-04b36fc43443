// "//-----------------------------------------------------------------------".
// <copyright file="NbaeInhouseContentHandler.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Application.Dispatcher.ContentHandler
{
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;
    using Microsoft.Extensions.Logging;
    using NBA.NextGen.CMSPlatform.VODService.Application.Dispatcher.DomainMessage;
    using NBA.NextGen.CMSPlatform.VODService.Domain.Common;
    using Newtonsoft.Json.Linq;

    /// <summary>
    /// NbaeInhouseContentHandler.
    /// </summary>
    public class NbaeInhouseContentHandler : DefaultBlobContentHandler
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="NbaeInhouseContentHandler"/> class.
        /// </summary>
        public NbaeInhouseContentHandler()
        {
        }

        /// <summary>
        /// accept.
        /// </summary>
        /// <returns>bool.</returns>
        public override bool Accept()
        {
            if (this.MatchFolder("nbae-inhouse") && this.EndsWith(".json"))
            {
                return true;
            }

            return false;
        }

        /// <summary>
        /// Build NbaeInhouseMessage.
        /// </summary>
        /// <returns>PayloadMessage.</returns>
        public override async Task<PayloadMessage> BuildPayloadAsync()
        {
            NbaeInhouseMessage message = new NbaeInhouseMessage();
            this.Logger.LogInformation("starting processing the resource " + this.ContextPath);
            string content = await this.GetResourceContentAsync().ConfigureAwait(false);
            this.Logger.LogInformation("complete loading the resource " + this.ContextPath);
            if (this.ContextPath.EndsWith(".json", System.StringComparison.InvariantCultureIgnoreCase))
            {
                JObject jsonObject = JObject.Parse(content);
                string assetId = jsonObject["Id"].ToObject<string>();
                await this.VodFileRegistryService.CorrelateFileToAssetIdAsync(this.ContextPath, assetId).ConfigureAwait(false);
                await this.VodAssetRegistryService.CreateAssetEntryAsync(assetId, this.ContextPath, Domain.Common.Enums.IngestionFlowTypes.NormalFlow).ConfigureAwait(false);
                string gameId = jsonObject["GameId"].ToObject<string>();
                this.SetResourceIdentifier(assetId);
                if (jsonObject["Files"] != null)
                {
                    JToken files = jsonObject["Files"];
                    Dictionary<string, object> assets = new Dictionary<string, object>();
                    foreach (JObject oneFileItem in files.OfType<JObject>())
                    {
                        var fileType = oneFileItem["Type"].ToObject<string>();
                        if (fileType.EndsWith("PAL"))
                        {
                            continue;
                        }

                        string fileName = oneFileItem["Name"].ToObject<string>();
                        if (fileName.Contains("/", System.StringComparison.OrdinalIgnoreCase))
                        {
                            fileName = fileName.Substring(fileName.LastIndexOf("/", System.StringComparison.OrdinalIgnoreCase) + 1);
                        }

                        string assetPath = this.GetAssetPath(fileName);

                        bool exists = await this.CheckResourceExistsAsync(assetPath).ConfigureAwait(false);
                        if (exists)
                        {
                            await this.VodFileRegistryService.CorrelateFileToAssetIdAsync(assetPath, assetId).ConfigureAwait(false);
                            string fileExt = fileName.Substring(fileName.LastIndexOf(".", System.StringComparison.OrdinalIgnoreCase) + 1);
                            assets[fileExt] = this.GetResourceLink(this.Settings.ContainerName, assetPath);
                        }
                        else if (assetPath.EndsWith("mp4"))
                        {
                            throw new MissingResourceException(this.Settings.ContainerName, assetPath);
                        }
                    }

                    message.Root["assets"] = assets;
                    message.Root["data"] = jsonObject;
                    message.Root["asset"] = this.GetResourceLink(this.Settings.ContainerName, this.ContextPath);
                }
            }
            else if (this.ContextPath.EndsWith(".mp4", System.StringComparison.InvariantCultureIgnoreCase))
            {
                message.Root["asset"] = this.GetResourceLink(this.Settings.ContainerName, this.ContextPath);
            }

            var payload = await this.ModifyPayloadAsync(message);

            return payload;
        }

        /// <summary>
        /// Gives any inheriting class a chance to modify payload.
        /// </summary>
        /// <param name="message">The current payload.</param>
        /// <returns>The final payload.</returns>
        protected virtual Task<PayloadMessage> ModifyPayloadAsync(PayloadMessage message)
        {
            return Task.FromResult(message);
        }
    }
}
