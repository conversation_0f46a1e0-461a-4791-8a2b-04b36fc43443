// "//-----------------------------------------------------------------------".
// <copyright file="WscRegionContentHandler.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Application.Dispatcher.ContentHandler
{
    using System;
    using System.Threading.Tasks;
    using Microsoft.Extensions.Logging;
    using NBA.NextGen.CMSPlatform.VODService.Application.Dispatcher.DomainMessage;
    using NBA.NextGen.CMSPlatform.VODService.Domain.Common;
    using Newtonsoft.Json.Linq;

    /// <summary>
    /// WscRegionContentHandler.
    /// </summary>
    public class WscRegionContentHandler : WscContentHandler
    {
        /// <inheritdoc/>
        public override bool Accept()
        {
            if (this.EndsWith(".json") && this.MatchFolder("regional-content"))
            {
                return true;
            }

            return false;
        }

        /// <inheritdoc/>
        public override async Task<PayloadMessage> BuildPayloadAsync()
        {
            var message = await base.BuildPayloadAsync().ConfigureAwait(false);

            var pathSplit = this.ContextPath.Split("/");
            var region = pathSplit[pathSplit.Length - 2];

            if (region != null)
            {
                this.Logger.LogInformation($"{this.ContextPath} Assigned Publisher: {region}");
                var wscJobj = (JObject)message.Root["wsc"];
                wscJobj["publisher"] = region;
            }

            return message;
        }
    }
}
