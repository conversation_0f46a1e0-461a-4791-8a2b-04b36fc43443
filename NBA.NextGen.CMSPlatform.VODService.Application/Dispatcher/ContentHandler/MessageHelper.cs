using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;
using System.Xml;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using NBA.NextGen.CMSPlatform.VODService.Application.Services;
using NBA.NextGen.CMSPlatform.VODService.Domain.Common.Enums;
using NBA.NextGen.CMSPlatform.VODService.Domain.Config;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace NBA.NextGen.CMSPlatform.VODService.Application.Dispatcher.ContentHandler;

public static class MessageHelper
{
    public static async Task<Dictionary<string, object>> GetMediaObjectAsync(ILogger logger, string mediaId, string teamContext, string homeTeamName, string awayTeamName, [NotNull] string mediaName, [NotNull] FileTransformationSettings settings, IVodAssetRegistryService assetRegistryService, HttpClient httpClient, IConfiguration configuration)
    {
        try
        {

            var key = configuration["gms_password"];

            var values = new List<KeyValuePair<string, string>>
            {
                new KeyValuePair<string, string>("grant_type", "password"),
                new KeyValuePair<string, string>("username", settings.GmsUser),
                new KeyValuePair<string, string>("password", key)
            };

            var content = new FormUrlEncodedContent(values);
            var authenticationString = "gmsclientid:XY7kmzoNzl100";
            string requestUri = settings.GmsDomain + "/gms/oauth/token";
            var base64EncodedAuthenticationString = Convert.ToBase64String(System.Text.ASCIIEncoding.ASCII.GetBytes(authenticationString));
            using var requestMessage = new HttpRequestMessage(HttpMethod.Post, requestUri);
            requestMessage.Headers.Authorization = new AuthenticationHeaderValue("Basic", base64EncodedAuthenticationString);
            requestMessage.Content = content;
            var task = httpClient.SendAsync(requestMessage);
            var response = await task.ConfigureAwait(false);
            response.EnsureSuccessStatusCode();
            string responseBody = await response.Content.ReadAsStringAsync().ConfigureAwait(false);
            JObject jsonObject = JObject.Parse(responseBody);
            Dictionary<string, object> media = new Dictionary<string, object>();
            string token = jsonObject["access_token"].ToObject<string>();
            string mediaAPI = settings.GmsDomain + "/gms/api/v1/core/streaminngmedia?leagueId=00&mediaId=" + mediaId;
            if (teamContext == "home" || teamContext == "away")
            {
                mediaAPI = mediaAPI + "&teamcontext=" + teamContext + "&home=" + homeTeamName + "&away=" + awayTeamName;
            }

            if (mediaName.StartsWith("NSS-", StringComparison.OrdinalIgnoreCase))
            {
                mediaAPI = settings.GmsDomain + "/gms/api/v1/core/streaminngmedia?leagueId=00" + "&mediaName=" + mediaName;
            }

            using var request = new HttpRequestMessage(HttpMethod.Get, mediaAPI);
            request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", token);
            response = await httpClient.SendAsync(request, HttpCompletionOption.ResponseHeadersRead).ConfigureAwait(false);
            response.EnsureSuccessStatusCode();
            var mediaContent = await response.Content.ReadAsStringAsync().ConfigureAwait(false);
            jsonObject = JObject.Parse(mediaContent);
            media["mediaName"] = jsonObject["displayName"].ToObject<string>();
            media["mediaUnique"] = jsonObject["name"].ToObject<string>();
            media["mediaId"] = jsonObject["id"].ToObject<string>();
            media["languageName"] = jsonObject["languageName"].ToObject<string>();
            media["languageDisplayName"] = jsonObject["languageDisplayName"].ToObject<string>();
            return media;
        }
        catch (Exception ex)
        {
            await assetRegistryService.AddAssetEventAsync(mediaId, AssetEventTypes.IngestionFailedGMSIssue, DateTime.UtcNow, ex.Message);
            logger.LogError(ex, "Failed Communicating with GMS");
            throw;
        }
    }

    public static JObject ConvertXMLToJson(string xmlContent)
    {
        if (xmlContent is null)
        {
            throw new ArgumentNullException(nameof(xmlContent));
        }

        XmlDocument xmlDoc = new XmlDocument();
        xmlDoc.LoadXml(xmlContent);
        string json = JsonConvert.SerializeXmlNode(xmlDoc);
        JObject jsonObject = JObject.Parse(json);
        return jsonObject;
    }

    public static string ConvertCaptionFromVtt(string data)
    {
        string simpleTimeFormat = "mm:ss,fff";
        string extendedTimeFormat = "HH:mm:ss,fff";

        StringReader reader = new StringReader(data);
        StringBuilder output = new StringBuilder();
        int lineNumber = 1;
        string line;
        while ((line = reader.ReadLine()) != null)
        {
            if (IsTimecode(line))
            {
                output.AppendLine(lineNumber + string.Empty);
                lineNumber++;

                line = line.Replace('.', ',');

                line = DeleteCueSettings(line);

                string timeSrt1 = line.Substring(0, line.IndexOf('-', StringComparison.Ordinal));
                string timeSrt2 = line.Substring(line.IndexOf('>', StringComparison.Ordinal) + 1);
                int divIt1 = timeSrt1.Count(x => x == ':');
                int divIt2 = timeSrt1.Count(x => x == ':');

                string timeFormat = simpleTimeFormat;
                if (divIt1 != divIt2)
                {
                    throw new InvalidDataException("InvalidTimeFormat");
                }

                if (divIt1 == 2 && divIt2 == 2)
                {
                    timeFormat = extendedTimeFormat;
                }

                DateTime timeAux1 = DateTime.ParseExact(timeSrt1.Trim(), timeFormat, CultureInfo.InvariantCulture);
                DateTime timeAux2 = DateTime.ParseExact(timeSrt2.Trim(), timeFormat, CultureInfo.InvariantCulture);
                line = timeAux1.ToString(extendedTimeFormat, CultureInfo.InvariantCulture) + " --> " + timeAux2.ToString(extendedTimeFormat, CultureInfo.InvariantCulture);

                output.AppendLine(line);

                bool foundCaption = false;
                while (true)
                {
                    if ((line = reader.ReadLine()) == null)
                    {
                        if (foundCaption)
                        {
                            break;
                        }
                        else
                        {
                            throw new InvalidDataException("Fail to find the caption");
                        }
                    }

                    if (string.IsNullOrEmpty(line) || string.IsNullOrWhiteSpace(line))
                    {
                        output.AppendLine();
                        break;
                    }

                    foundCaption = true;
                    output.AppendLine(line);
                }
            }
        }

        return output.ToString();
    }

    public static Dictionary<string, object> ConvertWSCHighlightVideoToFlatJSON(string data)
    {
        Dictionary<string, object> jsonStructure = new Dictionary<string, object>();
        JObject jsonObject = JObject.Parse(data);
        string[] oneToOneList = new string[12] { "id", "title", "competitionId", "contentType", "publishDate", "publishMethod", "creationSettings", "duration", "resolution", "description", "customData", "tags" };
        for (int i = 0; i < oneToOneList.Length; i++)
        {
            string attribute = oneToOneList[i];
            jsonStructure[attribute] = jsonObject[attribute];
        }

        HashSet<string> clipIds = new HashSet<string>();
        HashSet<string> gameClocks = new HashSet<string>();
        HashSet<string> gameDates = new HashSet<string>();
        HashSet<string> times = new HashSet<string>();
        HashSet<string> periods = new HashSet<string>();
        HashSet<string> actionTypes = new HashSet<string>();
        HashSet<string> eventTypes = new HashSet<string>();
        HashSet<string> eventIds = new HashSet<string>();
        HashSet<string> actionParamsShotType = new HashSet<string>();
        HashSet<string> actionParamsShotAttributes = new HashSet<string>();
        List<Dictionary<string, string>> teams = new List<Dictionary<string, string>>();
        List<Dictionary<string, string>> players = new List<Dictionary<string, string>>();
        List<Dictionary<string, string>> playerTeams = new List<Dictionary<string, string>>();
        List<Dictionary<string, object>> games = new List<Dictionary<string, object>>();
        HashSet<string> playerSet = new HashSet<string>();
        HashSet<string> playerTeamSet = new HashSet<string>();
        HashSet<string> teamSet = new HashSet<string>();
        HashSet<string> gameSet = new HashSet<string>();
        JToken events = jsonObject["events"] ?? new JArray();
        foreach (JObject eventItem in events.OfType<JObject>())
        {
            AppendItem(actionTypes, eventItem, "actionType");
            AppendItem(eventTypes, eventItem, "eventType");
            AppendItem(gameClocks, eventItem, "gameClock");
            AppendItem(times, eventItem, "time");
            AppendItem(periods, eventItem, "period");
            eventIds.Add(GetNbaId(eventItem["providerEventIds"].ToObject<JObject[]>()));
            if (eventItem["actionParameters"] != null)
            {
                JToken actionParameters = eventItem["actionParameters"];
                if (actionParameters["shotAttributes"] != null)
                {
                    string shotAttributes = actionParameters["shotAttributes"].ToObject<string>();
                    Array.ConvertAll(shotAttributes.Split(';'), s => actionParamsShotAttributes.Add(s));
                }

                if (actionParameters["shotType"] != null)
                {
                    string shotType = actionParameters["shotType"].ToObject<string>();
                    Array.ConvertAll(shotType.Split(';'), s => actionParamsShotType.Add(s));
                }
            }

            if (eventItem["players"] != null)
            {
                JToken playerNodes = eventItem["players"];
                foreach (JObject playerItem in playerNodes.OfType<JObject>())
                {
                    Dictionary<string, string> onePlayer = new Dictionary<string, string>();
                    Dictionary<string, string> onePlayerTeam = new Dictionary<string, string>();
                    string playerName = playerItem["name"].ToObject<string>();
                    onePlayer["name"] = playerName;
                    string playerId = GetNbaId(playerItem["providerPlayerId"].ToObject<JObject[]>());
                    onePlayer["id"] = playerId;
                    if (playerItem["team"] != null)
                    {
                        JToken playerTeam = playerItem["team"];
                        onePlayer["tid"] = GetNbaId(playerTeam["providerTeamId"].ToObject<JObject[]>());
                        onePlayerTeam["id"] = GetNbaId(playerTeam["providerTeamId"].ToObject<JObject[]>());
                        onePlayerTeam["name"] = playerTeam["name"].ToObject<string>();
                    }

                    if (!playerSet.Contains(playerId))
                    {
                        playerSet.Add(playerId);
                        players.Add(onePlayer);
                    }

                    if (!playerTeamSet.Contains(onePlayerTeam["id"]))
                    {
                        playerTeamSet.Add(onePlayerTeam["id"]);
                        playerTeams.Add(onePlayerTeam);
                    }
                }
            }

            if (eventItem["game"] != null)
            {
                JToken gameNode = eventItem["game"];
                Dictionary<string, object> oneGame = new Dictionary<string, object>();
                string gameId = GetNbaId(gameNode["providerGameId"].ToObject<JObject[]>());
                if (!gameSet.Contains(gameId))
                {
                    if (gameNode["teams"] != null)
                    {
                        JToken gameTeamsNode = gameNode["teams"];
                        List<Dictionary<string, string>> gameTeams = new List<Dictionary<string, string>>();
                        foreach (JObject gameteamItem in gameTeamsNode.OfType<JObject>())
                        {
                            Dictionary<string, string> oneTeam = new Dictionary<string, string>();
                            string teamId = GetNbaId(gameteamItem["providerTeamId"].ToObject<JObject[]>());
                            string name = gameteamItem["name"].ToObject<string>();
                            oneTeam["id"] = teamId;
                            oneTeam["name"] = name;
                            if (!teamSet.Contains(teamId))
                            {
                                teamSet.Add(teamId);
                                teams.Add(oneTeam);
                            }

                            gameTeams.Add(oneTeam);
                        }

                        oneGame["teams"] = gameTeams;
                    }

                    gameSet.Add(gameId);
                    games.Add(oneGame);
                }

                AppendItem(gameDates, (JObject)gameNode, "gameDate");
            }

            if (eventItem["eventClips"] != null)
            {
                JToken eventClips = eventItem["eventClips"];
                foreach (JObject eventClipItem in eventClips.OfType<JObject>())
                {
                    AppendItem(clipIds, eventClipItem, "id");
                }
            }
        }

        jsonStructure["clipIds"] = clipIds;
        jsonStructure["gameClocks"] = gameClocks;
        jsonStructure["gameDates"] = gameDates;
        jsonStructure["times"] = times;
        jsonStructure["periods"] = periods;
        jsonStructure["actionTypes"] = actionTypes;
        jsonStructure["eventTypes"] = eventTypes;
        jsonStructure["eventIds"] = eventIds;
        jsonStructure["actionParamsShotType"] = actionParamsShotType;
        jsonStructure["actionParamsShotAttributes"] = actionParamsShotAttributes;
        jsonStructure["teams"] = teams;
        jsonStructure["players"] = players;
        jsonStructure["playerTeams"] = playerTeams;
        jsonStructure["games"] = games;

        return jsonStructure;
    }

    private static string GetNbaId(JObject[] providerList)
    {
        string returnId = string.Empty;
        for (int i = 0; i < providerList.Length; i++)
        {
            JObject oneItem = providerList[i];
            string provider = oneItem["provider"].ToObject<string>();
            if (provider == "NBA")
            {
                return oneItem["id"].ToObject<string>();
            }
        }

        return returnId;
    }

    private static void AppendItem(HashSet<string> list, JObject jsonItem, string attrbute)
    {
        if (jsonItem[attrbute] != null)
        {
            string value = jsonItem[attrbute].ToObject<string>();
            list.Add(value);
        }
    }

    private static bool IsTimecode(string line)
    {
        return line.Contains("-->", StringComparison.Ordinal);
    }

    private static string DeleteCueSettings(string line)
    {
        StringBuilder output = new StringBuilder();
        foreach (char ch in line)
        {
            char lower = char.ToLower(ch, new System.Globalization.CultureInfo("en-US"));
            if (lower >= 'a' && lower <= 'z')
            {
                break;
            }

            output.Append(ch);
        }

        return output.ToString();
    }
}

