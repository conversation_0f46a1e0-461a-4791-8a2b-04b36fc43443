// "//-----------------------------------------------------------------------".
// <copyright file="WscFasttrackContentHandler.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Application.Dispatcher.ContentHandler
{
    using System.Collections.Generic;
    using System.Threading.Tasks;
    using NBA.NextGen.CMSPlatform.VODService.Application.Dispatcher.DomainMessage;
    using NBA.NextGen.CMSPlatform.VODService.Domain.Common;
    using Newtonsoft.Json.Linq;

    /// <summary>
    /// WscFasttrackContentHandler.
    /// </summary>
    public class WscFasttrackContentHandler : WscContentHandler
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="WscFasttrackContentHandler"/> class.
        /// </summary>
        public WscFasttrackContentHandler()
        {
        }

        /// <summary>
        /// accept.
        /// </summary>
        /// <returns>bool.</returns>
        public override bool Accept()
        {
            if (this.MatchFolder("wsc-fasttrack") && this.EndsWith(".json"))
            {
                return true;
            }

            return false;
        }

        /// <summary>
        /// Build WscFastTrackMessage.
        /// </summary>
        /// <returns>PayloadMessage.</returns>
        public override async Task<PayloadMessage> BuildPayloadAsync()
        {
            WscFastTrackMessage message = new WscFastTrackMessage();
            string content = await this.GetResourceContentAsync().ConfigureAwait(false);
            Dictionary<string, object> flatJson = MessageHelper.ConvertWSCHighlightVideoToFlatJSON(content);
            JObject jsonObject = JObject.Parse(content);
            message.Root["json"] = flatJson;
            message.Root["wsc"] = jsonObject;
            message.Root["assets"] = new Dictionary<string, string>();
            return message;
        }
    }
}
