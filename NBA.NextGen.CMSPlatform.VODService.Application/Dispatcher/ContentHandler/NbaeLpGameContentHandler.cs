// "//-----------------------------------------------------------------------".
// <copyright file="NbaeLpGameContentHandler.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Application.Dispatcher.ContentHandler
{
    /// <summary>
    /// NbaeLpGameContentHandler.
    /// </summary>
    public class NbaeLpGameContentHandler : NbaTvContentHandler
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="NbaeLpGameContentHandler"/> class.
        /// </summary>
        public NbaeLpGameContentHandler()
        {
            this.FolderMatchType = "nbae-lp-classic-games";
        }

        /// <summary>
        /// accept.
        /// </summary>
        /// <returns>bool.</returns>
        public override bool Accept()
        {
            if (this.MatchFolder("nbae-lp-classic-games") && base.Accept())
            {
                return true;
            }

            return false;
        }
    }
}
