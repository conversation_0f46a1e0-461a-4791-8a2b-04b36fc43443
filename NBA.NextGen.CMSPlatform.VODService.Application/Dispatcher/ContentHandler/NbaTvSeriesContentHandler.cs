// "//-----------------------------------------------------------------------".
// <copyright file="NbaTvSeriesContentHandler.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Application.Dispatcher.ContentHandler
{
    /// <summary>
    /// NbaTvSeriesContentHandler.
    /// </summary>
    public class NbaTvSeriesContentHandler : NbaTvContentHandler
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="NbaTvSeriesContentHandler"/> class.
        /// </summary>
        public NbaTvSeriesContentHandler()
        {
            this.FolderMatchType = "nbatv-series";
        }

        /// <summary>
        /// accept.
        /// </summary>
        /// <returns>bool.</returns>
        public override bool Accept()
        {
            if (this.MatchFolder("nbatv-series") && this.EndsWith(".xml"))
            {
                return true;
            }

            return false;
        }
    }
}
