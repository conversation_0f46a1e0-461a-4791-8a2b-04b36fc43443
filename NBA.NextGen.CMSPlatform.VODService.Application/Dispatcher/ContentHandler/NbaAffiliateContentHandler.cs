// "//-----------------------------------------------------------------------".
// <copyright file="NbaAffiliateContentHandler.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Application.Dispatcher.ContentHandler
{
    using System;
    using System.Threading.Tasks;
    using Microsoft.Azure.Storage;
    using Microsoft.Azure.Storage.Blob;
    using Microsoft.Extensions.Logging;
    using NBA.NextGen.CMSPlatform.VODService.Application.Services;
    using NBA.NextGen.CMSPlatform.VODService.Domain.Common;
    using NBA.NextGen.CMSPlatform.VODService.Domain.Common.Enums;
    using NBA.NextGen.CMSPlatform.VODService.Domain.NormalFlow;
    using Newtonsoft.Json;
    using Newtonsoft.Json.Linq;

    /// <summary>
    /// NbaAffiliateContentHandler.
    /// </summary>
    public class NbaAffiliateContentHandler : DefaultBlobContentHandler, ISupportAsyncCaptions
    {
        public IAWSTranscriptionOrchestrator TranscriptionOrchestrator { get; set; }

        /// <summary>
        /// Gets a value indicating whether that this is a liberty live model.
        /// </summary>
        private bool IsLibertyLive => this.MatchFolder("/liberty-live/");

        /// <inheritdoc/>
        public override bool Accept()
        {
            if (this.MatchFolder("third-party") && this.EndsWith(".json"))
            {
                return true;
            }

            return false;
        }

        /// <inheritdoc/>
        public override async Task<bool> HandleContentAsync()
        {
            var needsCaptionGeneration = this.MatchFolder("/without-captions/");

            PayloadMessage payload;

            this.Logger.LogInformation("NBA Affiliate: Needs Caption Generations is: {NeedsCaptionGeneration}", needsCaptionGeneration);

            // Let's check for liberty-live
            if (this.IsLibertyLive)
            {
                this.Logger.LogInformation("NBA Affiliate: Found Liberty Live Asset, handling accordingly");

                // This will get us an initialized handler, by giving a different file name.
                var handler = (WscRegionContentHandler)this.Dispatcher.GetHandler<WscRegionContentHandler>();
                handler.SetContextPath(this.ContextPath);
                payload = await handler.BuildPayloadAsync().ConfigureAwait(false);
                this.SetResourceIdentifier(handler.ResourceIdentifier);
                var wscJobj = (JObject)payload.Root["wsc"];
                wscJobj["entitlement"] = "local-access-1611661313";
                wscJobj["videoPublisher"] = "Liberty Live";
                wscJobj["videoType"] = "Games";
                wscJobj["videoCategory"] = "Archives";
                wscJobj["videoFranchise"] = "Alternate Broadcast";
                wscJobj["videoFranchiseName"] = "Liberty Live Broadcast";

                if (!needsCaptionGeneration)
                {
                    await this.CheckForSubtitlesWithDelayAsync().ConfigureAwait(false);
                }
            }
            else
            {
                payload = await this.BuildPayloadAsync().ConfigureAwait(false);
            }

            if (payload != null)
            {
                if (needsCaptionGeneration)
                {
                    // Our context path is a json, we need the full MP4 path
                    var asset = this.GetResourceLinkWithExtension(this.ContextPath, "mp4");
                    var videoId = this.IsLibertyLive
                    ? await TranscriptionOrchestrator.StartTranscriptionWorkflowAsync(this.Settings.ContainerName, this.ResourceIdentifier, asset, payload.Root, this.ContextPath, this.ResourceIdentifier)
                    : await TranscriptionOrchestrator.StartTranscriptionWorkflowAsync(this.Settings.ContainerName, this.ResourceIdentifier, asset, payload, this.ContextPath, this.ResourceIdentifier);
                    return true;
                }
                else
                {
                    var endpoint = this.ConsumerEndpoint;
                    string json;
                    if (this.IsLibertyLive)
                    {
                        var handler = (WscRegionContentHandler)this.Dispatcher.GetHandler<WscRegionContentHandler>();
                        endpoint = handler.ConsumerEndpoint;
                        json = JsonConvert.SerializeObject(payload.Root);
                    }
                    else
                    {
                        json = JsonConvert.SerializeObject(payload);
                    }

                    var result = await this.PublishToECMSAsync(this.ContextPath, json, endpoint).ConfigureAwait(false);
                    return result;
                }
            }

            return false;
        }

        public Task<bool> LinkCaptionsToPayloadAndSubmitAsync(JObject message, string vttUrl, string srtUrl)
        {
            string json;

            this.Logger.LogInformation($"Finished moving Closed Caption Files to source Container.");

            var endpoint = this.ConsumerEndpoint;

            var safeVtt = EnsureResourceLink(this.Settings.ContainerName, vttUrl);
            var safeSrt = EnsureResourceLink(this.Settings.ContainerName, srtUrl);

            if (this.IsLibertyLive)
            {
                var handler = (WscRegionContentHandler)this.Dispatcher.GetHandler<WscRegionContentHandler>();
                endpoint = handler.ConsumerEndpoint;
                json = this.AddCaptionsForNBALibertyLive(message, safeVtt, safeSrt);
            }
            else
            {
                json = this.AddCaptionsForNBAAffiliate(message, safeVtt, safeSrt);
            }

            return this.PublishToECMSAsync(this.ContextPath, json, endpoint);
        }

        /// <inheritdoc/>
        public override async Task<PayloadMessage> BuildPayloadAsync()
        {
            NbaAffiliatePayload payload = new NbaAffiliatePayload();

            string json = await this.GetResourceContentAsync(this.ContextPath).ConfigureAwait(false);

            var metadata = Newtonsoft.Json.JsonConvert.DeserializeObject<NbaAffiliateAssetMetadata>(json);

            var relativeMp4 = this.GetAssetPath(System.IO.Path.GetFileName(metadata.Video));
            var relativeJpg = this.GetAssetPath(System.IO.Path.GetFileName(metadata.Thumbnail));

            await this.VodFileRegistryService.CorrelateFileToAssetIdAsync(this.ContextPath, metadata.AssetID).ConfigureAwait(false);
            await this.VodFileRegistryService.CorrelateFileToAssetIdAsync(relativeMp4, metadata.AssetID).ConfigureAwait(false);
            await this.VodFileRegistryService.CorrelateFileToAssetIdAsync(relativeJpg, metadata.AssetID).ConfigureAwait(false);

            payload.Asset = this.ContextPath;

            payload.Assets = new Assets
            {
                Mp4 = this.GetResourceLink(this.Settings.ContainerName, relativeMp4),
                Jpg = this.GetResourceLink(this.Settings.ContainerName, relativeJpg),
            };

            this.SetResourceIdentifier(metadata.AssetID);
            await this.VodAssetRegistryService.CreateAssetEntryAsync(metadata.AssetID, this.ContextPath, Domain.Common.Enums.IngestionFlowTypes.NormalFlow).ConfigureAwait(false);

            payload.Data = new NbaAffiliatePayloadData()
            {
                AssetID = metadata.AssetID,
                Version = metadata.Version,
                Workflow = metadata.Workflow,
                Publisher = metadata.Publisher,
                PublishDate = metadata.PublishDate,
                Title = metadata.Title,
                Description = metadata.Description,
                Video = metadata.Video,
                Thumbnail = metadata.Thumbnail,
                Duration = metadata.Duration,
                Entitlement = metadata.Entitlement,
                Franchise = new NbaAffiliateFranchise()
                {
                    VideoCategory = metadata.Franchise.VideoCategory,
                    VideoFranchise = metadata.Franchise.VideoFranchise,
                    VideoType = metadata.Franchise.VideoType,
                    VideoFranchiseName = metadata.Franchise.VideoFranchiseName,
                },
                Players = metadata.Players,
                Tags = metadata.Tags,
                Teams = metadata.Teams,
            };

            return payload;
        }

        /// <summary>
        /// AddCaptionsForNBAAffiliate.
        /// </summary>
        /// <param name="message">The original json.</param>
        /// <param name="vttLink">The closed for vtt.</param>
        /// <param name="srtLink">The closed for srt.</param>
        /// <returns>The new Json.</returns>
        private string AddCaptionsForNBAAffiliate(JObject message, string vttLink, string srtLink)
        {
            var payload = Newtonsoft.Json.JsonConvert.DeserializeObject<NbaAffiliatePayload>(message.ToString());
            payload.Assets.CC = new System.Collections.Generic.Dictionary<string, string>()
            {
                { "vtt", vttLink },
                { "srt", srtLink },
            };

            return Newtonsoft.Json.JsonConvert.SerializeObject(payload);
        }

        /// <summary>
        /// AddCaptionsForNBALibertyLive.
        /// </summary>
        /// <param name="message">The original json.</param>
        /// <param name="vttLink">The closed for vtt.</param>
        /// <param name="srtLink">The closed for srt.</param>
        /// <returns>The new Json.</returns>
        private string AddCaptionsForNBALibertyLive(JObject message, string vttLink, string srtLink)
        {
            var cc = new JProperty("cc", new JArray
            {
                new JObject(
                    new JProperty("vtt", vttLink),
                    new JProperty("srt", srtLink)),
            });

            var assets = message["assets"] as JContainer;

            assets.Add(cc);

            message.Property("assets", System.StringComparison.InvariantCultureIgnoreCase).Value = assets;

            return message.ToString();
        }

        /// <summary>
        /// CheckForSubtitlesWithDelayAsync.
        /// </summary>
        /// <returns>A Task.</returns>
        private async Task CheckForSubtitlesWithDelayAsync()
        {
            var relativeSrtUrl = this.GetResourceWithExtension(this.ContextPath, "srt");

            var srtExists = await this.CheckResourceExistsAsync(relativeSrtUrl).ConfigureAwait(false);

            // If the SRT doesn't exist, wait 1 minute.
            if (!srtExists)
            {
                await Task.Delay(TimeSpan.FromMinutes(1)).ConfigureAwait(false);
                srtExists = await this.CheckResourceExistsAsync(relativeSrtUrl).ConfigureAwait(false);
            }

            // If it still doesn't exist, then log it.
            if (!srtExists)
            {
                await this.VodAssetRegistryService.AddAssetEventAsync(this.ResourceIdentifier, AssetEventTypes.LibertyLiveMissingCaptions, DateTime.UtcNow, "SRT File Note found after 1 minute delay... will process without captions!").ConfigureAwait(false);
            }
        }
    }
}