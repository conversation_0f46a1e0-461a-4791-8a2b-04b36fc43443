// "//-----------------------------------------------------------------------".
// <copyright file="NbaTvContentHandler.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Application.Dispatcher.ContentHandler
{
    using System;
    using System.Collections.Generic;
    using System.Threading.Tasks;
    using System.Xml;
    using NBA.NextGen.CMSPlatform.VODService.Application.Dispatcher.DomainMessage;
    using NBA.NextGen.CMSPlatform.VODService.Domain.Common;

    /// <summary>
    /// NbaTvContentHandler.
    /// </summary>
    public abstract class NbaTvContentHandler : DefaultBlobContentHandler
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="NbaTvContentHandler"/> class.
        /// </summary>
        protected NbaTvContentHandler()
        {
        }

        /// <summary>
        /// Gets or sets FolderMatchType.
        /// </summary>
        public string FolderMatchType { get; set; }

        /// <summary>
        /// accept.
        /// </summary>
        /// <returns>bool.</returns>
        public override bool Accept()
        {
            if (this.EndsWith(".xml") || this.EndsWith(".mp4") || this.EndsWith(".jpg") || this.EndsWith(".scc") || this.EndsWith(".xml"))
            {
                return true;
            }

            return false;
        }

        /// <summary>
        /// Build NbaTvMessage.
        /// </summary>
        /// <returns>PayloadMessage.</returns>
        public override async Task<PayloadMessage> BuildPayloadAsync()
        {
            string xmlResourceFileName = this.ContextPath;
            if (!this.ContextPath.EndsWith(".xml", StringComparison.InvariantCultureIgnoreCase))
            {
                xmlResourceFileName = this.ConvertToXMLResourceName(this.ContextPath);
            }

            NbaTvMessage message = new NbaTvMessage();
            string content = await this.GetResourceContentAsync(xmlResourceFileName).ConfigureAwait(false);
            Dictionary<string, object> jsonStructure = this.ParseXMLFromWM(content);
            Dictionary<string, string> files = (Dictionary<string, string>)jsonStructure["Files"];
            Dictionary<string, string> meta = (Dictionary<string, string>)jsonStructure["Meta"];
            Dictionary<string, object> assets = (Dictionary<string, object>)jsonStructure["Assets"];
            List<string> missing = new List<string>();
            foreach (var item in files)
            {
                string resourceName = item.Value;
                string assetPath = this.GetAssetPath(resourceName);
                bool exists = await this.CheckResourceExistsAsync(assetPath).ConfigureAwait(false);
                if (!exists)
                {
                    missing.Add(this.GetResourceLink(this.Settings.ContainerName, assetPath));
                    if (assetPath.EndsWith("mp4", System.StringComparison.OrdinalIgnoreCase))
                    {
                        throw new MissingResourceException(this.Settings.ContainerName, assetPath);
                    }
                }
            }

            Dictionary<string, string> infoNode = new Dictionary<string, string>();
            infoNode["folderMatchType"] = this.FolderMatchType;
            message.Root["meta"] = meta;
            message.Root["missing"] = missing;
            message.Root["assets"] = assets;
            message.Root["info"] = infoNode;
            return message;
        }

        /// <summary>
        /// ParseXMLFromWM.
        /// </summary>
        /// <param name="xmlContent">xmlContent.</param>
        /// <returns>Dictionary.</returns>
        private Dictionary<string, object> ParseXMLFromWM(string xmlContent)
        {
            if (xmlContent is null)
            {
                throw new ArgumentNullException(nameof(xmlContent));
            }

            XmlDocument xmlDoc = new XmlDocument();
            xmlDoc.LoadXml(xmlContent);
            Dictionary<string, string> metadata = new Dictionary<string, string>();
            Dictionary<string, object> assets = new Dictionary<string, object>();
            XmlNodeList rootAssetList = xmlDoc.GetElementsByTagName("Asset");
            Dictionary<string, string> files = new Dictionary<string, string>();
            this.BuildAssetMetadata(rootAssetList[0], files, metadata, assets);
            Dictionary<string, object> returnObject = new Dictionary<string, object>();
            returnObject["Files"] = files;
            returnObject["Assets"] = assets;
            returnObject["Meta"] = metadata;
            return returnObject;
        }

        /// <summary>
        /// appendMetadata.
        /// </summary>
        /// <param name="subAssetNode">subAssetNode.</param>
        /// <param name="submetadata">submetadata.</param>
        /// <param name="rootmetadata">rootmetadata.</param>
        /// <returns>string.</returns>
        private string AppendMetadata(XmlNode subAssetNode, Dictionary<string, string> submetadata, Dictionary<string, string> rootmetadata)
        {
            string assetTypeMeta = string.Empty;
            XmlNodeList metaList = subAssetNode.SelectNodes("Metadata");
            if (metaList.Count > 0)
            {
                XmlNode oneSection = metaList[0];
                string assetClass = string.Empty;
                string assetId = string.Empty;
                XmlNodeList metaDetail = oneSection.SelectNodes("App_Data");
                XmlNodeList amsNode = oneSection.SelectNodes("AMS");
                if (amsNode.Count > 0)
                {
                    XmlNode subMetaNode = amsNode[0];
                    assetClass = subMetaNode.Attributes["Asset_Class"].Value;
                    assetId = subMetaNode.Attributes["Asset_ID"].Value;
                    foreach (XmlNode attribute in subMetaNode.Attributes)
                    {
                        submetadata[attribute.Name] = attribute.Value;
                        if (assetClass.Equals("title", System.StringComparison.OrdinalIgnoreCase))
                        {
                            rootmetadata[attribute.Name] = attribute.Value;
                            this.SetResourceIdentifier(assetId);
                        }
                    }
                }

                foreach (XmlNode subMetaNode in metaDetail)
                {
                    string metaItemName = subMetaNode.Attributes["Name"].Value;
                    string metaItemValue = subMetaNode.Attributes["Value"].Value;
                    submetadata[metaItemName] = metaItemValue;
                    if (assetClass.Equals("title", System.StringComparison.OrdinalIgnoreCase))
                    {
                        rootmetadata[metaItemName] = metaItemValue;
                    }

                    if (metaItemName == "Type")
                    {
                        assetTypeMeta = metaItemValue;
                    }
                }
            }

            return assetTypeMeta;
        }

        /// <summary>
        /// BuildAssetMetadata.
        /// </summary>
        /// <param name="childrenNode">childrenNode.</param>
        /// <param name="files">files.</param>
        /// <param name="metadata">metadata.</param>
        /// <param name="assets">assets.</param>
        private void BuildAssetMetadata(XmlNode childrenNode, Dictionary<string, string> files, Dictionary<string, string> metadata, Dictionary<string, object> assets)
        {
            this.AppendMetadata(childrenNode, metadata, metadata);
            XmlNodeList subAssetList = childrenNode.SelectNodes("Asset");
            string assetTypeMeta = string.Empty;
            foreach (XmlNode subAssetNode in subAssetList)
            {
                Dictionary<string, string> submetadata = new Dictionary<string, string>();
                assetTypeMeta = this.AppendMetadata(subAssetNode, submetadata, metadata);
                XmlNodeList contentList = subAssetNode.SelectNodes("Content");
                if (contentList.Count > 0)
                {
                    XmlNode contentNode = contentList[0];
                    string contentValue = contentNode.Attributes["Value"].Value;
                    if (assetTypeMeta == "mezzanine")
                    {
                        files["mp4"] = contentValue;
                    }
                    else if (assetTypeMeta == "poster")
                    {
                        files["image"] = contentValue;
                    }
                    else if (assetTypeMeta == "captioning")
                    {
                        files["cc"] = contentValue;
                    }
                    else if (assetTypeMeta == "movie")
                    {
                        files["movie"] = contentValue;
                    }

                    string assetPath = this.GetAssetPath(contentValue);
                    submetadata["Content"] = this.GetResourceLink(this.Settings.ContainerName, assetPath);
                }

                assets[assetTypeMeta] = submetadata;
            }
        }
    }
}
