using System;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using NBA.NextGen.CMSPlatform.VODService.Application.Services;
using NBA.NextGen.CMSPlatform.VODService.Application.UseCases.FileTransformation.WscJson.Queries;
using NBA.NextGen.CMSPlatform.VODService.Domain.Common;
using NBA.NextGen.CMSPlatform.VODService.Domain.Common.Enums;
using NBA.NextGen.CMSPlatform.VODService.Domain.Config;
using NBA.NextGen.CMSPlatform.VODService.Domain.Utilities;
using NBA.NextGen.CMSPlatform.VODService.Domain.Vcms;

namespace NBA.NextGen.CMSPlatform.VODService.Application.Dispatcher.ContentHandler;

public class DirectFlowHandler : DefaultBlobContentHandler
{
    private readonly WscJsonFileTransformationQueryHandler handler;
    private readonly FileTransformationSettings fileTransformationSettings;
    private readonly IFileTransformationBlobService blobHandlerService;

    public DirectFlowHandler([NotNull] IOptions<FileTransformationSettings> blobSettings, IFileTransformationBlobService blobHandlerService, ILogger logger)
    {
        this.handler = new WscJsonFileTransformationQueryHandler(blobSettings, blobHandlerService, logger, this.SetResourceIdentifier);
        this.fileTransformationSettings = blobSettings.Value;
        this.blobHandlerService = blobHandlerService;
    }

    public override bool Accept()
    {
        var resourceLink = this.GetResourceLink(this.fileTransformationSettings.ContainerName,this.ContextPath);
        this.Logger.LogInformation("resourceLink " + resourceLink);
        return this.EndsWith(".json")
               && !resourceLink.Contains("/gam/", System.StringComparison.OrdinalIgnoreCase);
    }

    public override async Task<bool> HandleContentAsync()
    {
        var wscJsonFileTransformationQuery = new WscJsonFileTransformationQuery
        {
            FileName = this.ContextPath,
        };

        var response = await this.handler.Handle(wscJsonFileTransformationQuery, default);
        ADI result = response.Adi;
        
        this.SetResourceIdentifier(result.GetAdiAssetId());
        await this.VodFileRegistryService.CorrelateFileToAssetIdAsync(this.ContextPath, this.ResourceIdentifier);

        await this.VodAssetRegistryService.CreateAssetEntryAsync(this.ResourceIdentifier, this.ContextPath, IngestionFlowTypes.DirectFlow);

        string xmlFileName = this.blobHandlerService.GetFileName(this.ContextPath, this.Logger).Split("/", System.StringSplitOptions.RemoveEmptyEntries).Last();
        await this.blobHandlerService.WriteBlobAsync($"{xmlFileName}.xml", result, this.Logger).ConfigureAwait(false);
        this.Logger.LogInformation("Save CableLabs File " + xmlFileName);

        var msg = new AdiMessageWrapper()
        {
            Adi = result,
        };

        await this.VcmsSubmitQueueClient.SendAsync(msg);
        string adiXml = XmlHelper.SerializeXML(result, Encoding.UTF8);
        await this.VodAssetRegistryService.AddAssetEventAsync(msg.Adi.GetAdiAssetId(), AssetEventTypes.QueuedForVcmsSubmission, DateTime.UtcNow, adiXml);
        this.Logger.LogInformation("Direct Flow ADI delivered to SubmitToVCMS Queue");
        return true;
    }
}

