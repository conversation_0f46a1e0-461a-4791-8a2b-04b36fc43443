using System;

namespace NBA.NextGen.CMSPlatform.VODService.Application.Dispatcher.ContentHandler;

public class MissingResourceException : Exception
{

    public string ContainerName { get; set; }
    public string MissingFile { get; set; }

    public MissingResourceException()
    {
    }

    public MissingResourceException(string containerName, string fileName)
        : base("Failed to Find Asset Name: " + fileName)
    {
        ContainerName = containerName;
        MissingFile = fileName;
    }
}
