// "//-----------------------------------------------------------------------".
// <copyright file="DefaultBlobContentHandler.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
namespace NBA.NextGen.CMSPlatform.VODService.Application.Dispatcher.ContentHandler
{
    using System;
    using System.Diagnostics.CodeAnalysis;
    using System.Threading.Tasks;
    using Microsoft.Extensions.Logging;
    using NBA.NextGen.CMSPlatform.VODService.Domain.Common;
    using NBA.NextGen.CMSPlatform.VODService.Domain.Utilities;
    using Newtonsoft.Json;

    /// <summary>
    /// DefaultBlobContentHandler.
    /// </summary>
    public class DefaultBlobContentHandler : AbsBlobContentHandler
    {
        /// <summary>
        /// forceAPI.
        /// </summary>
        private bool forceAPI = true;

        /// <summary>
        /// Initializes a new instance of the <see cref="DefaultBlobContentHandler"/> class.
        /// </summary>
        public DefaultBlobContentHandler()
        {
        }

        /// <inheritdoc/>
        public override bool Accept()
        {
            return false;
        }

        /// <summary>
        /// Build Payload.
        /// </summary>
        /// <returns>Task.</returns>
        public virtual async Task<PayloadMessage> BuildPayloadAsync()
        {
            return await Task.FromResult(new PayloadMessage()).ConfigureAwait(false);
        }

        /// <inheritdoc/>
        public override async Task<bool> HandleContentAsync()
        {
            PayloadMessage playload = await this.BuildPayloadAsync().ConfigureAwait(false);
            if (playload != null)
            {
                var serializerSettings = new JsonSerializerSettings();
                string jsonData = JsonConvert.SerializeObject(playload.Root, Formatting.Indented, serializerSettings);
                var result = await this.PublishToECMSAsync(this.ContextPath, jsonData).ConfigureAwait(false);
                return result;
            }

            return false;
        }

        /// <summary>
        /// SetForceAPI.
        /// </summary>
        /// <param name="forceAPI">forceAPI.</param>
        protected void SetForceAPI(bool forceAPI)
        {
            this.forceAPI = forceAPI;
        }

        /// <summary>
        /// MatchFolder.
        /// </summary>
        /// <param name="folderPattern">folderPattern.</param>
        /// <returns>bool.</returns>
        protected bool MatchFolder([NotNull] string folderPattern)
        {
            if (!folderPattern.EndsWith("/", System.StringComparison.OrdinalIgnoreCase))
            {
                folderPattern = $"{folderPattern}/";
            }

            return this.ContextPath.Contains(folderPattern, System.StringComparison.OrdinalIgnoreCase);
        }

        /// <summary>
        /// EndsWith.
        /// </summary>
        /// <param name="folderPattern">folderPattern.</param>
        /// <returns>bool.</returns>
        protected bool EndsWith([NotNull] string folderPattern)
        {
            return this.ContextPath.EndsWith(folderPattern, System.StringComparison.OrdinalIgnoreCase);
        }

        /// <summary>
        /// convert to xml resource name.
        /// </summary>
        /// <param name="resourceName">resource name.</param>
        /// <returns>string.</returns>
        protected string ConvertToXMLResourceName(string resourceName)
        {
            return resourceName;
        }

        /// <summary>
        /// publish payload to ECMS.
        /// </summary>
        /// <param name="path">content path.</param>
        /// <param name="jsonData">message payload.</param>
        /// <param name="endpointOverride">Optional endpoint override.</param>
        /// <returns>bool.</returns>
        protected async Task<bool> PublishToECMSAsync(string path, string jsonData, string endpointOverride = null)
        {
            var cacheId = Guid.NewGuid().ToString();
            var encodedData = System.Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes(jsonData));

            var repository = this.RepositoryFactory.Resolve<EcmsCacheRecord>();
            var cache = new EcmsCacheRecord()
            {
                Id = cacheId,
                EncodedContent = encodedData
            };
            
            this.Logger.LogInformation("Saving ECMS Payload to Cache...");
            await repository.CreateItemAsync(cache);

            var request = new SubmitToEcmsWrapper()
            {
                ContextPath = this.ContextPath,
                Endpoint = endpointOverride ?? this.ConsumerEndpoint,
                ResourceId = this.ResourceIdentifier,
                FilePath = path,
                CacheId = cacheId
            };

            var msg = $"AssetId: {request.ResourceId}, CacheId: {request.CacheId}, Payload:\n{jsonData}";
            await this.EcmsSubmitQueueClient.SendAsync(request);
            await this.VodAssetRegistryService.AddAssetEventAsync(this.ResourceIdentifier, Domain.Common.Enums.AssetEventTypes.QueuedForEcmsSubmission, DateTime.UtcNow, msg).ConfigureAwait(false);
            return true;
       }
    }
}
