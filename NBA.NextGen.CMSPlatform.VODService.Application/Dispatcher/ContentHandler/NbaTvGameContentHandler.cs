// "//-----------------------------------------------------------------------".
// <copyright file="NbaTvGameContentHandler.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Application.Dispatcher.ContentHandler
{
    /// <summary>
    /// NbaTvGameContentHandler.
    /// </summary>
    public class NbaTvGameContentHandler : NbaTvContentHandler
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="NbaTvGameContentHandler"/> class.
        /// </summary>
        public NbaTvGameContentHandler()
        {
            this.FolderMatchType = "nbatv-classic-games";
        }

        /// <summary>
        /// accept.
        /// </summary>
        /// <returns>bool.</returns>
        public override bool Accept()
        {
            if (this.MatchFolder("nbatv-classic-games") && this.EndsWith(".xml"))
            {
                return true;
            }

            return false;
        }
    }
}
