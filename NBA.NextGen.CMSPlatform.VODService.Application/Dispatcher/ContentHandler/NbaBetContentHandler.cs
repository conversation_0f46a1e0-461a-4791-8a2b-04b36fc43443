// "//-----------------------------------------------------------------------".
// <copyright file="NbaBetContentHandler.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Application.Dispatcher.ContentHandler
{
    using System;
    using System.Threading.Tasks;
    using Microsoft.Extensions.Logging;
    using NBA.NextGen.CMSPlatform.VODService.Application.Dispatcher.DomainMessage;
    using NBA.NextGen.CMSPlatform.VODService.Application.Services;
    using NBA.NextGen.CMSPlatform.VODService.Domain.Common;
    using NBA.NextGen.CMSPlatform.VODService.Domain.Common.Enums;
    using NBA.NextGen.CMSPlatform.VODService.Domain.NormalFlow;
    using Newtonsoft.Json;
    using Newtonsoft.Json.Linq;

    /// <summary>
    /// NbaBetContentHandler.
    /// </summary>
    public class NbaBetContentHandler : DefaultBlobContentHandler, ISupportAsyncCaptions
    {
        public IAWSTranscriptionOrchestrator TranscriptionOrchestrator { get; set; }

        /// <inheritdoc/>
        public override bool Accept()
        {
            if (this.MatchFolder("nba-bet") && this.EndsWith(".mp4"))
            {
                return true;
            }

            return false;
        }

        /// <inheritdoc/>
        public override async Task<bool> HandleContentAsync()
        {
            PayloadMessage payload = await this.BuildPayloadAsync().ConfigureAwait(false);
            if (payload != null)
            {
                var path = this.GetResourceLink(this.Settings.ContainerName, this.ContextPath);
                var videoId = await TranscriptionOrchestrator.StartTranscriptionWorkflowAsync(this.Settings.ContainerName, this.ResourceIdentifier, path, payload, this.ContextPath, this.ResourceIdentifier);

                return true;
            }

            return false;
        }

        public Task<bool> LinkCaptionsToPayloadAndSubmitAsync(JObject message, string vttUrl, string srtUrl)
        {
            var payload = Newtonsoft.Json.JsonConvert.DeserializeObject<NbaBetPayload>(message.ToString());

            this.Logger.LogInformation($"Finished moving Closed Caption Files to source Container.");

            var safeVtt = EnsureResourceLink(this.Settings.ContainerName, vttUrl);
            var safeSrt = EnsureResourceLink(this.Settings.ContainerName, srtUrl);

            payload.Assets.CC = new System.Collections.Generic.Dictionary<string, string>()
            {
                { "vtt", safeVtt },
                { "srt", safeSrt },
            };

            var json = Newtonsoft.Json.JsonConvert.SerializeObject(payload);

            return this.PublishToECMSAsync(this.ContextPath, json);
        }

        /// <inheritdoc/>
        public override async Task<PayloadMessage> BuildPayloadAsync()
        {
            NbaBetPayload payload = new NbaBetPayload();

            string jsonUrl = this.GetResourceWithExtension(this.ContextPath, "json");
            string json = await this.GetResourceContentAsync(jsonUrl).ConfigureAwait(false);

            var metadata = Newtonsoft.Json.JsonConvert.DeserializeObject<NbaBetAssetMetadata>(json);

            payload.Asset = jsonUrl;

            payload.Assets = new Assets
            {
                Mp4 = this.GetResourceLink(this.Settings.ContainerName, this.GetAssetPath(System.IO.Path.GetFileName(this.ContextPath))),
                Jpg = this.GetResourceLink(this.Settings.ContainerName, this.GetAssetPath(System.IO.Path.GetFileName(metadata.Thumbnail))),
            };

            payload.Data = new NbaBetPayloadData()
            {
                AssetID = metadata.AssetID,
                Version = metadata.Version,
                Workflow = metadata.Workflow,
                Publisher = metadata.Publisher,
                Franchise = metadata.Franchise,
                PublishDate = metadata.PublishDate,
                Title = metadata.Title,
                Description = metadata.Description,
                Video = metadata.Video,
                Thumbnail = metadata.Thumbnail,
                Teams = metadata.Teams,
                Players = metadata.Players,
                Tags = metadata.Tags,
            };

            return payload;
        }
    }
}
