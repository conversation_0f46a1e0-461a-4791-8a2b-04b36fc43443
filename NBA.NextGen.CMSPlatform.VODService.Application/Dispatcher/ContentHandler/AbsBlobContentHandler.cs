// "//-----------------------------------------------------------------------".
// <copyright file="AbsBlobContentHandler.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>

namespace NBA.NextGen.CMSPlatform.VODService.Application.Dispatcher.ContentHandler
{
    using System;
    using System.Diagnostics.CodeAnalysis;
    using System.IO;
    using System.Threading.Tasks;
    using Microsoft.Azure.Storage.Blob;
    using Microsoft.Extensions.Configuration;
    using Microsoft.Extensions.Logging;
    using MST.Common.Data;
    using MST.Common.Messaging;
    using NBA.NextGen.CMSPlatform.VODService.Application.Services;
    using NBA.NextGen.CMSPlatform.VODService.Domain.Common;
    using NBA.NextGen.CMSPlatform.VODService.Domain.Common.Enums;
    using NBA.NextGen.CMSPlatform.VODService.Domain.Config;

    /// <summary>
    /// AbsBlobContentHandler.
    /// </summary>
    public abstract class AbsBlobContentHandler
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="AbsBlobContentHandler"/> class.
        /// </summary>
        protected AbsBlobContentHandler()
        {
        }

        /// <summary>
        /// Gets or sets dispatcher.
        /// </summary>
        public DispatcherBase Dispatcher { get; set; }

        /// <summary>
        /// Gets or sets Settings.
        /// </summary>
        public NormalFlowSettings Settings { get; set; }

        /// <summary>
        /// Gets or sets Logger.
        /// </summary>
        public ILogger Logger { get; set; }

        /// <summary>
        /// Gets or sets BlobHandlerService.
        /// </summary>
        public IDataStoreFactory BlobClientProvider { get; set; }

        /// <summary>
        /// Gets or sets ConsumerEndpoint.
        /// </summary>
        public string ConsumerEndpoint { get; set; }

        /// <summary>
        /// Gets or sets ContextPath.
        /// </summary>
        public string ContextPath { get; set; }

        /// <summary>
        /// Gets or sets ResourceIdentifier.
        /// </summary>
        public string ResourceIdentifier { get; set; }

        /// <summary>
        /// Gets or sets ContainerName.
        /// </summary>
        public string ContainerName { get; set; }

        /// <summary>
        /// Gets or sets the Repository Factory.
        /// </summary>
        public IObjectRepositoryFactory RepositoryFactory { get; set; }

        public IMessageSender<SubmitToEcmsWrapper> EcmsSubmitQueueClient { get; set; }
        public IMessageSender<AdiMessageWrapper> VcmsSubmitQueueClient { get; set; }
        public IVodFileRegistryService VodFileRegistryService { get; set; }
        public IVodAssetRegistryService VodAssetRegistryService { get; set; }
        public IConfiguration Configuration { get; set; }

        /// <summary>
        /// HandleContent.
        /// </summary>
        /// <returns>string.</returns>
        public virtual Task<bool> HandleContentAsync()
        {
            return Task.FromResult(true);
        }

        /// <summary>
        /// Check whether content cn be handled.
        /// </summary>
        /// <returns>bool.</returns>
        public abstract bool Accept();

        private string GetAwsRegion()
        {
            return this.Configuration.GetSection("AWS")["Region"];
        }

        private bool GetUseAzureForAssets()
        {
            bool.TryParse(this.Configuration.GetSection("AssetDelivery")["UseAzure"], out var useAzure);
            return useAzure;
        }

        /// <summary>
        /// set context path.
        /// </summary>
        /// <param name="path">Processed content path.</param>
        public void SetContextPath(string path)
        {
            this.ContextPath = path;
        }

        /// <summary>
        /// set asset identifier.
        /// </summary>
        /// <param name="identifier">Processed content path.</param>
        public void SetResourceIdentifier(string identifier)
        {
            this.ResourceIdentifier = identifier;
            this.Dispatcher.ResourceIdentifier = identifier;
        }

        public async Task<bool> ProcessingAsync(string path)
        {
            var result = false;
            this.ContextPath = path;
            if (this.Accept())
            {
                result = await this.HandleContentAsync().ConfigureAwait(false);
            }

            return result;
        }

        /// <summary>
        /// GetResourceContent.
        /// </summary>
        /// <returns>string.</returns>
        protected async Task<string> GetResourceContentAsync()
        {
            return await this.GetResourceContentAsync(this.ContextPath).ConfigureAwait(false);
        }

        /// <summary>
        /// GetResourceContent.
        /// </summary>
        /// <param name="resourceName">Resource Name.</param>
        /// <returns>string.</returns>
        protected async Task<string> GetResourceContentAsync(string resourceName)
        {
            this.Logger.LogInformation("fetching resource " + resourceName);
            string resourceContainer = this.Settings.ContainerName;
            if (this.ContainerName != null)
            {
                resourceContainer = this.ContainerName;
            }

            var client = this.BlobClientProvider.Resolve(resourceContainer);

            if (!await client.ExistsAsync(resourceName))
            {
                throw new MissingResourceException(resourceContainer, resourceName);
            }

            var content = await client.GetItemAsync(resourceName);
            this.Logger.LogInformation("complete fetching resource " + resourceName);
            return content;
        }

        /// <summary>
        /// GetResourceContent.
        /// </summary>
        /// <param name="containerName">Container Name.</param>
        /// <param name="resourceName">Resource Name.</param>
        /// <param name="content">content.</param>
        /// <returns>string.</returns>
        protected async Task<string> WriteResourceContentAsync(string containerName, string resourceName, string content)
        {
            this.Logger.LogInformation("writing resource " + resourceName + " under " + containerName);
            var client = this.BlobClientProvider.Resolve(containerName);
            await client.CreateItemAsync(resourceName, content, "application/octet-stream").ConfigureAwait(false);

            this.Logger.LogInformation("complete writing resource " + resourceName);
            return resourceName;
        }

        /// <summary>
        /// CheckResourceExistsAsync.
        /// </summary>
        /// <param name="resourceName">Resource Name.</param>
        /// <returns>bool.</returns>
        protected async Task<bool> CheckResourceExistsAsync(string resourceName)
        {
            var client = this.BlobClientProvider.Resolve(this.Settings.ContainerName);
            return await client.ExistsAsync(resourceName);
        }

        /// <summary>
        /// GetResourceLink.
        /// </summary>
        /// <param name="resourceName">resourceName.</param>
        /// <param name="extension">extension.</param>
        /// <returns>string.</returns>
        protected string GetResourceLinkWithExtension([NotNull] string resourceName, [NotNull] string extension)
        {
            string pathWithoutExtension = resourceName;
            if (pathWithoutExtension.Contains(".", System.StringComparison.OrdinalIgnoreCase))
            {
                pathWithoutExtension = pathWithoutExtension.Substring(0, pathWithoutExtension.LastIndexOf(".", System.StringComparison.OrdinalIgnoreCase));
            }

            string fileName = pathWithoutExtension + "." + extension;
            return this.GetResourceLink(this.Settings.ContainerName, fileName);
        }

        /// <summary>
        /// GetResourceWithExtension.
        /// </summary>
        /// <param name="resourceName">resourceName.</param>
        /// <param name="extension">extension.</param>
        /// <returns>string.</returns>
        protected string GetResourceWithExtension([NotNull] string resourceName, [NotNull] string extension)
        {
            string pathWithoutExtension = resourceName;
            if (pathWithoutExtension.Contains(".", System.StringComparison.OrdinalIgnoreCase))
            {
                pathWithoutExtension = pathWithoutExtension.Substring(0, pathWithoutExtension.LastIndexOf(".", System.StringComparison.OrdinalIgnoreCase));
            }

            string fileName = pathWithoutExtension + "." + extension;
            return fileName;
        }

        /// <summary>
        /// GetAssetPath.
        /// </summary>
        /// <param name="assetName">assetName.</param>
        /// <returns>the resource link with prefix.</returns>
        protected string GetAssetPath(string assetName)
        {
            string prefix = this.ContextPath.Substring(0, this.ContextPath.LastIndexOf("/", System.StringComparison.OrdinalIgnoreCase));
            return prefix + "/" + assetName;
        }

        protected string EnsureResourceLink(string containerName, string fullUrl)
        {
            var str = this.GetResourceLink(containerName, "");
            var relativeUrl = fullUrl.Replace(str, string.Empty, StringComparison.InvariantCultureIgnoreCase);
            return GetResourceLink(containerName, relativeUrl);
        }

        protected string GetResourceLink(string containerName, string resourceName)
        {
            if (resourceName.StartsWith("/"))
            {
                resourceName = resourceName.Substring(1);
            }

            if (GetUseAzureForAssets())
            {
                var translatedContainer = this.Configuration.GetSection("AssetDelivery").GetSection("BucketToContainer")[this.Settings.ContainerName];
                return "https://" + this.Settings.AccountName + ".blob.core.windows.net/" + translatedContainer + "/" + resourceName;
            }
            else
            {
                var region = GetAwsRegion();
                return $"https://{containerName}.s3.{region}.amazonaws.com/{resourceName}";
            }
        }
    }
}
