// "//-----------------------------------------------------------------------".
// <copyright file="CnNbaTvContentHandler.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Application.Dispatcher.ContentHandler
{
    /// <summary>
    /// CnNbaTvContentHandler.
    /// </summary>
    public class CnNbaTvContentHandler : NbaTvContentHandler
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="CnNbaTvContentHandler"/> class.
        /// </summary>
        public CnNbaTvContentHandler()
        {
            this.FolderMatchType = "cn-nbatv";
        }

        /// <summary>
        /// accept.
        /// </summary>
        /// <returns>bool.</returns>
        public override bool Accept()
        {
            if (this.MatchFolder("cn-nbatv") && this.EndsWith(".xml"))
            {
                return true;
            }

            return false;
        }
    }
}
