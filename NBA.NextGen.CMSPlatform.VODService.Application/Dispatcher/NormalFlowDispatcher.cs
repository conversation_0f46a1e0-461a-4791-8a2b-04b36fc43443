namespace NBA.NextGen.CMSPlatform.VODService.Application.Dispatcher;

using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using MST.Common.Data;
using MST.Common.Messaging;
using NBA.NextGen.CMSPlatform.VODService.Application.Dispatcher.ContentHandler;
using NBA.NextGen.CMSPlatform.VODService.Application.Services;
using NBA.NextGen.CMSPlatform.VODService.Domain.Config;

public class NormalFlowDispatcher : DispatcherBase
{

    public NormalFlowDispatcher(IDataStoreFactory objectStorageFactory, ILogger<NormalFlowDispatcher> logger, IOptions<NormalFlowSettings> settings, IAzureVideoIndexerService videoIndexerService, IObjectRepositoryFactory repositoryFactory, IMessageSenderFactory queueClientProvider, IVodFileRegistryService vodFileRegistryService, IVodAssetRegistryService vodAssetRegistryService, IAWSTranscriptionOrchestrator aWSTranscriptionOrchestrator, IConfiguration configuration)
        : base(objectStorageFactory, logger, settings, repositoryFactory, queueClientProvider, vodFileRegistryService, vodAssetRegistryService, configuration)
    {
        AbsBlobContentHandler inhouseContentHandler = new NbaeInhouseContentHandler();
        AbsBlobContentHandler wscFastTrackContentHandler = new WscFasttrackContentHandler();
        AbsBlobContentHandler wscContentHandler = new WscContentHandler();
        AbsBlobContentHandler cnnbaTvContentHandler = new CnNbaTvContentHandler();
        AbsBlobContentHandler nbalpGameContentHandler = new NbaeLpGameContentHandler();
        AbsBlobContentHandler gameContentHandler = new NbaTvGameContentHandler();
        AbsBlobContentHandler nbatvSeries = new NbaTvSeriesContentHandler();
        NorthboundMetadataContentHandler northboundMetadataContentHandler = new NorthboundMetadataContentHandler();
        NbaeInhouseCaptionRequiredContentHandler inhouseCaptionRequiredContentHandler = new NbaeInhouseCaptionRequiredContentHandler();
        WscRegionContentHandler wscRegionContentHandler = new WscRegionContentHandler();
        NbaBetContentHandler nbaBetContentHandler = new NbaBetContentHandler();
        NbaAffiliateContentHandler nbaAffiliateContentHandler = new NbaAffiliateContentHandler();

        cnnbaTvContentHandler.ConsumerEndpoint = "/private/api/v1/manage-nba-video/services/nbatv/from-function/video/create/";
        nbatvSeries.ConsumerEndpoint = "/private/api/v1/manage-nba-video/services/nbatv/from-function/video/create/";
        gameContentHandler.ConsumerEndpoint = "/private/api/v1/manage-nba-video/services/nbatv/from-function/video/create/";
        nbalpGameContentHandler.ConsumerEndpoint = "/private/api/v1/manage-nba-video/services/nbatv/from-function/video/create/";
        inhouseContentHandler.ConsumerEndpoint = "/private/api/v1/manage-nba-video/services/in-house/from-function/video/create/";
        inhouseCaptionRequiredContentHandler.ConsumerEndpoint = "/private/api/v1/manage-nba-video/services/in-house/from-function/video/create/";
        northboundMetadataContentHandler.ConsumerEndpoint = "/private/api/v1/manage-nba-video/services/automated/nbad-northbound-meta/from-function/video/create/";
        wscFastTrackContentHandler.ConsumerEndpoint = "/private/api/v1/manage-nba-video/services/wsc/from-fasttrack/video/create/";
        wscContentHandler.ConsumerEndpoint = "/private/api/v1/manage-nba-video/services/wsc/highlights/from-function/video/create";
        wscRegionContentHandler.ConsumerEndpoint = "/private/api/v1/manage-nba-video/services/wsc/highlights/from-function/video/create";
        nbaBetContentHandler.ConsumerEndpoint = "/private/api/v1/manage-nba-video/services/nba-bet/from-function/video/create/";
        nbaAffiliateContentHandler.ConsumerEndpoint = "/private/api/v1/manage-nba-video/services/third-party/from-function/video/create/";
        
        inhouseCaptionRequiredContentHandler.TranscriptionOrchestrator = aWSTranscriptionOrchestrator;
        northboundMetadataContentHandler.TranscriptionOrchestrator = aWSTranscriptionOrchestrator;
        nbaBetContentHandler.TranscriptionOrchestrator = aWSTranscriptionOrchestrator;
        nbaAffiliateContentHandler.TranscriptionOrchestrator = aWSTranscriptionOrchestrator;

        this.AddHandler(inhouseContentHandler);
        this.AddHandler(wscFastTrackContentHandler);
        this.AddHandler(wscContentHandler);
        this.AddHandler(nbalpGameContentHandler);
        this.AddHandler(gameContentHandler);
        this.AddHandler(nbatvSeries);
        this.AddHandler(northboundMetadataContentHandler);
        this.AddHandler(inhouseCaptionRequiredContentHandler);
        this.AddHandler(wscRegionContentHandler);
        this.AddHandler(nbaBetContentHandler);
        this.AddHandler(nbaAffiliateContentHandler);
    }
}
