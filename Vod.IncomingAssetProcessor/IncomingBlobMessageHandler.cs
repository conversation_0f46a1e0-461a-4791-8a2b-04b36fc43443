using Microsoft.Extensions.Options;
using MST.Common.Data;
using MST.Common.Messaging;
using NBA.NextGen.CMSPlatform.VODService.Application.Dispatcher.ContentHandler;
using NBA.NextGen.CMSPlatform.VODService.Application.Services;
using NBA.NextGen.CMSPlatform.VODService.Domain.Common;
using NBA.NextGen.CMSPlatform.VODService.Domain.Common.Enums;
using NBA.NextGen.CMSPlatform.VODService.Domain.Config;
using Vod.Common.Data;
using Vod.Common.Models;
using Vod.IncomingAssetProcessor.Strategies;
using Vod.IncomingAssetProcessor.Strategies.Factory;

namespace Vod.IncomingAssetProcessor;

public class IncomingBlobMessageHandler : IMessageHandler
{
    private readonly IVodFileRegistryService _vodFileRegistryService;
    private readonly IVodAssetRegistryService _vodAssetRegistryService;
    private readonly ILogger _logger;
    private readonly IDispatcherStrategyFactory _factory;
    private readonly IDataStoreFactory _dataStoreFactory;
    private readonly IObjectRepository<DelayedResource> _delayedResourceRepository;
    private readonly IMessageSender<AssetCopyMessage> _queueClient;
    private readonly AssetCopySettings _assetCopySettings;
    public IncomingBlobMessageHandler(
        ILogger<IncomingBlobMessageHandler> logger,
        IDispatcherStrategyFactory factory,
        IVodFileRegistryService vodFileRegistryService,
        IVodAssetRegistryService vodAssetRegistryService,
        IObjectRepositoryFactory objectFactor,
        IMessageSenderFactory queueClientProvider,
        IOptions<AssetCopySettings> assetCopySettings,
        IDataStoreFactory dataStoreFactory
    )
    {
        _logger = logger;
        _factory = factory;
        _vodFileRegistryService = vodFileRegistryService;
        _vodAssetRegistryService = vodAssetRegistryService;
        _delayedResourceRepository = objectFactor.Resolve<DelayedResource>();
        _queueClient = queueClientProvider.Resolve<AssetCopyMessage>();
        _assetCopySettings = assetCopySettings.Value;
        _dataStoreFactory = dataStoreFactory;
    }
    public Task ProcessError(Exception exception)
    {
        _logger.LogError(exception, "IncomingBlobMessageHandler Queue Error: {ExceptionMessage}", exception.Message);
        return Task.CompletedTask;
    }

    public async Task ProcessMessage(ReceivedMessage message)
    {
        if (string.IsNullOrWhiteSpace(message.Content))
        {
            return;
        }

        var eventData = System.Text.Json.JsonSerializer.Deserialize<IncomingObjectEvent>(message.Content, new System.Text.Json.JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        });
        
        var key = eventData?.Detail?.Object?.Key;
        var bucketName = eventData?.Detail?.Bucket?.Name;
        if (key is null || bucketName is null)
        {
            _logger.LogError("No key or bucket name found on message: " + message.Content);
            return;
        }

        if (key.EndsWith(".json"))
        {
            var repo = _dataStoreFactory.Resolve(bucketName);
            if (repo is not null)
            {
                var sourceJson = await repo.GetItemAsync(key);
                if (sourceJson?.StartsWith("{\"jobName\":") is true)
                {
                    _logger.LogInformation($"Skipping {key} - Looks like a Transcribe Meta-data file...");
                    return;
                }
            }
        }

        var strategy = _factory.GetStrategy(bucketName);

        if (strategy is null)
        {
            _logger.LogWarning("No Strategy Found for: " + bucketName);
            message.ShouldDeadLetter = true;
            return;
        }

        if (_assetCopySettings.EnableProducer)
        {
            // TODO: send ContentType as part of this message
            var assetCopyMessage = new AssetCopyMessage()
            {
                BucketName = bucketName,
                FlowType = strategy.FlowType,
                Key = key
            };

            try
            {
                await _queueClient.SendAsync(assetCopyMessage);
                _logger.LogInformation($"Submit to asset copy queue, bucketName: {bucketName}, key: {key}");
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Submit to asset copy queue failed: " + key);
            }
        }

        if (strategy.FlowType == IngestionFlowTypes.NoOp)
        {
            return;
        }

        var success = await ProcessFileAsync(strategy, key, message.Content);
        message.ShouldDeadLetter = !success;
    }

    private async Task<bool> ProcessFileAsync(IDispatcherStrategy strategy, string key, string sourceEvent)
    {
        try
        {
            var resourceId = await strategy.ProcessFileAsync(key);
            if (resourceId is not null)
            {
                var message = $"{strategy.GetType().Name} processing result for resource {resourceId}@{key} successfully ";
                await _vodAssetRegistryService.AddAssetEventAsync(resourceId, AssetEventTypes.IngestionCompleted, DateTime.UtcNow, message);
                await _vodFileRegistryService.RegisterFileProcessedAsync(key, true, DateTime.UtcNow);
            }
            else
            {
                _logger.LogInformation($"{strategy.GetType().Name} skip processing for resource {key}");
                await _vodFileRegistryService.RegisterFileProcessedAsync(key, false, DateTime.UtcNow);
            }
            return true;
        }
        catch (Exception e)
        {
            var assetId = strategy.ActiveAssetId ?? "No Asset Id";
            if (e is MissingResourceException missingResourceException)
            {
                var errorMessage = $"Trigger File: {key}\nMissing File: {missingResourceException.MissingFile}";
                await _vodAssetRegistryService.AddAssetEventAsync(assetId, AssetEventTypes.IngestionBlockedAssetMissing, DateTime.UtcNow, errorMessage);
                if (!string.IsNullOrWhiteSpace(missingResourceException.MissingFile))
                {
                    var item = new DelayedResource
                    {
                        Id = Guid.NewGuid().ToString(),
                        AssetId = assetId,
                        CreatedAtUtc = DateTime.UtcNow,
                        LastCheckedAtUtc = DateTime.UtcNow,
                        Payload = System.Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes(sourceEvent)),
                        MissingFile = missingResourceException.MissingFile,
                        ContainerName = missingResourceException.ContainerName,
                        ThresholdAlertSent = false
                    };
                    await _delayedResourceRepository.CreateItemAsync(item);
                }
                return true;
            }

            var message = $"{strategy.GetType().Name} failed processing for resource {key} (AssetId: {assetId})";
            _logger.LogError(e, message);
            await _vodAssetRegistryService.AddAssetEventAsync(assetId, AssetEventTypes.IngestionFailed, DateTime.UtcNow, message);
            await _vodFileRegistryService.RegisterFileProcessedAsync(key, true, DateTime.UtcNow, e);
            return false;
        }
    }
}
