
using NBA.NextGen.CMSPlatform.VODService.Application.Dispatcher;
using NBA.NextGen.CMSPlatform.VODService.Application.Services;
using NBA.NextGen.CMSPlatform.VODService.Domain.Common.Enums;

namespace Vod.IncomingAssetProcessor.Strategies;

public abstract class BaseFlowStrategy : IDispatcherStrategy
{
    public string? ActiveAssetId => _dispatcher.ResourceIdentifier;
    public abstract IngestionFlowTypes FlowType { get; }
    private readonly DispatcherBase _dispatcher;
    private readonly string _bucketName;
    private readonly IVodFileRegistryService _vodFileRegistryService;

    public BaseFlowStrategy(DispatcherBase dispatcher, IVodFileRegistryService vodFileRegistryService, string bucketName)
    {
        _dispatcher = dispatcher;
        _bucketName = bucketName;
        _vodFileRegistryService = vodFileRegistryService;
    }

    public bool CanProcess(string bucketName)
    {
        return _bucketName.Equals(bucketName, StringComparison.OrdinalIgnoreCase);
    }

    public async virtual Task<string?> ProcessFileAsync(string key)
    {
        var parentFolder = key.Substring(0, key.IndexOf("/", 1));
        await _vodFileRegistryService.RecordFileArrivalAsync(key, parentFolder, FlowType, DateTime.UtcNow);

        return await _dispatcher.SimpleProcessingAsync(key);
    }
}
