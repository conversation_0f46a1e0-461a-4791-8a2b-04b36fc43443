namespace Vod.IncomingAssetProcessor.Strategies.Factory;

public class DispatcherStrategyFactory : IDispatcherStrategyFactory
{
    private readonly IEnumerable<IDispatcherStrategy> _strategies;
    public DispatcherStrategyFactory(IEnumerable<IDispatcherStrategy> strategies)
    {
        _strategies = strategies;
    }
    public IDispatcherStrategy? GetStrategy(string url)
    {
        return _strategies.SingleOrDefault(s => s.CanProcess(url));
    }

}

