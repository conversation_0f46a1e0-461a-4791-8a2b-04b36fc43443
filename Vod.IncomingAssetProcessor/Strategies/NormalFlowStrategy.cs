using Microsoft.Extensions.Options;
using NBA.NextGen.CMSPlatform.VODService.Application.Dispatcher;
using NBA.NextGen.CMSPlatform.VODService.Application.Services;
using NBA.NextGen.CMSPlatform.VODService.Domain.Common.Enums;
using NBA.NextGen.CMSPlatform.VODService.Domain.Config;

namespace Vod.IncomingAssetProcessor.Strategies;

public class NormalFlowStrategy : BaseFlowStrategy
{
    public NormalFlowStrategy(NormalFlowDispatcher dispatcher, IVodFileRegistryService vodFileRegistryService, IOptions<NormalFlowSettings> settings)
    : base(dispatcher, vodFileRegistryService, settings.Value.ContainerName ?? throw new ArgumentNullException(nameof(settings.Value.ContainerName)))
    {
    }

    public override IngestionFlowTypes FlowType => IngestionFlowTypes.NormalFlow;
}