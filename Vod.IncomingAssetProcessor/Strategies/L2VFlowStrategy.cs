using Microsoft.Extensions.Options;
using NBA.NextGen.CMSPlatform.VODService.Application.Dispatcher;
using NBA.NextGen.CMSPlatform.VODService.Application.Services;
using NBA.NextGen.CMSPlatform.VODService.Domain.Common.Enums;
using NBA.NextGen.CMSPlatform.VODService.Domain.Config;

namespace Vod.IncomingAssetProcessor.Strategies;

public class L2VFlowStrategy : BaseFlowStrategy
{
    public L2VFlowStrategy(LiveToVodDispatcher dispatcher, IVodFileRegistryService vodFileRegistryService, IOptions<LiveToVodSettings> settings)
    : base(dispatcher, vodFileRegistryService, settings.Value.ContainerName ?? throw new ArgumentNullException(nameof(settings.Value.ContainerName)))
    {
    }

    public override IngestionFlowTypes FlowType => IngestionFlowTypes.LiveToVod;
}