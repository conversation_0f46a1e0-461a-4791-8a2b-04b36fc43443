using Microsoft.Extensions.Options;
using NBA.NextGen.CMSPlatform.VODService.Application.Dispatcher;
using NBA.NextGen.CMSPlatform.VODService.Application.Services;
using NBA.NextGen.CMSPlatform.VODService.Domain.Common.Enums;
using NBA.NextGen.CMSPlatform.VODService.Domain.Config;

namespace Vod.IncomingAssetProcessor.Strategies;

public class DirectFlowGeneratedStrategy : BaseFlowStrategy
{
    public DirectFlowGeneratedStrategy(DirectFlowDispatcher dispatcher, IVodFileRegistryService vodFileRegistryService, IOptions<VcmsStorageSettings> settings)
    : base(dispatcher, vodFileRegistryService, settings.Value.ContainerName ?? throw new ArgumentNullException(nameof(settings.Value.ContainerName)))
    {
    }

    public override IngestionFlowTypes FlowType => IngestionFlowTypes.NoOp;

    public override Task<string?> ProcessFileAsync(string url)
    {
        return Task.FromResult<string?>(string.Empty);
    }
}