using System.Diagnostics.CodeAnalysis;
using MST.Common.AWS.Extensions;
using MST.Common.Azure.Extensions;
using Vod.Common.Extensions;
using Vod.IncomingAssetProcessor.Strategies;
using Vod.IncomingAssetProcessor.Strategies.Factory;

namespace Vod.IncomingAssetProcessor.Extensions;

[ExcludeFromCodeCoverage]
public static class ApplicationExtensions
{
    public static IServiceCollection AddApplicationDependencies(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddSharedApplicationDependencies(configuration);
        return services;
    }

    public static IServiceCollection AddApplicationHandlers(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddSingleton<IDispatcherStrategy, DirectFlowStrategy>();
        services.AddSingleton<IDispatcherStrategy, DirectFlowGeneratedStrategy>();
        services.AddSingleton<IDispatcherStrategy, L2VFlowStrategy>();
        services.AddSingleton<IDispatcherStrategy, NormalFlowStrategy>();
        services.AddSingleton<IDispatcherStrategyFactory, DispatcherStrategyFactory>();

        var queueName = configuration.GetSection("AWSQueueNames")["IncomingBlob"] ?? throw new NullReferenceException();
        services.RegisterSQSConsumer<IncomingBlobMessageHandler>(queueName);
        
        return services;
    }
}