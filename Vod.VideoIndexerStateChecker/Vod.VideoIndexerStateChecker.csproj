<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <OutputType>Exe</OutputType>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.Extensions.Hosting" Version="8.0.1" />
    </ItemGroup>

    <ItemGroup>
        <Content Include="..\config\*.json">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </Content>
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\NBA.NextGen.CMSPlatform.VODService.Infrastructure\NBA.NextGen.CMSPlatform.VODService.Infrastructure.csproj" />
        <ProjectReference Include="..\Vod.Common\Vod.Common.csproj" />
    </ItemGroup>
</Project>
