using System;
using System.Diagnostics.CodeAnalysis;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Vod.Common.Extensions;

namespace Vod.VideoIndexerStateChecker.Extensions;

[ExcludeFromCodeCoverage]
public static class ApplicationExtensions
{
    public static IServiceCollection AddApplicationDependencies(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddSharedApplicationDependencies(configuration);
        services.AddSingleton<VideoIndexerJob>();
        return services;
    }

    public static IServiceCollection AddApplicationFactories(this IServiceCollection services)
    {
        return services;
    }

    public static IServiceCollection AddApplicationHandlers(this IServiceCollection services)
    {
        return services;
    }

    public static IServiceCollection AddApplicationConfiguration(this IServiceCollection services, IConfiguration configuration)
    {
        return services;
    }

    public static IServiceCollection AddApplicationRepositories(this IServiceCollection services)
    {
        return services;
    }
}