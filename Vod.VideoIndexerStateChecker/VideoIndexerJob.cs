using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using MST.Common.Data;
using NBA.NextGen.CMSPlatform.VODService.Application.Dispatcher;
using NBA.NextGen.CMSPlatform.VODService.Application.Dispatcher.ContentHandler;
using NBA.NextGen.CMSPlatform.VODService.Application.Services;
using NBA.NextGen.CMSPlatform.VODService.Domain.Common.Enums;
using NBA.NextGen.CMSPlatform.VODService.Domain.Config;
using NBA.NextGen.CMSPlatform.VODService.Domain.Utilities;
using Newtonsoft.Json.Linq;

namespace Vod.VideoIndexerStateChecker;

public class VideoIndexerJob
{
    private readonly IObjectRepository<AssetCacheItem> repository;
    private readonly NormalFlowDispatcher normalFlowDispatcher;
    private readonly IVodAssetRegistryService vodAssetRegistryService;
    private readonly IAzureVideoIndexerService videoIndexerService;

    private readonly VideoIndexerSettings settings;

    private readonly ILogger logger;

    public VideoIndexerJob(NormalFlowDispatcher normalFlowDispatcher, IOptions<VideoIndexerSettings> settings, IObjectRepositoryFactory factory, IVodAssetRegistryService vodAssetRegistryService, IAzureVideoIndexerService videoIndexerService, ILogger<VideoIndexerJob> logger)
    {
        this.normalFlowDispatcher = normalFlowDispatcher;
        this.repository = factory.Resolve<AssetCacheItem>();
        this.vodAssetRegistryService = vodAssetRegistryService;
        this.videoIndexerService = videoIndexerService;
        this.settings = settings.Value;
        this.logger = logger;
    }

    public Task RunAsync()
    {
        logger.LogInformation($"Video Indexer Job Checker Disabled");
        return Task.CompletedTask;
    }
}
