image: node:20.14-slim

definitions:
  steps:
    - step: &buildImage
        name: "Build Azure Images"
        size: 2x
        services:
          - docker
        script:
          - source set_env.sh
          - source .gitops/init.sh
          - source .gitops/vars.sh
          - export currentApplication=vod-api-service
          - export dockerPath=Vod.Api/Dockerfile
          - source .gitops/image-vars.sh
          - /bin/bash .gitops/build-image.sh
          - export currentApplication=vod-ecms-processor
          - export dockerPath=Vod.EcmsProcessor/Dockerfile
          - source .gitops/image-vars.sh
          - /bin/bash .gitops/build-image.sh
          - export currentApplication=vod-incomingasset-processor
          - export dockerPath=Vod.IncomingAssetProcessor/Dockerfile
          - source .gitops/image-vars.sh
          - /bin/bash .gitops/build-image.sh
          - export currentApplication=vod-vcms-processor
          - export dockerPath=Vod.VcmsProcessor/Dockerfile
          - source .gitops/image-vars.sh
          - /bin/bash .gitops/build-image.sh
          - export currentApplication=vod-videoindexer-statechecker
          - export dockerPath=Vod.VideoIndexerStateChecker/Dockerfile
          - source .gitops/image-vars.sh
          - /bin/bash .gitops/build-image.sh
          - export currentApplication=vod-delayedresource-checker
          - export dockerPath=Vod.DelayedResourceMonitor/Dockerfile
          - source .gitops/image-vars.sh
          - /bin/bash .gitops/build-image.sh
          - export currentApplication=vod-mediaconvert-cron
          - export dockerPath=Vod.MediaConvertCron/Dockerfile
          - source .gitops/image-vars.sh
          - /bin/bash .gitops/build-image.sh
          - export currentApplication=vod-transcribe-cron
          - export dockerPath=Vod.TranscribeCron/Dockerfile
          - source .gitops/image-vars.sh
          - /bin/bash .gitops/build-image.sh
          - export currentApplication=vod-incomingassetcopy-processor
          - export dockerPath=Vod.IncomingAssetCopyProcessor/Dockerfile
          - source .gitops/image-vars.sh
          - /bin/bash .gitops/build-image.sh

    - step: &pushToRegistry
        name: "Push to ACR"
        size: 2x
        services:
          - docker
        runs-on:
          - 'self.hosted'
          - 'docker'
        script:
          - source set_env.sh
          - source .gitops/init.sh
          - source .gitops/vars.sh
          - export currentApplication=vod-api-service
          - source .gitops/image-vars.sh
          - source .gitops/publish-ecr-acr.sh
          - export currentApplication=vod-ecms-processor
          - source .gitops/image-vars.sh
          - source .gitops/publish-ecr-acr.sh
          - export currentApplication=vod-incomingasset-processor
          - source .gitops/image-vars.sh
          - source .gitops/publish-ecr-acr.sh
          - export currentApplication=vod-vcms-processor
          - source .gitops/image-vars.sh
          - source .gitops/publish-ecr-acr.sh
          - export currentApplication=vod-videoindexer-statechecker
          - source .gitops/image-vars.sh
          - source .gitops/publish-ecr-acr.sh
          - export currentApplication=vod-delayedresource-checker
          - source .gitops/image-vars.sh
          - source .gitops/publish-ecr-acr.sh
          - export currentApplication=vod-mediaconvert-cron
          - source .gitops/image-vars.sh
          - source .gitops/publish-ecr-acr.sh
          - export currentApplication=vod-transcribe-cron
          - source .gitops/image-vars.sh
          - source .gitops/publish-ecr-acr.sh
          - export currentApplication=vod-incomingassetcopy-processor
          - source .gitops/image-vars.sh
          - source .gitops/publish-ecr-acr.sh

    - step: &ChangeImageTagAzDO
        name: "Change Azure Image Tag AzDO"
        size: 2x
        services:
          - docker
        runs-on:
          - 'self.hosted'
          - 'docker'
        script:
          - source set_env.sh
          - source .gitops/init.sh
          - source .gitops/vars.sh
          - /bin/bash .gitops/install.sh
          - export currentApplication=vod-api-service
          - source .gitops/image-vars.sh
          - /bin/bash .gitops/update-deploy.sh
          - export currentApplication=vod-ecms-processor
          - source .gitops/image-vars.sh
          - /bin/bash .gitops/update-deploy.sh
          - export currentApplication=vod-incomingasset-processor
          - source .gitops/image-vars.sh
          - /bin/bash .gitops/update-deploy.sh
          - export currentApplication=vod-vcms-processor
          - source .gitops/image-vars.sh
          - /bin/bash .gitops/update-deploy.sh

    - step: &checkMarx
          name: 'Runs an On-Demand Incremental Checkmarx Scan on a private runner'
          runs-on:
            - 'self.hosted'
            - 'linux'
            - 'docker'
            - 'checkmarx'
          services:
            - docker
          script:
            - source set_env.sh
            - echo 'This step will run on a self-hosted Linux Shell'
            - apt update
            - apt install curl zip -y
            - apt install default-jdk -y && dpkg --configure -a
            - export JAVA_HOME=/usr/lib/jvm/java-11-openjdk-amd64
            - export PATH=${JAVA_HOME}/bin:$PATH
            - mkdir /tmp/cx-cli-dir
            - curl https://nba-devops.s3.amazonaws.com/install-files/CxConsolePlugin-1.1.38.zip -o "/tmp/cx-cli-dir/cx-cli.zip"
            - unzip -o /tmp/cx-cli-dir/cx-cli.zip -d /tmp/cx-cli-dir/cx-cli
            - ENV="${BITBUCKET_BRANCH}"
            - if [[ "${ENV}" == "master" ]] || [[ "${ENV}" == "main" ]]; then ENV="prod"; fi
            - >-
              /bin/bash /tmp/cx-cli-dir/cx-cli/runCxConsole.sh Scan -cxServer "https://checkmarx.nba-hq.com" -cxUser "${CX_USER}" -cxPassword "${CX_PASSWORD}"
              -cxSastUrl https://checkmarx.nba-hq.com/ -cxSastUser "${CX_USER}" -cxSastPass "${CX_PASSWORD}" -enableSca -scaUsername "${CX_USER_SCA}"
              -scaPassword "${CX_PASSWORD_SCA}" -scaAccount NBA -projectName "CxServer\Digital Product Development\MediaOps VOD ${env}"
              -locationType "folder" -locationPath "${PWD}/" -includeExcludePattern "!**/test/e2e/**/*, !**/.gitgnore/**/*, !**/.gradle/**/*, !**/.checkstyle/**/*, !**/.classpath/**/*,
              !**/bin/**/*, !**/obj/**/*, !**/backup/**/*, !**/.idea/**/*, !**/*.DS_Store, !**/*.ipr, !**/*.iws, !**/*.bak, !**/*.tmp, !**/*.aac, !**/*.aif, !**/*.iff,
              !**/*.m3u, !**/*.mid, !**/*.mp3, !**/*.mpa, !**/*.ra, !**/*.wav, !**/*.wma, !**/*.3g2, !**/*.3gp, !**/*.asf, !**/*.asx, !**/*.avi, !**/*.flv, !**/*.mov,
              !**/*.mp4, !**/*.mpg, !**/*.rm, !**/*.swf, !**/*.vob, !**/*.wmv, !**/*.bmp, !**/*.gif, !**/*.jpg, !**/*.png, !**/*.psd, !**/*.tif, !**/*.swf, !**/*.jar,
              !**/*.zip, !**/*.rar, !**/*.exe, !**/*.dll, !**/*.pdb, !**/*.7z, !**/*.gz, !**/*.tar.gz, !**/*.tar, !**/*.gz, !**/*.ahtm, !**/*.ahtml, !**/*.fhtml,
              !**/*.hdm, !**/*.hdml, !**/*.hsql, !**/*.ht, !**/*.hta, !**/*.htc, !**/*.htd, !**/*.war, !**/*.ear, !**/*.htmls, !**/*.ihtml, !**/*.mht, !**/*.mhtm,
              !**/*.mhtml, !**/*.ssi, !**/*.stm, !**/*.bin,!**/*.lock,!**/*.svg,!**/*.obj, !**/*.stml, !**/*.ttml, !**/*.txn, !**/*.xhtm, !**/*.xhtml, !**/*.class,
              !**/*.iml, !Checkmarx/Reports/*.*, !OSADependencies.json, !**/node_modules/**/*, !**/*.test.js" -Incremental -PeriodicFullScan "10" -SASTHigh "0" -SASTMedium "10"
              -SCAHigh "0" -SCAMedium "10"

    - step: &invictiScan
        image: alpine:3.22.0
        name: "Invicti Scan"
        services:
          - docker
        script:
          - apk add bash curl jq
          - source set_env.sh
          - /bin/bash .gitops/invicti_cicd.sh api_key=${MDO_INVICTI_API_KEY} website_name="${WEBSITE_NAME}"

    - step: &terraformPlan
        name: "Plan Cloud Resources Deployment with Terraform"
        oidc: true
        image: hashicorp/terraform:latest
        script:
          - env=$BITBUCKET_BRANCH
          - source .gitops/aws-vars.sh
          - cd Infrastructure
          - TFBACK="${env}.aws.tfbackend"
          - TFVAR="${env}.tfvars"
          - terraform init -backend-config="backends/${TFBACK}"
          - terraform plan -var-file="./environments/${TFVAR}" -out=tfplan.binary
          - echo "Terraform plan completed. Review changes before approving the apply step"
        artifacts:
          - Infrastructure/tfplan.binary

    - step: &Terraform-Lint-Test-and-Validate
        name: Terraform Linting and Validation
        image: hashicorp/terraform:latest
        script:
          - cd ./Infrastructure
          - terraform fmt --check --recursive
          - terraform init --backend=false
          - terraform validate
pipelines:
  pull-requests:
    '**':
        - step: *Terraform-Lint-Test-and-Validate
  branches:
    definitions:
      services:
        docker:
          memory: 2120
    main:
      - step:
          name: 'Set Environment Variables'
          script:
            - env="${BITBUCKET_BRANCH}"
            - echo -e export KUBE_REPO="nba-com-aks-kube-prod" >> set_env.sh
            - echo -e export NEW_RELIC_LOG_LEVEL="error" >> set_env.sh
            - if [ "${env}" == "feature/addCheckmarxPROD" ]; then ttag="test"; fi
            - echo -e export ttag="${ttag}" >> set_env.sh
            - echo -e export env=prod >> set_env.sh
          artifacts:
            - set_env.sh
      - parallel:
          fail-fast: true
          steps:
            - step: *checkMarx
            - step: *buildImage

      - step:
          name: Slack Notification
          script:
            - set +e
            - echo "Sending Slack Notification"
            - pipe: atlassian/slack-notify:2.1.0
              variables:
                WEBHOOK_URL: "*******************************************************************************"
                SLACK_CHANNEL: "devops-alerts-prod-deployments"
                SLACK_USERNAME: "VOD"
                MESSAGE: "You are about to Deploy VOD Image to PRODUCTION environment ACR from ${BITBUCKET_BRANCH} branch,\nWith the commit message: $(git log --format=%B -n 1 $BITBUCKET_COMMIT).\nBitbucket Pipleline URL: $BITBUCKET_GIT_HTTP_ORIGIN/pipelines/results/${BITBUCKET_BUILD_NUMBER}"
            - set -e
      - step:
          name: "Approval"
          trigger: manual
          script:
            - echo "Proceeding after Manual Approval"
      - step: *pushToRegistry
      - step: *ChangeImageTagAzDO

  custom:
    deploy-iac:
      - step:
          name: "Dev - Plan Cloud Resources Deployment with Terraform"
          oidc: true
          image: hashicorp/terraform:latest
          script:
            - source .gitops/aws-vars.sh
            - cd Infrastructure
            - terraform init -backend-config="backends/dev.aws.tfbackend"
            - terraform plan -var-file="./environments/dev.tfvars" -out=tfplan-dev.binary
            - echo "Terraform plan completed. Review changes before approving the apply step"
          artifacts:
            - Infrastructure/tfplan-dev.binary
      - step:
          image: hashicorp/terraform:latest
          name: 'Dev - Review and Approve Terraform Changes'
          trigger: manual
          oidc: true
          script:
            - source .gitops/aws-vars.sh
            - cd Infrastructure
            - terraform init -backend-config="backends/dev.aws.tfbackend"
            - echo "Applying Terraform plan..."
            - terraform apply -auto-approve tfplan-dev.binary
      - step:
          image: node:20.14-slim
          name: "Dev - Initialize DocumentDB Collections"
          runs-on:
            - 'self.hosted'
            - 'linux'
            - 'docker'
          trigger: manual
          oidc: true
          script:
            - apt-get update && apt-get install -y wget unzip
            - wget https://releases.hashicorp.com/terraform/1.11.4/terraform_1.11.4_linux_amd64.zip
            - unzip terraform_1.11.4_linux_amd64.zip && mv terraform /usr/local/bin/
            - source .gitops/aws-vars.sh
            - cd Infrastructure
            - terraform init -backend-config="backends/dev.aws.tfbackend"
            - export DOCUMENTDB_USERNAME=$(terraform output -raw docdb_username)
            - export DOCUMENTDB_PASSWORD=$(terraform output -raw docdb_password)
            - export DOCUMENTDB_ENDPOINT=$(terraform output -raw docdb_cluster_endpoint)
            - export DOCUMENTDB_DATABASE="dev-vod-main"
            - cd ../scripts/documentdb-init
            - npm install
            - npm run build
            - npm run start
      - step:
          name: "QA - Plan Cloud Resources Deployment with Terraform"
          oidc: true
          image: hashicorp/terraform:latest
          script:
            - source .gitops/aws-vars.sh
            - cd Infrastructure
            - terraform init -backend-config="backends/qa.aws.tfbackend"
            - terraform plan -var-file="./environments/qa.tfvars" -out=tfplan-qa.binary
            - echo "Terraform plan completed. Review changes before approving the apply step"
          artifacts:
            - Infrastructure/tfplan-qa.binary
      - step:
          image: hashicorp/terraform:latest
          name: 'QA - Review and Approve Terraform Changes'
          trigger: manual
          oidc: true
          script:
            - source .gitops/aws-vars.sh
            - cd Infrastructure
            - terraform init -backend-config="backends/qa.aws.tfbackend"
            - echo "Applying Terraform plan..."
            - terraform apply -auto-approve tfplan-qa.binary
      - step:
          image: node:20.14-slim
          name: "QA - Initialize DocumentDB Collections"
          runs-on:
            - 'self.hosted'
            - 'linux'
            - 'docker'
          trigger: manual
          oidc: true
          script:
            - apt-get update && apt-get install -y wget unzip
            - wget https://releases.hashicorp.com/terraform/1.11.4/terraform_1.11.4_linux_amd64.zip
            - unzip terraform_1.11.4_linux_amd64.zip && mv terraform /usr/local/bin/
            - source .gitops/aws-vars.sh
            - cd Infrastructure
            - terraform init -backend-config="backends/qa.aws.tfbackend"
            - export DOCUMENTDB_USERNAME=$(terraform output -raw docdb_username)
            - export DOCUMENTDB_PASSWORD=$(terraform output -raw docdb_password)
            - export DOCUMENTDB_ENDPOINT=$(terraform output -raw docdb_cluster_endpoint)
            - export DOCUMENTDB_DATABASE="qa-vod-main"
            - cd ../scripts/documentdb-init
            - npm install
            - npm run build
            - npm run start
      - step:
          name: "Prod - Plan Cloud Resources Deployment with Terraform"
          oidc: true
          image: hashicorp/terraform:latest
          script:
            - env="prod"
            - source .gitops/aws-vars.sh
            - cd Infrastructure
            - terraform init -backend-config="backends/prod.aws.tfbackend"
            - terraform plan -var-file="./environments/prod.tfvars" -out=tfplan-prod.binary
            - echo "Terraform plan completed. Review changes before approving the apply step"
          artifacts:
            - Infrastructure/tfplan-prod.binary
      - step:
          image: hashicorp/terraform:latest
          name: 'Prod - Review and Approve Terraform Changes'
          trigger: manual
          oidc: true
          script:
            - env="prod"
            - source .gitops/aws-vars.sh
            - cd Infrastructure
            - terraform init -backend-config="backends/prod.aws.tfbackend"
            - echo "Applying Terraform plan..."
            - terraform apply -auto-approve tfplan-prod.binary
      - step:
          image: node:20.14-slim
          name: "Prod - Initialize DocumentDB Collections"
          runs-on:
            - 'self.hosted'
            - 'linux'
            - 'docker'
          trigger: manual
          oidc: true
          script:
            - env="prod"
            - apt-get update && apt-get install -y wget unzip
            - wget https://releases.hashicorp.com/terraform/1.11.4/terraform_1.11.4_linux_amd64.zip
            - unzip terraform_1.11.4_linux_amd64.zip && mv terraform /usr/local/bin/
            - source .gitops/aws-vars.sh
            - cd Infrastructure
            - terraform init -backend-config="backends/prod.aws.tfbackend"
            - export DOCUMENTDB_USERNAME=$(terraform output -raw docdb_username)
            - export DOCUMENTDB_PASSWORD=$(terraform output -raw docdb_password)
            - export DOCUMENTDB_ENDPOINT=$(terraform output -raw docdb_cluster_endpoint)
            - export DOCUMENTDB_DATABASE="prod-vod-main"
            - cd ../scripts/documentdb-init
            - npm install
            - npm run build
            - npm run start

####################################################
# Leaving these pipelines here for testing purposes.    
    invicti-scan-dev:
      - step:
          name: 'Set Environment'
          script:
            - echo -e export "WEBSITE_NAME=\"VOD MT (Dev)\"" >> set_env.sh
          artifacts:
            - set_env.sh
      - step: *invictiScan

    invicti-scan-qa:
      - step:
          name: 'Set Environment'
          script:
            - echo -e export "WEBSITE_NAME=\"VOD MT (QA)\"" >> set_env.sh
          artifacts:
            - set_env.sh
      - step: *invictiScan

    invicti-scan-prod:
      - step:
          name: 'Set Environment'
          script:
            - echo -e export "WEBSITE_NAME=\"VOD MT (Prod)\"" >> set_env.sh
          artifacts:
            - set_env.sh
      - step: *invictiScan
####################################################

    deploy-to-dev:
      - step:
          name: 'Set Environments Name'
          script:
            - env="dev"
            - echo "${env}"
            - NEW_RELIC_LOG_LEVEL="info"
            - echo "${NEW_RELIC_LOG_LEVEL}"
            - echo -e export ttag="${ttag}" >> set_env.sh
            - echo -e export env="${env}" >> set_env.sh
            - echo -e export NEW_RELIC_LOG_LEVEL="${NEW_RELIC_LOG_LEVEL}" >> set_env.sh
            - echo -e export "WEBSITE_NAME=\"VOD MT (Dev)\"" >> set_env.sh
            - echo -e export
          artifacts:
            - set_env.sh
      - step:
          name: Slack Notification
          script:
            - set +e
            - echo "Sending Slack Notification"
            - pipe: atlassian/slack-notify:2.1.0
              variables:
                WEBHOOK_URL: "*******************************************************************************"
                SLACK_CHANNEL: "devops-alerts-deployment"
                SLACK_USERNAME: "VOD"
                MESSAGE: "Build and push VOD images to Dev ECR from ${BITBUCKET_BRANCH} branch,\nWith the commit message: $(git log --format=%B -n 1 $BITBUCKET_COMMIT).\nBitbucket Pipleline URL: $BITBUCKET_GIT_HTTP_ORIGIN/pipelines/results/${BITBUCKET_BUILD_NUMBER}"
            - set -e
      - parallel:
          fail-fast: true
          steps:
            - step: *checkMarx
            - step: *invictiScan
      - parallel:
          fail-fast: true
          steps:
            - step:
                name: "Build AWS Image - Vod.Api"
                size: 2x
                services:
                  - docker
                script:
                  - source .gitops/build-and-push-image.sh 'vod-api-service' 'Vod.Api/Dockerfile'
            - step:
                name: "Build AWS Image - Vod.DelayedResourceMonitor"
                size: 2x
                services:
                  - docker
                script:
                  - source .gitops/build-and-push-image.sh 'vod-delayedresource-checker' 'Vod.DelayedResourceMonitor/Dockerfile'
            - step:
                name: "Build AWS Image - Vod.EcmsProcessor"
                size: 2x
                services:
                  - docker
                script:
                  - source .gitops/build-and-push-image.sh 'vod-ecms-processor' 'Vod.EcmsProcessor/Dockerfile'
            - step:
                name: "Build AWS Image - Vod.IncomingAssetProcessor"
                size: 2x
                services:
                  - docker
                script:
                  - source .gitops/build-and-push-image.sh 'vod-incomingasset-processor' 'Vod.IncomingAssetProcessor/Dockerfile'
            - step:
                name: "Build AWS Image - Vod.MediaConvertCron"
                size: 2x
                services:
                  - docker
                script:
                  - source .gitops/build-and-push-image.sh 'vod-mediaconvert-cron' 'Vod.MediaConvertCron/Dockerfile'
            - step:
                name: "Build AWS Image - Vod.TranscribeCron"
                size: 2x
                services:
                  - docker
                script:
                  - source .gitops/build-and-push-image.sh 'vod-transcribe-cron' 'Vod.TranscribeCron/Dockerfile'
            - step:
                name: "Build AWS Image - Vod.VcmsProcessor"
                size: 2x
                services:
                  - docker
                script:
                  - source .gitops/build-and-push-image.sh 'vod-vcms-processor' 'Vod.VcmsProcessor/Dockerfile'
            - step:
                name: "Build AWS Image - Vod.IncomingAssetCopyProcessor"
                size: 2x
                services:
                  - docker
                script:
                  - source .gitops/build-and-push-image.sh 'vod-incomingassetcopy-processor' 'Vod.IncomingAssetCopyProcessor/Dockerfile'
    deploy-to-qa:
      - step:
          name: 'Set Environments Name'
          script:
            - env="qa"
            - echo "${env}"
            - NEW_RELIC_LOG_LEVEL="info"
            - echo "${NEW_RELIC_LOG_LEVEL}"
            - echo -e export ttag="${ttag}" >> set_env.sh
            - echo -e export env="${env}" >> set_env.sh
            - echo -e export NEW_RELIC_LOG_LEVEL="${NEW_RELIC_LOG_LEVEL}" >> set_env.sh
            - echo -e export "WEBSITE_NAME=\"VOD MT (QA)\"" >> set_env.sh
            - echo -e export
          artifacts:
            - set_env.sh
      - step:
          name: Slack Notification
          script:
            - set +e
            - echo "Sending Slack Notification"
            - pipe: atlassian/slack-notify:2.1.0
              variables:
                WEBHOOK_URL: "*******************************************************************************"
                SLACK_CHANNEL: "devops-alerts-deployment"
                SLACK_USERNAME: "VOD"
                MESSAGE: "Build and push VOD images to QA ECR from ${BITBUCKET_BRANCH} branch,\nWith the commit message: $(git log --format=%B -n 1 $BITBUCKET_COMMIT).\nBitbucket Pipleline URL: $BITBUCKET_GIT_HTTP_ORIGIN/pipelines/results/${BITBUCKET_BUILD_NUMBER}"
            - set -e
      - parallel:
          fail-fast: true
          steps:
            - step: *checkMarx
            - step: *invictiScan
      - parallel:
          fail-fast: true
          steps:
            - step:
                name: "Build AWS Image - Vod.Api"
                size: 2x
                services:
                  - docker
                script:
                  - source .gitops/build-and-push-image.sh 'vod-api-service' 'Vod.Api/Dockerfile'
            - step:
                name: "Build AWS Image - Vod.DelayedResourceMonitor"
                size: 2x
                services:
                  - docker
                script:
                  - source .gitops/build-and-push-image.sh 'vod-delayedresource-checker' 'Vod.DelayedResourceMonitor/Dockerfile'
            - step:
                name: "Build AWS Image - Vod.EcmsProcessor"
                size: 2x
                services:
                  - docker
                script:
                  - source .gitops/build-and-push-image.sh 'vod-ecms-processor' 'Vod.EcmsProcessor/Dockerfile'
            - step:
                name: "Build AWS Image - Vod.IncomingAssetProcessor"
                size: 2x
                services:
                  - docker
                script:
                  - source .gitops/build-and-push-image.sh 'vod-incomingasset-processor' 'Vod.IncomingAssetProcessor/Dockerfile'
            - step:
                name: "Build AWS Image - Vod.MediaConvertCron"
                size: 2x
                services:
                  - docker
                script:
                  - source .gitops/build-and-push-image.sh 'vod-mediaconvert-cron' 'Vod.MediaConvertCron/Dockerfile'
            - step:
                name: "Build AWS Image - Vod.TranscribeCron"
                size: 2x
                services:
                  - docker
                script:
                  - source .gitops/build-and-push-image.sh 'vod-transcribe-cron' 'Vod.TranscribeCron/Dockerfile'
            - step:
                name: "Build AWS Image - Vod.VcmsProcessor"
                size: 2x
                services:
                  - docker
                script:
                  - source .gitops/build-and-push-image.sh 'vod-vcms-processor' 'Vod.VcmsProcessor/Dockerfile'
            - step:
                name: "Build AWS Image - Vod.IncomingAssetCopyProcessor"
                size: 2x
                services:
                  - docker
                script:
                  - source .gitops/build-and-push-image.sh 'vod-incomingassetcopy-processor' 'Vod.IncomingAssetCopyProcessor/Dockerfile'
    deploy-prod-iac:
      - step:
          name: "Prod - Plan Cloud Resources Deployment with Terraform"
          oidc: true
          image: hashicorp/terraform:latest
          script:
            - env="prod"
            - source .gitops/aws-vars.sh
            - cd Infrastructure
            - terraform init -backend-config="backends/prod.aws.tfbackend"
            - terraform plan -var-file="./environments/prod.tfvars" -out=tfplan-prod.binary
            - echo "Terraform plan completed. Review changes before approving the apply step"
          artifacts:
            - Infrastructure/tfplan-prod.binary
      - step:
          image: hashicorp/terraform:latest
          name: 'Prod - Review and Approve Terraform Changes'
          trigger: manual
          oidc: true
          script:
            - env="prod"
            - source .gitops/aws-vars.sh
            - cd Infrastructure
            - terraform init -backend-config="backends/prod.aws.tfbackend"
            - echo "Applying Terraform plan..."
            - terraform apply -auto-approve tfplan-prod.binary
      - step:
          image: node:20.14-slim
          name: "Prod - Initialize DocumentDB Collections"
          runs-on:
            - 'self.hosted'
            - 'linux'
            - 'docker'
          trigger: manual
          oidc: true
          script:
            - env="prod"
            - apt-get update && apt-get install -y wget unzip
            - wget https://releases.hashicorp.com/terraform/1.11.4/terraform_1.11.4_linux_amd64.zip
            - unzip terraform_1.11.4_linux_amd64.zip && mv terraform /usr/local/bin/
            - source .gitops/aws-vars.sh
            - cd Infrastructure
            - terraform init -backend-config="backends/prod.aws.tfbackend"
            - export DOCUMENTDB_USERNAME=$(terraform output -raw docdb_username)
            - export DOCUMENTDB_PASSWORD=$(terraform output -raw docdb_password)
            - export DOCUMENTDB_ENDPOINT=$(terraform output -raw docdb_cluster_endpoint)
            - export DOCUMENTDB_DATABASE="prod-vod-main"
            - cd ../scripts/documentdb-init
            - npm install
            - npm run build
            - npm run start
    deploy-to-prod:
      - step:
          name: 'Set Environments Name'
          script:
            - env="prod"
            - echo "${env}"
            - NEW_RELIC_LOG_LEVEL="info"
            - echo "${NEW_RELIC_LOG_LEVEL}"
            - echo -e export ttag="${ttag}" >> set_env.sh
            - echo -e export env="${env}" >> set_env.sh
            - echo -e export NEW_RELIC_LOG_LEVEL="${NEW_RELIC_LOG_LEVEL}" >> set_env.sh
            - echo -e export "WEBSITE_NAME=\"VOD MT (Prod)\"" >> set_env.sh
            - echo -e export
          artifacts:
            - set_env.sh
      - step:
          name: Slack Notification
          script:
            - set +e
            - echo "Sending Slack Notification"
            - pipe: atlassian/slack-notify:2.1.0
              variables:
                WEBHOOK_URL: "*******************************************************************************"
                SLACK_CHANNEL: "devops-alerts-deployment"
                SLACK_USERNAME: "VOD"
                MESSAGE: "Build and push VOD images to Prod ECR from ${BITBUCKET_BRANCH} branch,\nWith the commit message: $(git log --format=%B -n 1 $BITBUCKET_COMMIT).\nBitbucket Pipleline URL: $BITBUCKET_GIT_HTTP_ORIGIN/pipelines/results/${BITBUCKET_BUILD_NUMBER}"
            - set -e
      - parallel:
          fail-fast: true
          steps:
            - step: *checkMarx
            - step: *invictiScan
      - parallel:
          fail-fast: true
          steps:
            - step:
                name: "Build AWS Image - Vod.Api"
                size: 2x
                services:
                  - docker
                script:
                  - source .gitops/build-and-push-image.sh 'vod-api-service' 'Vod.Api/Dockerfile'
            - step:
                name: "Build AWS Image - Vod.DelayedResourceMonitor"
                size: 2x
                services:
                  - docker
                script:
                  - source .gitops/build-and-push-image.sh 'vod-delayedresource-checker' 'Vod.DelayedResourceMonitor/Dockerfile'
            - step:
                name: "Build AWS Image - Vod.EcmsProcessor"
                size: 2x
                services:
                  - docker
                script:
                  - source .gitops/build-and-push-image.sh 'vod-ecms-processor' 'Vod.EcmsProcessor/Dockerfile'
            - step:
                name: "Build AWS Image - Vod.IncomingAssetProcessor"
                size: 2x
                services:
                  - docker
                script:
                  - source .gitops/build-and-push-image.sh 'vod-incomingasset-processor' 'Vod.IncomingAssetProcessor/Dockerfile'
            - step:
                name: "Build AWS Image - Vod.MediaConvertCron"
                size: 2x
                services:
                  - docker
                script:
                  - source .gitops/build-and-push-image.sh 'vod-mediaconvert-cron' 'Vod.MediaConvertCron/Dockerfile'
            - step:
                name: "Build AWS Image - Vod.TranscribeCron"
                size: 2x
                services:
                  - docker
                script:
                  - source .gitops/build-and-push-image.sh 'vod-transcribe-cron' 'Vod.TranscribeCron/Dockerfile'
            - step:
                name: "Build AWS Image - Vod.VcmsProcessor"
                size: 2x
                services:
                  - docker
                script:
                  - source .gitops/build-and-push-image.sh 'vod-vcms-processor' 'Vod.VcmsProcessor/Dockerfile'
            - step:
                name: "Build AWS Image - Vod.IncomingAssetCopyProcessor"
                size: 2x
                services:
                  - docker
                script:
                  - source .gitops/build-and-push-image.sh 'vod-incomingassetcopy-processor' 'Vod.IncomingAssetCopyProcessor/Dockerfile'
    nightly-full-checkmarx-scans:
     - step:
          name: 'Runs a Nightly Checkmarx Scan on a private runner'
          runs-on:
            - 'self.hosted'
            - 'linux'
            - 'docker'
            - 'checkmarx'
          services:
            - docker
          script:
            - echo 'This step will run on a self-hosted Linux Shell'
            - apt update
            - apt install curl zip -y
            - apt install default-jdk -y && dpkg --configure -a
            - export JAVA_HOME=/usr/lib/jvm/java-11-openjdk-amd64
            - export PATH=${JAVA_HOME}/bin:$PATH
            - mkdir /tmp/cx-cli-dir
            - curl https://nba-devops.s3.amazonaws.com/install-files/CxConsolePlugin-1.1.38.zip -o "/tmp/cx-cli-dir/cx-cli.zip"
            - unzip -o /tmp/cx-cli-dir/cx-cli.zip -d /tmp/cx-cli-dir/cx-cli
            - ENV="${BITBUCKET_BRANCH}"
            - if [[ "${ENV}" == "master" ]] || [[ "${ENV}" == "main" ]]; then ENV="prod"; fi
            - >-
              /bin/bash /tmp/cx-cli-dir/cx-cli/runCxConsole.sh Scan -cxServer "https://checkmarx.nba-hq.com" -cxUser "${CX_USER}" -cxPassword "${CX_PASSWORD}"
              -cxSastUrl https://checkmarx.nba-hq.com/ -cxSastUser "${CX_USER}" -cxSastPass "${CX_PASSWORD}" -enableSca -scaUsername "${CX_USER_SCA}"
              -scaPassword "${CX_PASSWORD_SCA}" -scaAccount NBA -projectName "CxServer\Digital Product Development\MediaOps VOD  ${ENV^^}"
              -locationType "folder" -locationPath "${PWD}/" -includeExcludePattern "!**/test/e2e/**/*, !**/.gitgnore/**/*, !**/.gradle/**/*, !**/.checkstyle/**/*, !**/.classpath/**/*,
              !**/bin/**/*, !**/obj/**/*, !**/backup/**/*, !**/.idea/**/*, !**/*.DS_Store, !**/*.ipr, !**/*.iws, !**/*.bak, !**/*.tmp, !**/*.aac, !**/*.aif, !**/*.iff,
              !**/*.m3u, !**/*.mid, !**/*.mp3, !**/*.mpa, !**/*.ra, !**/*.wav, !**/*.wma, !**/*.3g2, !**/*.3gp, !**/*.asf, !**/*.asx, !**/*.avi, !**/*.flv, !**/*.mov,
              !**/*.mp4, !**/*.mpg, !**/*.rm, !**/*.swf, !**/*.vob, !**/*.wmv, !**/*.bmp, !**/*.gif, !**/*.jpg, !**/*.png, !**/*.psd, !**/*.tif, !**/*.swf, !**/*.jar,
              !**/*.zip, !**/*.rar, !**/*.exe, !**/*.dll, !**/*.pdb, !**/*.7z, !**/*.gz, !**/*.tar.gz, !**/*.tar, !**/*.gz, !**/*.ahtm, !**/*.ahtml, !**/*.fhtml,
              !**/*.hdm, !**/*.hdml, !**/*.hsql, !**/*.ht, !**/*.hta, !**/*.htc, !**/*.htd, !**/*.war, !**/*.ear, !**/*.htmls, !**/*.ihtml, !**/*.mht, !**/*.mhtm,
              !**/*.mhtml, !**/*.ssi, !**/*.stm, !**/*.bin,!**/*.lock,!**/*.svg,!**/*.obj, !**/*.stml, !**/*.ttml, !**/*.txn, !**/*.xhtm, !**/*.xhtml, !**/*.class,
              !**/*.iml, !Checkmarx/Reports/*.*, !OSADependencies.json, !**/node_modules/**/*, !**/*.test.js" -ForceScan -SASTHigh "0" -SASTMedium "10" -SCAHigh "0" -SCAMedium "10"
   
