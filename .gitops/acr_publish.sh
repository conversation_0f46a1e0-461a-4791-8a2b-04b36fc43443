#!/bin/bash
apt update
apt install curl zip -y
curl -sL https://aka.ms/InstallAzureCLIDeb | bash
az login --service-principal -u $AZURE_CLIENT_ID -p $AZURE_CLIENT_SECRET --tenant $AZURE_TENANT_ID
az account show
export TTT=$(az keyvault secret show --name $ACR_SP_APP_ID --vault-name $AZURE_VAULT --query "value")
export KVACR_SP_APP_ID=${TTT//\"/}
export TTT=$(az keyvault secret show --name $ACR_SP_SECRET --vault-name $AZURE_VAULT --query "value")
export KVACR_SP_SECRET=${TTT//\"/}
echo ">>>>>Pushing with vars:"
echo KVACR_SP_APP_ID:${#KVACR_SP_APP_ID}
echo KVACR_SP_SECRET:${#KVACR_SP_SECRET}
echo ACR_IMAGE_TAG:$ACR_IMAGE_TAG
echo "<<<<<<<<"
az acr login -n $ACR --username $KVACR_SP_APP_ID --password $KVACR_SP_SECRET
docker push $ACR_IMAGE_TAG