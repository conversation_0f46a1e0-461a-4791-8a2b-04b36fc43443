#!/bin/bash


# Parse named arguments
for ARG in "$@"
do
  case $ARG in
    api_key=*)
      API_KEY="${ARG#*=}"
      shift
      ;;
    website_name=*)
      WEBSITE_NAME="${ARG#*=}"
      shift
      ;;
    *)
      echo "Unknown argument: $ARG"
      exit 1
      ;;
  esac
done

# Validate input
if [ -z "$API_KEY" ] || [ -z "$WEBSITE_NAME" ]; then
    echo "Usage: $0 api_key=<api_key> website_name=<website_name>"
    exit 1
fi


AUTH_HEADER="Authorization: Basic $API_KEY"

# Config
BASE_URL="https://ie.invicti.com/api/1.0/issues/allissues"
PAGE=1
TOTAL_PAGES=1

# Counters
critical_count=0
high_count=0
medium_count=0
low_count=0

# Function to URL-encode the website name
urlencode() {
  local raw="$1"
  local encoded=""
  local i c

  for (( i = 0; i < ${#raw}; i++ )); do
    c=${raw:$i:1}
    case "$c" in
      [a-zA-Z0-9.~_-]) encoded+="$c" ;;
      *) printf -v encoded "%s%%%02X" "$encoded" "'$c" ;;
    esac
  done
  echo "$encoded"
}

ENCODED_WEBSITE_NAME=$(urlencode "$WEBSITE_NAME")

while [ $PAGE -le $TOTAL_PAGES ]; do
  RESPONSE=$(curl --path-as-is -s -w "HTTPSTATUS:%{http_code}" -X 'GET' \
    -H 'Host: ie.invicti.com' \
    -H "$AUTH_HEADER" \
    "$BASE_URL?pageSize=100&webSiteName=${ENCODED_WEBSITE_NAME}&page=$PAGE&sortType=Descending")

  # Split body and status
  BODY=$(echo "$RESPONSE" | sed -e 's/HTTPSTATUS\:.*//g')
  STATUS=$(echo "$RESPONSE" | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')

  if [ "$STATUS" -ne 200 ]; then
    echo "Error: API request failed with HTTP status $STATUS"
    exit 1
  fi

  # Get page count (only on first page)
  if [ $PAGE -eq 1 ]; then
    TOTAL_PAGES=$(echo "$BODY" | jq -r '.PageCount')
    LATEST_SCAN_ID=$(echo "$BODY" | jq -r '.List[0].LatestScanId // empty')

  fi

  # Count severities from current page
  page_counts=$(echo "$BODY" | jq -r '
    .List[] |
    select(.IsAddressed == false) |
    select(.Severity | test("^(Critical|High|Medium|Low)$"; "i")) |
    .Severity' | sort | uniq -c)

  # Parse counts
  while read -r count severity; do
    case "$severity" in
      Critical) critical_count=$((critical_count + count)) ;;
      High) high_count=$((high_count + count)) ;;
      Medium) medium_count=$((medium_count + count)) ;;
      Low) low_count=$((low_count + count)) ;;
    esac
  done <<< "$page_counts"

  PAGE=$((PAGE + 1))
done

# Final Output
if [ $((critical_count + high_count + medium_count)) -eq 0 ]; then
  VALIDATION_URL="https://ie.invicti.com/api/1.0/websites/get?query=$ENCODED_WEBSITE_NAME"
  VALIDATION_STATUS=$(curl -s -o /dev/null -w "%{http_code}" -k -X 'GET' \
    -H 'Host: ie.invicti.com' \
    -H "$AUTH_HEADER" \
    "$VALIDATION_URL")
  if [ "$VALIDATION_STATUS" -eq 200 ]; then
    echo "Error: No vulnerabilities reported by Invicti. Ensure that this application has been scanned. Reach out to CIPHER for assistance. "
  elif [ "$VALIDATION_STATUS" -eq 404 ]; then
    echo "Error: No application found matching '$WEBSITE_NAME'. Please check for a typo."
  else
    echo "Unexpected error while validating application name (HTTP status: $VALIDATION_STATUS)"
  fi
else
  echo "--------------------Invicti DAST Scan Results--------------------"
  echo "  Critical Severity Results: $critical_count"
  echo "  High Severity Results:     $high_count"
  echo "  Medium Severity Results:   $medium_count"
  echo "  Low Severity Results:      $low_count"
  echo "     "
  echo "  Scan Results Location: https://ie.invicti.com/scans/report/$LATEST_SCAN_ID/"
  echo "-----------------------------------------------------------------"
fi

