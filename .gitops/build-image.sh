#!/bin/bash
echo ">>>>> Building with tag"
echo TAG:$TAG
echo ENV:$ENV
echo TEMP_TAG:$TEMP_TAG
echo NEW_RELIC_LICENSE_KEY:$NEW_RELIC_LICENSE_KEY
echo NEW_RELIC_LOG_LEVEL:$NEW_RELIC_LOG_LEVEL
echo DOCKERPATH:$dockerPath

# Prep the build
set -eux


time docker build -t $NAME:$TAG --build-arg BUILD_ENV="${ENV}" --build-arg BUILD_NUMBER="${BITBUCKET_BUILD_NUMBER}" --build-arg NEW_RELIC_APP_NAME="VOD-${NAME}-${ENV}" --build-arg NEW_RELIC_LICENSE_KEY="${NEW_RELIC_LICENSE_KEY}" --build-arg NEW_RELIC_ENABLED=true --build-arg NEW_RELIC_DISTRIBUTED_TRACING_ENABLED=true --build-arg NEW_RELIC_LOG_LEVEL="${NEW_RELIC_LOG_LEVEL}" --build-arg FEED_ACCESSTOKEN="${ADO_ACCESS_TOKEN}" -f ${dockerPath} .
#time docker tag $NAME:$TAG $IMAGE:$TAG
#time docker save --output nba-next-image.docker $IMAGE:$TAG
#time docker push $IMAGE:$TAG

##Push to temporary registry
time docker tag $NAME:$TAG $TEMP_IMAGE:$TEMP_TAG
## WIZ Scan
# Update wiz_scan = "false" if you wish to skip the wiz scan
wiz_scan="true" 
curl -o wizcli https://wizcli.app.wiz.io/latest/wizcli && chmod +x wizcli
./wizcli auth --id $CLIENT_ID --secret $CLIENT_SECRET
if [[ $wiz_scan == "true" ]]; then ./wizcli docker scan --image $TEMP_IMAGE:$TEMP_TAG --policy "NBA-Critical-Vulnerability-Policy" --policy "NBA-High-Vulnerability-Policy" --policy "NBA-Medium-Vulnerability-Policy"; else echo "SKIPPING WIZ SCAN " ;fi 

time docker push $TEMP_IMAGE:$TEMP_TAG
