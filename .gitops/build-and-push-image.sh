#!/bin/bash

set_app(){
    source set_env.sh
    export currentApplication=$1
    export dockerPath=$2
    echo $currentApplication
    source .gitops/init.sh
    source .gitops/vars.sh
    source .gitops/image-vars.sh 
}

# Function to print build information
print_build_info() {
    echo ">>>>> Building with tag"
    echo TAG:$TAG
    echo ENV:$ENV
    echo TEMP_TAG:$TEMP_TAG
    # echo NEW_RELIC_LICENSE_KEY:$NEW_RELIC_LICENSE_KEY
    # echo NEW_RELIC_LOG_LEVEL:$NEW_RELIC_LOG_LEVEL
    echo DOCKERPATH:$dockerPath
}

# Function to prepare the build environment
prep_build() {
    set -eux
}

# Function to build the Docker image
build_docker_image() {
    time docker build -t $NAME:$TAG \
        --build-arg BUILD_ENV="${ENV}" \
        --build-arg BUILD_NUMBER="${BITBUCKET_BUILD_NUMBER}" \
        --build-arg FEED_ACCESSTOKEN="${ADO_ACCESS_TOKEN}" \
        -f ${dockerPath} .
    cd ..
}

# Function to tag and push the Docker image to a temporary registry
push_temp_registry() {
    time docker tag $NAME:$TAG $TEMP_IMAGE:$TEMP_TAG
    echo $NAME:$TAG $TEMP_IMAGE:$TEMP_TAG
    time docker push $TEMP_IMAGE:$TEMP_TAG
}

push_ecr() {
    time docker tag $NAME:$TAG $ECR_IMAGE:$TAG
    echo $NAME:$TAG $ECR_IMAGE:$TAG
    time docker push $ECR_IMAGE:$TAG
}


# Function to perform WIZ scan
perform_wiz_scan() {
    local wiz_scan="true"
    curl -o wizcli https://wizcli.app.wiz.io/latest/wizcli && chmod +x wizcli
    ./wizcli auth --id $CLIENT_ID --secret $CLIENT_SECRET
    if [[ $wiz_scan == "true" ]]; then
        ./wizcli docker scan --image $TEMP_IMAGE:$TEMP_TAG \
            --policy "NBA-Critical-Vulnerability-Policy" \
            --policy "NBA-High-Vulnerability-Policy" \
            --policy "NBA-Medium-Vulnerability-Policy"
    else
        echo "SKIPPING WIZ SCAN"
    fi
}

# Main script execution
set_app $1 $2
print_build_info
prep_build
build_docker_image
push_temp_registry
push_ecr
perform_wiz_scan
