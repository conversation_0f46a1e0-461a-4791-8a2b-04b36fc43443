#!/bin/bash -x
echo ">>>>>> Step Start >>>>>>>"
TIMESTAMP=$(date +%d-%B-%y--%R)
echo ">>>>>> Updating with vars:"
echo TIMESTAMP:$TIMESTAMP
echo TAG:$TAG
echo ACR-TAG:$ACR_IMAGE:$TAG
echo "<<<<"
echo KUBE_REPO:$KUBE_REPO
echo KUBE_REPO_CUS:$KUBE_REPO_CUS
echo ARGO_APP_FILE:$ARGO_APP_FILE
echo "<<<<<<<<<<<<<"
echo ">>>>>> Login >>>>>>>"
az login --service-principal -u $AZURE_CLIENT_ID -p $AZURE_CLIENT_SECRET --tenant $AZURE_TENANT_ID
az account show
echo "<<<<<<<<<<<<<"
export ZZZ=$(az keyvault secret show --name $AZDO_REPO_KEY --vault-name $AZURE_VAULT --query "value")
export REPO_KEY=${ZZZ//\"/}
if [ "${ENV}" == "prod" ]; then 
    export ZZZ=$(az keyvault secret show --name $AZDO_REPO_PROD_KEY --vault-name $AZURE_VAULT --query "value")
    export REPO_KEY=${ZZZ//\"/}
else
    export ZZZ=$(az keyvault secret show --name $AZDO_REPO_KEY --vault-name $AZURE_VAULT --query "value")
    export REPO_KEY=${ZZZ//\"/}
fi
echo "<<<"
echo REPO_AZ_KEY:${#REPO_KEY}
echo ">>>>> SSH key and known hosts >>>>>>>>"
cd /tmp
mkdir -p ~/.ssh
ssh-keyscan ssh.dev.azure.com > my_known_hosts
(umask  077 ; echo $REPO_KEY | base64 -di > ~/.ssh/id_rsa_azdo)
cat my_known_hosts >> ~/.ssh/known_hosts
echo "<<<<<<<<<<<<<"
echo ">>>>> list root SSH  >>>>>>>>"
ls -la /root/.ssh/
echo "<<<<<<<<<<<<<"
echo ">>>>>>  GIT SSH Import  >>>>>>>"
export GIT_SSH_COMMAND="ssh -i ~/.ssh/id_rsa_azdo -o IdentitiesOnly=yes"
echo "<<<<<<<<<<<<<"
echo ">>>>>>  GIT Config  >>>>>>>"
git config --global user.email "<EMAIL>"
git config --global user.name "AZDO"
echo "<<<<<<<<<<<<<"
echo ">>>>>> GIT Clone >>>>>>>"
<NAME_EMAIL>:v3/nbadev/DTC/$KUBE_REPO
echo "<<<<<<<<<<<<<"
cd $KUBE_REPO
echo ">>>>>> GIT Current Branch >>>>>>>"
git branch --show-current
echo ">>>>>>>>>>>>>"
echo ">>>>>> GIT Pull >>>>>>>"
git pull
echo ">>>>>>>>>>>>>"
echo "<<<<<< PWD <<<<<<<"
pwd
echo ">>>>>>>>>>>>>"
echo ">>>>> list tmp  >>>>>>>>"
ls -la /tmp
echo "<<<<<<<<<<<<<"
echo ">>>>> list $KUBE_REPO  >>>>>>>>"
ls -la /tmp/$KUBE_REPO
echo ">>>>> cat Argo App Manifest >>>>>>>>"
cat /tmp/$KUBE_REPO/$ARGO_APP_FOLDER/$ARGO_APP_FILE
echo "<<<<<<<<<<<<<"
echo ">>>>> SED the values of Argo App file >>>>>>>>"
sed -i "/^[[:space:]]*tag:/ s/:.*/: $TAG/" /tmp/$KUBE_REPO/$ARGO_APP_FOLDER/$ARGO_APP_FILE
echo "<<<<<<<<<<<<<"
echo ">>>>> Check the Argo App file >>>>>>>>"
cat /tmp/$KUBE_REPO/$ARGO_APP_FOLDER/$ARGO_APP_FILE
echo "<<<<<<<<<<<<<"
echo ">>>>> GIT Push $KUBE_REPO >>>>>>>>"
git add . && git commit -m "Pushing $CURRENTAPP tag: $TAG Upd: $TIMESTAMP" && git push
echo "<<<<<<<<<<<<<"
########
########
#if [[ "${ENV}" == "prod" || "${ENV}" == "uat" ]]; then 
#    echo ">>>>> Switch to repo folder >>>>>>>>";
#    cd /tmp;
#    echo ">>>>>> GIT Clone CUS >>>>>>>";
#    <NAME_EMAIL>:v3/nbadev/DTC/$KUBE_REPO_CUS;
#    echo "<<<<<<<<<<<<<";
#    cd $KUBE_REPO_CUS;
#    echo ">>>>>> GIT Current Branch CUS>>>>>>>";
#    git branch --show-current;
#    echo ">>>>>>>>>>>>>";
#    echo ">>>>>> GIT Pull CUS >>>>>>>";
#    git pull;
#    echo ">>>>>>>>>>>>>";
#    echo "<<<<<< PWD <<<<<<<";
#    pwd;
#    echo ">>>>>>>>>>>>>";
#    echo ">>>>> list tmp  >>>>>>>>";
#    ls -la /tmp;
#    echo "<<<<<<<<<<<<<";
#    echo ">>>>> list $KUBE_REPO_CUS  >>>>>>>>";
#    ls -la /tmp/$KUBE_REPO_CUS;
#    echo ">>>>> cat Argo App Manifest >>>>>>>>";
#    cat /tmp/$KUBE_REPO_CUS/$ARGO_APP_FOLDER/$ARGO_APP_FILE;
#    echo "<<<<<<<<<<<<<";
#    echo ">>>>> SED the values of Argo App file >>>>>>>>";
#    sed -i "/^[[:space:]]*tag:/ s/:.*/: $TAG/" /tmp/$KUBE_REPO_CUS/$ARGO_APP_FOLDER/$ARGO_APP_FILE;
#    echo "<<<<<<<<<<<<<";
#    echo ">>>>> Check the Argo App file >>>>>>>>";
#    cat /tmp/$KUBE_REPO_CUS/$ARGO_APP_FOLDER/$ARGO_APP_FILE;
#    echo "<<<<<<<<<<<<<";
#    echo ">>>>> GIT Push $KUBE_REPO_CUS >>>>>>>>";
#    git add . && git commit -m "Pushing BlobApi tag: $TAG Upd: $TIMESTAMP" && git push;
#    echo "<<<<<<<<<<<<<";
#fi

