#!/bin/bash
export ENV="${env}"
export CURRENTAPP="${currentApplication}"
#ACR tags
if [[ "${ENV}" == "prod" ]] || [[ "${ENV}" == "ngprod" ]]; then 
    export ACR_IMAGE="${ACR_PROD}/devops/${CURRENTAPP}";
    export ACR=$ACR_PROD;
    export AZURE_CLIENT_ID=$AZURE_CLIENT_ID_PROD;
    export AZURE_CLIENT_SECRET=$AZURE_CLIENT_SECRET_PROD;
    export AZURE_TENANT_ID=$AZURE_TENANT_ID_PROD;
else
    export ACR_IMAGE="${ACR_DEV}/devops/${CURRENTAPP}";
    export ACR=$ACR_DEV;
fi
export ACR_IMAGE_TAG="$ACR_IMAGE:$TAG"

#Install az cli and log in to ACR
apt update
apt install curl zip -y
curl -sL https://aka.ms/InstallAzureCLIDeb | bash
#
az login --service-principal -u $AZURE_CLIENT_ID -p $AZURE_CLIENT_SECRET --tenant $AZURE_TENANT_ID
az account show
#
export TTT=$(az keyvault secret show --name $ACR_SP_APP_ID --vault-name $AZURE_VAULT --query "value")
export KVACR_SP_APP_ID=${TTT//\"/}
export TTT=$(az keyvault secret show --name $ACR_SP_SECRET --vault-name $AZURE_VAULT --query "value")
export KVACR_SP_SECRET=${TTT//\"/}

#Push the image to ACR
echo ">>>>> Pushing with vars:"
echo KVACR_SP_APP_ID:${#KVACR_SP_APP_ID}
echo KVACR_SP_SECRET:${#KVACR_SP_SECRET}
echo ACR_IMAGE_TAG:$ACR_IMAGE_TAG
echo "<<<<<"
#
az acr login -n $ACR --username $KVACR_SP_APP_ID --password $KVACR_SP_SECRET
#docker pull $IMAGE:$TAG
#docker load --input nba-next-image.docker

##Pull from temp registry and push to ECR and ACR
docker pull $TEMP_IMAGE:$TEMP_TAG

# No ECR for now
#docker tag $TEMP_IMAGE:$TEMP_TAG $IMAGE:$TAG
#docker push $IMAGE:$TAG 

docker tag $TEMP_IMAGE:$TEMP_TAG $ACR_IMAGE_TAG
docker push $ACR_IMAGE_TAG

##Remove the temp image
if  [[ "${ENV}" == "prod" ]] || [[ "${ENV}" == "ngprod" ]]; then
    aws ecr batch-delete-image --repository-name $TEMP_NAME --image-ids imageTag=$TEMP_TAG --region us-east-1 --profile prod
else
    aws ecr batch-delete-image --repository-name $TEMP_NAME --image-ids imageTag=$TEMP_TAG --region us-east-1
fi
