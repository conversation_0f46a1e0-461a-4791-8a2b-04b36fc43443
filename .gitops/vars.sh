#!/bin/bash

echo "${env}"
echo "${currentApplication}"
export ENV="${env}"
export TTAG="${ttag}"
#
if [[ "${ENV}" == "prod" ]] ; then 
    #export ENV="prod";
    export ECR="************.dkr.ecr.us-east-1.amazonaws.com"
else
    export ECR="************.dkr.ecr.us-east-1.amazonaws.com"
fi
#

echo ">>>>> Using variables"
echo ECR:$ECR
echo BRANCH:$ENV
echo "<<<<<"

if  [[ "${ENV}" == "prod" ]] || [[ "${ENV}" == "ngprod" ]]; then
    echo "Configuring AWS Credentials and Authenticate to ECR - PROD"
    aws configure set aws_access_key_id "${AWS_KEY}"
    aws configure set aws_secret_access_key "${AWS_SECRET}"
    aws sts assume-role --role-arn arn:aws:iam::************:role/lambda-cross-account-deploy --role-session-name NBANext-Session > sts.json
    AWS_ACCESS_KEY_ID=$(jq .Credentials.AccessKeyId sts.json|tr -d '"')
    AWS_SECRET_ACCESS_KEY=$(jq .Credentials.SecretAccessKey sts.json|tr -d '"')
    AWS_SESSION_TOKEN=$(jq .Credentials.SessionToken sts.json|tr -d '"')
    aws configure set aws_access_key_id "${AWS_ACCESS_KEY_ID}" --profile prod
    aws configure set aws_secret_access_key "${AWS_SECRET_ACCESS_KEY}" --profile prod
    aws configure set aws_session_token "${AWS_SESSION_TOKEN}" --profile prod
    aws ecr get-login-password --region us-east-1 --profile prod| docker login -u AWS --password-stdin $ECR
else    
    echo "Configuring AWS Credentials and Authenticate to ECR - DEV"
    aws configure set aws_access_key_id "${AWS_KEY}"
    aws configure set aws_secret_access_key "${AWS_SECRET}"
    aws ecr get-login-password --region us-east-1 | docker login -u AWS --password-stdin $ECR
fi
#
