#!/bin/bash

echo "${env}"
echo "${currentApplication}"
export CURRENTAPP="${currentApplication}"

export NAME="devops/$CURRENTAPP"
export IMAGE="$ECR/$NAME"

## Set Temporary registry
export TEMP_NAME="devops/nba-temp"
export TEMP_IMAGE="$ECR/$TEMP_NAME"
export ECR_IMAGE="$ECR/mst/$CURRENTAPP"
export TEMP_TAG="${CURRENTAPP}-${ENV}-$(date '+%Y%m%d')-${BITBUCKET_BUILD_NUMBER}"

export TAG="${ENV}-$(date '+%Y%m%d')-${BITBUCKET_BUILD_NUMBER}"
#
if [ "${TTAG}" == "test" ]
then
  export TAG="test-$(date '+%Y%m%d')-${BITBUCKET_BUILD_NUMBER}";
fi
#

echo ">>>>> Setting up deployment variables"
export ARGO_APP_FOLDER="src/apps/vod-api-service/app"
export ARGO_APP_FILE="application-${CURRENTAPP}.yaml"

echo ">>>>> Using variables"
echo ECR:$ECR
echo NAME:$NAME
echo IMAGE:$IMAGE
echo TAG:$TAG
echo "<<<<<"
