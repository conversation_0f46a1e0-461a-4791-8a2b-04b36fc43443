#!/bin/bash

echo "${env}"
unset AWS_ACCESS_KEY_ID
unset AWS_SECRET_ACCESS_KEY 
echo $BITBUCKET_STEP_OIDC_TOKEN > $(pwd)/web-identity-token
export AWS_WEB_IDENTITY_TOKEN_FILE=$(pwd)/web-identity-token

if [[ "${env}" == "prod" ]]; then
    export AWS_ROLE_ARN=arn:aws:iam::807554360695:role/nba-pdgbl-mst-vod-role001
else
    export AWS_ROLE_ARN=arn:aws:iam::004488441890:role/nba-dvgbl-mst-vod-role001
fi