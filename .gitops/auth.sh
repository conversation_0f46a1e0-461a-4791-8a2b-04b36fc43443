#!/bin/bash
echo ">>>>>> Step Start >>>>>>>"
TIMESTAMP=$(date +%d-%B-%y--%R)
apt-get update && apt-get install -yq git
git --version
echo "<<<<<<<<<<<<<"
echo ">>>>>> AzDo CLI Install >>>>>>>"
apt update && apt install curl zip -yq
curl -sL https://aka.ms/InstallAzureCLIDeb | bash
echo "<<<<<<<<<<<<<"
echo ">>>>>> AzDo CLI extension add >>>>>>>"
az extension add --name azure-devops --yes --allow-preview false
echo "<<<<<<<<<<<<<"
echo ">>>>>> Login >>>>>>>"
az login --service-principal -u $AZURE_CLIENT_ID -p $AZURE_CLIENT_SECRET --tenant $AZURE_TENANT_ID
az account show
echo "<<<<<<<<<<<<<"
export ZZZ=$(az keyvault secret show --name $AZDO_REPO_KEY --vault-name $AZURE_VAULT --query "value")
export REPO_KEY=${ZZZ//\"/}
if [ "${ENV}" == "prod" ]; then 
    export ZZZ=$(az keyvault secret show --name $AZDO_REPO_PROD_KEY --vault-name $AZURE_VAULT --query "value")
    export REPO_KEY=${ZZZ//\"/}
else
    export ZZZ=$(az keyvault secret show --name $AZDO_REPO_KEY --vault-name $AZURE_VAULT --query "value")
    export REPO_KEY=${ZZZ//\"/}
fi
echo "<<<"
echo REPO_AZ_KEY:${#REPO_KEY}
echo ">>>>> SSH Install >>>>>>>>"
apt-get update && apt-get -yq install ssh
echo "<<<<<<<<<<<<<"
echo ">>>>> SSH key and known hosts >>>>>>>>"
cd /tmp
mkdir -p ~/.ssh
ssh-keyscan ssh.dev.azure.com > my_known_hosts
(umask  077 ; echo $REPO_KEY | base64 -di > ~/.ssh/id_rsa_azdo)
cat my_known_hosts >> ~/.ssh/known_hosts
echo "<<<<<<<<<<<<<"
echo ">>>>> list root SSH  >>>>>>>>"
ls -la /root/.ssh/
echo "<<<<<<<<<<<<<"
echo ">>>>>>  GIT SSH Import  >>>>>>>"
export GIT_SSH_COMMAND="ssh -i ~/.ssh/id_rsa_azdo -o IdentitiesOnly=yes"
echo "<<<<<<<<<<<<<"
echo ">>>>>>  GIT Config  >>>>>>>"
git config --global user.email "<EMAIL>"
git config --global user.name "AZDO"
echo "<<<<<<<<<<<<<"
