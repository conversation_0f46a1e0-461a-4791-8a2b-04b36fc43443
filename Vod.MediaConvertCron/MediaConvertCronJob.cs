using Amazon.MediaConvert;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using MST.Common.Data;
using NBA.NextGen.CMSPlatform.VODService.Application.Services;
using NBA.NextGen.CMSPlatform.VODService.Domain.Common;

namespace Vod.MediaConvertCron;

public class MediaConvertCronJob
{
    private readonly IQueryableRepository<MediaConvertJob> _queryableRepository;
    private readonly IDataStoreFactory _dataStoreFactory;
    private readonly IAWSMediaConvertService _mediaConvertService;
    private readonly IAWSTranscriptionOrchestrator _transcriptionOrchestrator;
    private readonly ILogger<MediaConvertCronJob> _logger;

    public MediaConvertCronJob(
        IQueryableRepositoryFactory queryableFactory,
        IDataStoreFactory dataStoreFactory,
        IAWSMediaConvertService mediaConvertService,
        IAWSTranscriptionOrchestrator transcriptionOrchestrator,
        ILogger<MediaConvertCronJob> logger)
    {
        _queryableRepository = queryableFactory.Resolve<MediaConvertJob>();
        _mediaConvertService = mediaConvertService;
        _transcriptionOrchestrator = transcriptionOrchestrator;
        _logger = logger;
        _dataStoreFactory = dataStoreFactory;
    }

    public async Task RunAsync()
    {
        _logger.LogInformation("Starting MediaConvert job processor at {Time}", DateTime.UtcNow);

        try
        {
            var items = await _queryableRepository.GetItemsAsync(_ => true, 0, -1);

            if (items == null || items.Count() == 0)
            {
                _logger.LogInformation("No MediaConvert jobs found to process.");
                return;
            }

            _logger.LogInformation("Found {Count} MediaConvert job(s) to process.", items.Count());

            foreach (var item in items)
            {
                try
                {
                    _logger.LogInformation("Checking status for MediaConvert job ID: {JobId}", item.JobId);

                    var job = await _mediaConvertService.GetJobByIdAsync(item.JobId);
                    var jobStatus = job?.Status;
                    _logger.LogInformation("Status for job {JobId}: {Status}", item.JobId, jobStatus);

                    if (jobStatus == JobStatus.COMPLETE)
                    {
                        _logger.LogInformation("Advancing job {JobId} to transcription", item.JobId);
                        var result = await _transcriptionOrchestrator.AdvanceToTranscribeAsync(item.JobId);
                        if (result)
                        {
                            _logger.LogInformation("Successfully advanced job {JobId} to transcription", item.JobId);
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing MediaConvert job with ID {JobId}", item.JobId);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Fatal error during MediaConvert job processing.");
            throw;
        }

        _logger.LogInformation("Finished MediaConvert job processor at {Time}", DateTime.UtcNow);
    }
}