using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Vod.Common.Extensions;
using Vod.MediaConvertCron;
using Vod.MediaConvertCron.Extensions;

var builder = Host.CreateApplicationBuilder(args);
builder.Configuration
        .SetBasePath(AppDomain.CurrentDomain.BaseDirectory)
        .AddEnvironmentVariables();

var envName = builder.Configuration.GetValue<string>("ENV") ?? "qa";
builder.Configuration
        .AddAmazonSecretsManager(Environment.GetEnvironmentVariable("AWS_REGION") ?? "us-east-1",
                Environment.GetEnvironmentVariable("ENV") is not null ? $"/vod-middle-tier/{Environment.GetEnvironmentVariable("ENV")}" : "/vod-middle-tier/dev")
        .AddJsonFile($"{envName}.settings.json", false, true)
        .AddSharedConfiguration()
        .AddJsonFile("local.settings.json", true, true);

builder.Services.AddApplicationConfiguration(builder.Configuration)
        .AddApplicationDependencies(builder.Configuration)
        .AddApplicationHandlers()
        .AddApplicationFactories()
        .AddApplicationRepositories();

var app = builder.Build();

var configuration = app.Services.GetService<IConfiguration>();
if (configuration is not null)
{
        bool.TryParse(configuration.GetSection("TranscriptionEngine")["EnableMediaConvertCron"], out bool enableCron);
        if (enableCron)
        {
                var job = app.Services.GetService<MediaConvertCronJob>() ?? throw new NullReferenceException(nameof(MediaConvertCronJob));
                await job.RunAsync();
        }
        else
        {
                Console.WriteLine("Media Convert Cron Disabled!");
        }
}

