FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build

RUN curl -L https://raw.githubusercontent.com/Microsoft/artifacts-credprovider/master/helpers/installcredprovider.sh | sh

ARG FEED_ACCESSTOKEN
ENV VSS_NUGET_EXTERNAL_FEED_ENDPOINTS="{\"endpointCredentials\": [{\"endpoint\":\"https://pkgs.dev.azure.com/nbadev/DTC/_packaging/Shared/nuget/v3/index.json\", \"username\":\"docker\", \"password\":\"${FEED_ACCESSTOKEN}\"}]}"

ARG BUILD_CONFIGURATION=Release
WORKDIR /src
COPY ["Vod.EcmsProcessor/Vod.EcmsProcessor.csproj", "Vod.EcmsProcessor/"]
COPY ["NBA.NextGen.CMSPlatform.VODService.Application/NBA.NextGen.CMSPlatform.VODService.Application.csproj", "NBA.NextGen.CMSPlatform.VODService.Application/"]
COPY ["NBA.NextGen.CMSPlatform.VODService.Domain/NBA.NextGen.CMSPlatform.VODService.Domain.csproj", "NBA.NextGen.CMSPlatform.VODService.Domain/"]
COPY ["NBA.NextGen.CMSPlatform.VODService.Infrastructure/NBA.NextGen.CMSPlatform.VODService.Infrastructure.csproj", "NBA.NextGen.CMSPlatform.VODService.Infrastructure/"]
COPY ["Vod.Common/Vod.Common.csproj", "Vod.Common/"]
COPY ["./Nuget.config", "."]
RUN dotnet restore --configfile ./Nuget.config "Vod.EcmsProcessor/Vod.EcmsProcessor.csproj"
COPY . .
WORKDIR "/src/Vod.EcmsProcessor"
RUN dotnet build "Vod.EcmsProcessor.csproj" -c $BUILD_CONFIGURATION -o /app/build

FROM build AS publish
ARG BUILD_CONFIGURATION=Release
RUN dotnet publish "Vod.EcmsProcessor.csproj" -c $BUILD_CONFIGURATION -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
RUN apt-get update && apt-get install -y \
    curl \
    ca-certificates \
 && curl -o /usr/local/share/ca-certificates/aws-rds.crt https://truststore.pki.rds.amazonaws.com/global/global-bundle.pem \
 && update-ca-certificates
ENTRYPOINT ["dotnet", "Vod.EcmsProcessor.dll"]
