using System.Diagnostics.CodeAnalysis;
using Vod.Common.Extensions;
using MST.Common.Azure.Workers;
using NBA.NextGen.CMSPlatform.VODService.Application.UseCases.Gam;
using NBA.NextGen.CMSPlatform.VODService.Application.UseCases.FreeWheel;
using MST.Common.Azure.Extensions;
using MST.Common.AWS.Extensions;

namespace Vod.EcmsProcessor.Extensions;

[ExcludeFromCodeCoverage]
public static class ApplicationExtensions
{
    public static IServiceCollection AddApplicationDependencies(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddSharedApplicationDependencies(configuration);
        return services;
    }

    public static IServiceCollection AddApplicationFactories(this IServiceCollection services)
    {
        return services;
    }

    public static IServiceCollection AddApplicationHandlers(this IServiceCollection services, IConfiguration configuration)
    {
        var submitQueueName = configuration.GetSection("AWSQueueNames")["SubmitToEcms"] ?? throw new NullReferenceException();
        var updateQueueName = configuration.GetSection("AWSQueueNames")["UpdateEcms"] ?? throw new NullReferenceException();
        services.RegisterSQSConsumer<SubmitToEcmsMessageHandler>(submitQueueName);
        services.RegisterSQSConsumer<UpdateEcmsMessageHandler>(updateQueueName);

        services.AddSingleton<SendResponseToGamCommandHandler>();
        services.AddSingleton<RespondToFreeWheelCommandHandler>();
        return services;
    }

    public static IServiceCollection AddApplicationConfiguration(this IServiceCollection services, IConfiguration configuration)
    {
        return services;
    }

    public static IServiceCollection AddApplicationRepositories(this IServiceCollection services)
    {
        return services;
    }
}