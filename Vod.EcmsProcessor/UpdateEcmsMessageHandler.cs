using Microsoft.Extensions.Options;
using MST.Common.Messaging;
using NBA.NextGen.CMSPlatform.VODService.Application.Services;
using NBA.NextGen.CMSPlatform.VODService.Application.UseCases.FreeWheel;
using NBA.NextGen.CMSPlatform.VODService.Application.UseCases.Gam;
using NBA.NextGen.CMSPlatform.VODService.Domain.Common;
using NBA.NextGen.CMSPlatform.VODService.Domain.Config;
using Newtonsoft.Json;
using Vod.Common.Services;

namespace Vod.EcmsProcessor;

public class UpdateEcmsMessageHandler : IMessageHandler
{
    private readonly ILogger _logger;
    private readonly IEcmsService _ecmsService;
    private readonly IServiceProvider _serviceProvider;
    private readonly CmsStatusNotificationsSettings _cmsStatusNotificationSettings;
    private readonly IMessageSender<UpdateToEcmsWrapper> _ecmsUpdateSender;
    private readonly IVodAssetRegistryService _assetRegistryService;

    public UpdateEcmsMessageHandler(ILogger<UpdateEcmsMessageHandler> logger, IMessageSenderFactory senderFactory, IEcmsService ecmsService, IServiceProvider serviceProvider, IOptions<CmsStatusNotificationsSettings> cmsStatusNotificationSettings, IVodAssetRegistryService assetRegistryService)
    {
        _logger = logger;
        _ecmsService = ecmsService;
        _serviceProvider = serviceProvider;
        _cmsStatusNotificationSettings = cmsStatusNotificationSettings.Value;
        _ecmsUpdateSender = senderFactory.Resolve<UpdateToEcmsWrapper>();
        _assetRegistryService = assetRegistryService;
    }

    public async Task ProcessMessage(ReceivedMessage message)
    {
        var request = JsonConvert.DeserializeObject<UpdateToEcmsWrapper>(message.Content ?? "");

        _logger.LogInformation("Received UpdateToEcmsWrapper");

        if (request is null)
        {
            _logger.LogError("UpdateToEcmsWrapper Failed to Deserialize Request, was null");
            return;
        }

        // if (cmsStatusNotificationSettings.EnableEventNotifier)
        // {
        //     var eventNotifier = eventNotifierProvider.GetNotifier(cmsStatusNotificationSettings.EventGridTopic);
        //     var identifier = request.StatusNotification.Identifier;
        //     await eventNotifier.NotifyAsync(cmsStatusNotificationSettings.EventGridEventType, identifier ?? string.Empty, request.StatusNotification).ConfigureAwait(false);
        // }

        // Check if this is a freewheel ad.
        if (!string.IsNullOrWhiteSpace(request.StatusNotification?.AdditionalInfo?.VodServiceUuid))
        {
            if (request.StatusNotification.AdditionalInfo.VodServiceUuid.StartsWith("FW|", System.StringComparison.InvariantCultureIgnoreCase))
            {
                try
                {
                    var result = await UpdateAssetInFreewheelAsync(request);
                    message.ShouldDeadLetter = !result;
                }
                catch (Exception e)
                {
                    _logger.LogError(e, "Update FreeWheel Failed");
                    message.ShouldDeadLetter = true;
                }
                return;
            }

            if (request.StatusNotification.AdditionalInfo.VodServiceUuid.StartsWith("GAM|", System.StringComparison.InvariantCultureIgnoreCase))
            {
                try
                {
                    var result = await UpdateAssetInGamAsync(request);
                    message.ShouldDeadLetter = !result;
                }
                catch (Exception e)
                {
                    _logger.LogError(e, "Update GAM Failed");
                    message.ShouldDeadLetter = true;
                }
                return;
            }
        }

        if (!_cmsStatusNotificationSettings.EnableEcmsNotification)
        {
            return;
        }

        try
        {
            var success = await _ecmsService.UpdateAssetInEcmsAsync(request);
            _logger.LogInformation($"Success: {success}");
        }
        catch (Exception e)
        {
            // Resubmit up to 3 times.
            if (request.RetryCount < 2)
            {
                request.RetryCount++;
                await _ecmsUpdateSender.SendAsync(request);
                _logger.LogError(e, "Failed to Send Update to ECMS, will resubmit to end of queue.");
            }
            else
            {
                _logger.LogError(e, "Failed to Send Update to ECMS, will dead-letter.");
                var identifier = request?.StatusNotification?.Identifier;
                await _assetRegistryService.AddAssetEventAsync(identifier ?? "No Asset Id Found", NBA.NextGen.CMSPlatform.VODService.Domain.Common.Enums.AssetEventTypes.EcmsUpdateMessageDeadLettered, DateTime.UtcNow, e.Message);
                message.ShouldDeadLetter = true;
            }
        }


    }

    public Task ProcessError(Exception exception)
    {
        _logger.LogError(exception, "UpdateEcmsMessageHandler Queue Error: {ExceptionMessage}", exception.Message);
        return Task.CompletedTask;
    }

    private async Task<bool> UpdateAssetInFreewheelAsync(UpdateToEcmsWrapper request)
    {
        var command = new RespondToFreeWheelCommand()
        {
            InternalId = request.StatusNotification.AdditionalInfo.VodServiceUuid,
            Duration = request.StatusNotification.AdditionalInfo.Duration,
            OutputAssetId = request.StatusNotification.AdditionalInfo.OutputAssetId,
            IsError = request.StatusNotification.Status != NBA.NextGen.CMSPlatform.VODService.Domain.CmsStatusNotifications.StatusNotificationStatus.Succeeded,
            ErrorMessage = request.StatusNotification.FailureReason,
            MediaKindEnvKey = _cmsStatusNotificationSettings.MediaKindEnvKey,
        };

        var handler = _serviceProvider.GetService<RespondToFreeWheelCommandHandler>();
        if (handler is null)
        {
            throw new NullReferenceException("Could Not Resolve RespondToFreeWheelCommandHandler");
        }
        return await handler.Handle(command, CancellationToken.None);
    }

    private async Task<bool> UpdateAssetInGamAsync(UpdateToEcmsWrapper request)
    {
        var command = new SendResponseToGamCommand()
        {
            InternalId = request.StatusNotification.AdditionalInfo.VodServiceUuid,
            Duration = request.StatusNotification.AdditionalInfo.Duration,
            OutputAssetId = request.StatusNotification.AdditionalInfo.OutputAssetId,
            IsError = request.StatusNotification.Status != NBA.NextGen.CMSPlatform.VODService.Domain.CmsStatusNotifications.StatusNotificationStatus.Succeeded,
            ErrorMessage = request.StatusNotification.FailureReason,
            MediaKindEnvKey = _cmsStatusNotificationSettings.MediaKindEnvKey,
        };

        var handler = _serviceProvider.GetService<SendResponseToGamCommandHandler>();
        if (handler is null)
        {
            throw new NullReferenceException("Could Not Resolve SendResponseToGamCommandHandler");
        }
        return await handler.Handle(command, CancellationToken.None);
    }
}