using MST.Common.Messaging;
using NBA.NextGen.CMSPlatform.VODService.Application.Services;
using NBA.NextGen.CMSPlatform.VODService.Domain.Common;
using Newtonsoft.Json;
using Vod.Common.Services;

namespace Vod.EcmsProcessor;

public class SubmitToEcmsMessageHandler : IMessageHandler
{
    private readonly ILogger _logger;
    private readonly IEcmsService _ecmsService;
    private readonly IMessageSender<SubmitToEcmsWrapper> _ecmsSubmitSender;
    private readonly IVodAssetRegistryService _assetRegistryService;

    public SubmitToEcmsMessageHandler(ILogger<SubmitToEcmsMessageHandler> logger, IMessageSenderFactory senderFactory, IEcmsService ecmsService, IVodAssetRegistryService assetRegistryService)
    {
        _logger = logger;
        _ecmsService = ecmsService;
        _ecmsSubmitSender = senderFactory.Resolve<SubmitToEcmsWrapper>();
        _assetRegistryService = assetRegistryService;
    }

    public async Task ProcessMessage(ReceivedMessage message)
    {
        var request = JsonConvert.DeserializeObject<SubmitToEcmsWrapper>(message.Content ?? "");

        _logger.LogInformation("Received SubmitToEcmsMessage");

        if (request is null)
        {
            _logger.LogError("SubmitToEcmsMessage Failed to Deserialize Request, was null");
            return;
        }

        try
        {
            var success = await _ecmsService.SubmitToEcmsAsync(request);
            _logger.LogInformation($"Success: {success}");
        }
        catch (Exception e)
        {
            // Resubmit up to 3 times.
            if (request.RetryCount < 2)
            {
                request.RetryCount++;
                await _ecmsSubmitSender.SendAsync(request);
                _logger.LogError(e, "Failed to Send to ECMS, will resubmit to end of queue.");
            }
            else
            {
                _logger.LogError(e, "Failed to Send to ECMS, will dead-letter");
                await _assetRegistryService.AddAssetEventAsync(request.ResourceId, NBA.NextGen.CMSPlatform.VODService.Domain.Common.Enums.AssetEventTypes.EcmsSubmitMessageDeadLettered, DateTime.UtcNow, e.Message);
                message.ShouldDeadLetter = true;
            }
        }
    }

    public Task ProcessError(Exception exception)
    {
        _logger.LogError(exception, "SubmitToEcmsMessageHandler Queue Error: {ExceptionMessage}", exception.Message);
        return Task.CompletedTask;
    }
}
