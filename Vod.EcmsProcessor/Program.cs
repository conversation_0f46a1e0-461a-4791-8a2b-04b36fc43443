using HealthChecks.UI.Client;
using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using Vod.EcmsProcessor.Extensions;
using Vod.Common.Extensions;
using Vod.Common.Health;
using Microsoft.Extensions.Diagnostics.HealthChecks;

Console.WriteLine("ECMS Processor starting up!");

var builder = WebApplication.CreateBuilder(args);
builder.Configuration
        .SetBasePath(AppDomain.CurrentDomain.BaseDirectory)
        .AddJsonFile("appsettings.json", false, true)
        .AddJsonFile("local.settings.json", true, true)
        //.AddAppConfiguration(builder.Configuration)
        .AddEnvironmentVariables();

var envName = builder.Configuration.GetValue<string>("ENV") ?? "qa";
builder.Configuration
        .AddAmazonSecretsManager(Environment.GetEnvironmentVariable("AWS_REGION") ?? "us-east-1",
                Environment.GetEnvironmentVariable("ENV") is not null ? $"/vod-middle-tier/{Environment.GetEnvironmentVariable("ENV")}" : "/vod-middle-tier/dev")
        .AddJsonFile($"{envName}.settings.json", false, true)
        .AddSharedConfiguration();

Console.WriteLine("Env: " + envName);

builder.Services.AddAuthorization();
builder.Services.AddControllers();
builder.Services.AddMvc();

builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

builder.Services.AddApplicationConfiguration(builder.Configuration)
        .AddApplicationDependencies(builder.Configuration)
        .AddApplicationHandlers(builder.Configuration)
        .AddApplicationFactories()
        .AddApplicationRepositories();

builder.Services.AddHealthChecks()
    .AddCheck<MemoryHealthCheck>("Memory Check", HealthStatus.Unhealthy, ["VOD ECMS Processor"]);

var app = builder.Build();

var logger = app.Services.GetService<ILogger<Program>>();

logger!.LogInformation("Getting Started!");

if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseHttpsRedirection();

app.UseAuthorization();
app.MapControllers();

app.MapHealthChecks("/healthz", new HealthCheckOptions
{
    Predicate = _ => true,
    ResponseWriter = UIResponseWriter.WriteHealthCheckUIResponse
});

app.MapHealthChecks("/readyz", new HealthCheckOptions
{
    Predicate = _ => true,
    ResponseWriter = UIResponseWriter.WriteHealthCheckUIResponse
});

app.Run();