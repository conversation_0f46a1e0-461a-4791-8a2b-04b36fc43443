// "//-----------------------------------------------------------------------".
// <copyright file="AdditionalInfo.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Domain.CmsStatusNotifications
{
    using System;

    /// <summary>
    /// AdditionalInfo.
    /// </summary>
    public class AdditionalInfo
    {
        /// <summary>
        /// Gets or sets the program id.
        /// </summary>
        public string ProgramId { get; set; }

        /// <summary>
        /// Gets or sets the Vod Service UUID.
        /// </summary>
        public string VodServiceUuid { get; set; }

        /// <summary>
        /// Gets or sets the Output Asset Id.
        /// </summary>
        public string OutputAssetId { get; set; }

        /// <summary>
        /// Gets or sets the Duration.
        /// </summary>
        public TimeSpan Duration { get; set; }

        /// <summary>
        /// Gets or sets the Provider Id.
        /// </summary>
        public string ProviderId { get; set; }

        /// <summary>
        /// Gets or sets the recordingStatus .
        /// </summary>
        public string RecordingStatus { get; set; }

        /// <summary>
        /// Gets or sets the VideoSyndicationUrl.
        /// </summary>
#pragma warning disable CA1056 // URI-like properties should not be strings
        public string VideoSyndicationUrl { get; set; }
#pragma warning restore CA1056 // URI-like properties should not be strings

        /// <summary>
        /// Gets or sets the Ivi_ctype.
        /// </summary>
#pragma warning disable CA1707 // Identifiers should not contain underscores
        public string Ivi_ctype { get; set; }
#pragma warning restore CA1707 // Identifiers should not contain underscores
    }
}
