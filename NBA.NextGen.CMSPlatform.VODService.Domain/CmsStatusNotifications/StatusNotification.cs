// "//-----------------------------------------------------------------------".
// <copyright file="StatusNotification.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Domain.CmsStatusNotifications
{
    /// <summary>
    /// StatusNotificationStatus.
    /// </summary>
    public enum StatusNotificationStatus
    {
        /// <summary>
        /// The succeeded
        /// </summary>
        Succeeded = 0,

        /// <summary>
        /// The failed
        /// </summary>
        Failed = 1,
    }

    /// <summary>
    ///   StatusNotification.
    /// </summary>
    public partial class StatusNotification
    {
        /// <summary>
        /// Gets or sets the identifier.
        /// </summary>
        /// <value>
        /// The identifier.
        /// </value>
        public string Identifier { get; set; }

        /// <summary>
        /// Gets or sets the timestamp.
        /// </summary>
        /// <value>
        /// The timestamp.
        /// </value>
        public string Timestamp { get; set; }

        /// <summary>
        /// Gets or sets the status.
        /// </summary>
        /// <value>
        /// The status.
        /// </value>
        public StatusNotificationStatus? Status { get; set; }

        /// <summary>
        /// Gets or sets the operation.
        /// </summary>
        /// <value>
        /// The operation.
        /// </value>
        public string Operation { get; set; }

        /// <summary>
        /// Gets or sets the title details href.
        /// </summary>
        /// <value>
        /// The title details href.
        /// </value>
        public string TitleDetailsHref { get; set; }

        /// <summary>
        /// Gets or sets the title view link.
        /// </summary>
        /// <value>
        /// The title view link.
        /// </value>
        public string TitleViewLink { get; set; }

        /// <summary>
        /// Gets or sets the failure reason.
        /// </summary>
        /// <value>
        /// The failure reason.
        /// </value>
        public string FailureReason { get; set; }

        /// <summary>
        /// Gets or sets the work order href.
        /// </summary>
        /// <value>
        /// The work order href.
        /// </value>
        public string WorkOrderHref { get; set; }

        /// <summary>
        /// Gets or sets the additional info.
        /// </summary>
        public AdditionalInfo AdditionalInfo { get; set; }
    }
}
