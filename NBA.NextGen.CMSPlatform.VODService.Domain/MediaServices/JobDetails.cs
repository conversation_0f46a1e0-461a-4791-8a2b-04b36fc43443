// "//-----------------------------------------------------------------------".
// <copyright file="JobDetails.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Domain.MediaServices
{
    using System;
    using System.Collections.Generic;
    using System.Text;

    /// <summary>
    /// JobDetails.
    /// </summary>
    public class JobDetails
    {
        /// <summary>
        /// Gets or sets the name of the Job.
        /// </summary>
        public string JobName { get; set; }

        /// <summary>
        /// Gets or sets names of the output assets.
        /// </summary>
        public string OutputAssetName { get; set; }

        /// <summary>
        /// Gets or sets names of the output assets.
        /// </summary>
        public string InputAssetName { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether a job is finished.
        /// </summary>
        public bool IsSuccessful { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether a job is in a final state and should be deleted.
        /// </summary>
        public bool IsInFinalState { get; set; }

        /// <summary>
        /// Gets or sets the correlation key.
        /// </summary>
        public string CorrelationKey { get; set; }

        /// <summary>
        /// Gets or sets the correlation source.
        /// </summary>
        public string CorrelationSource { get; set; }
    }
}
