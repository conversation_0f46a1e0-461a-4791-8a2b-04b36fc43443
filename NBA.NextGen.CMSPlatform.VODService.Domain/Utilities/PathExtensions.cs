// "//-----------------------------------------------------------------------".
// <copyright file="PathExtensions.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Domain.Utilities
{
    using System;
    using System.Diagnostics.CodeAnalysis;
    using System.Linq;

    /// <summary>
    /// PathExtensions.
    /// </summary>
    public static class PathExtensions
    {
        /// <summary>
        /// Gets Directory Name.
        /// </summary>
        /// <param name="fullPath">Full Path of the resource.</param>
        /// <returns>string.</returns>
        public static string GetDirectoryName([NotNull] this string fullPath)
        {
            return fullPath.HasDirectories() ? fullPath.Split("/", StringSplitOptions.None).First() : fullPath;
        }

        /// <summary>
        /// Gets File path excluding container.
        /// </summary>
        /// <param name="fullPath">Full Path of the resource.</param>
        /// <param name="filename">The file name.</param>
        /// <returns>string.</returns>
        public static string GetFilePathExcludingContainer([NotNull] this string fullPath, [NotNull] string filename)
        {
            return $"{fullPath}/{filename}";
        }

        /// <summary>
        /// Generates File path.
        /// </summary>
        /// <param name="fullPath">Full Path of the resource.</param>
        /// <param name="filename">The file name.</param>
        /// <returns>string.</returns>
        public static string GenerateFilePath([NotNull] this string fullPath, [NotNull] string filename)
        {
            return fullPath.HasDirectories() ? $"{string.Join("/", fullPath.Split("/", StringSplitOptions.None).SkipLast(1))}/{filename}" : filename;
        }

        /// <summary>
        /// Checks if path has directories involved.
        /// </summary>
        /// <param name="fullPath">Full Path of the resource.</param>
        /// <returns>bool.</returns>
        private static bool HasDirectories([NotNull] this string fullPath)
        {
            return fullPath.Contains('/', StringComparison.OrdinalIgnoreCase);
        }
    }
}
