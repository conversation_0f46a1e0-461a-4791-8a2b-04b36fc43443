// "//-----------------------------------------------------------------------".
// <copyright file="EncodingExtensions.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Domain.Utilities
{
    using System.Diagnostics.CodeAnalysis;
    using System.Text;
    using Base62;

    /// <summary>
    /// EncodingExtensions.
    /// </summary>
    public static class EncodingExtensions
    {
        /// <summary>
        /// Gets the Base62 encoded string.
        /// </summary>
        /// <param name="input">INput string.</param>
        /// <returns>Base62 string.</returns>
        public static string ToBase62String([NotNull] this string input)
        {
            return Encoding.UTF8.GetBytes(input).ToBase62();
        }

        /// <summary>
        /// Gets the Base62 decoded string.
        /// </summary>
        /// <param name="input">INput string.</param>
        /// <returns>Base62 string.</returns>
        public static string FromBase62String([NotNull] this string input)
        {
            return Encoding.UTF8.GetString(input.FromBase62());
        }
    }
}
