// "//-----------------------------------------------------------------------".
// <copyright file="FlowAgnosticReseedRequest.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Domain.Utilities
{
    using System;
    using System.Collections.Generic;

    /// <summary>
    /// NormalFlowReseedRequest.
    /// </summary>
    public class FlowAgnosticReseedRequest
    {
        /// <summary>
        /// Gets or sets the FlowName.
        /// </summary>
        public string FolderName { get; set; }

        /// <summary>
        /// Gets or sets the StartTime.
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// Gets or Sets the EndTime.
        /// </summary>
        public DateTime EndTime { get; set; }

        /// <summary>
        /// Gets or Sets a value indicating whether the function should evaluate but not send to ecms.
        /// </summary>
        public bool EvaluateAndRun { get; set; }

        /// <summary>
        /// Gets or sets a list of files names to filter down to.
        /// </summary>
        public List<string> FileNames { get; set; }

        /// <summary>
        /// Gets or Sets a value indicating whether the function should target a different flow beside normalflow.
        /// </summary>
        /// <value>Options are "normalflow", "directflow", "live2vodflow".</value>
        public string FlowTypeOverride { get; set; }
    }
}
