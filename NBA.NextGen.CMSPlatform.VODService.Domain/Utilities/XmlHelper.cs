// "//-----------------------------------------------------------------------".
// <copyright file="XmlHelper.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Domain.Utilities
{
    using System;
    using System.IO;
    using System.Text;
    using System.Xml;
    using System.Xml.Serialization;

    /// <summary>
    /// XML Helper.
    /// </summary>
    public static class XmlHelper
    {
        /// <summary>
        /// The namespaces.
        /// </summary>
        private static readonly XmlSerializerNamespaces Namespaces = new XmlSerializerNamespaces(new[] { new XmlQualifiedName(string.Empty, string.Empty) });

        /// <summary>
        /// Serializes to XML.
        /// </summary>
        /// <typeparam name="T">The type param.</typeparam>
        /// <param name="dataToSerialize">The data to serialize.</param>
        /// <param name="encoding">The encoding.</param>
        /// <returns>
        /// The XML string.
        /// </returns>
        public static string SerializeXML<T>(T dataToSerialize, Encoding encoding)
        {
            if (encoding == null)
            {
                throw new ArgumentNullException(nameof(encoding));
            }

            using (MemoryStream ms = new MemoryStream())
            {
                XmlWriterSettings xmlWriterSettings = new System.Xml.XmlWriterSettings()
                {
                    Encoding = encoding,
                };
                using (var xmlWriter = XmlWriter.Create(ms, xmlWriterSettings))
                {
                    var serializer = new XmlSerializer(typeof(T));
                    serializer.Serialize(xmlWriter, dataToSerialize, Namespaces);
                }

                return encoding.GetString(ms.ToArray());
            }
        }

        /// <summary>
        /// Deserializes from XML.
        /// </summary>
        /// <typeparam name="T">The type param.</typeparam>
        /// <param name="xmlText">The XML text.</param>
        /// <returns>The object.</returns>
        public static T DeserializeXML<T>(string xmlText)
        {
            MemoryStream memStream = new MemoryStream(Encoding.UTF8.GetBytes(xmlText));
            using (var xmlReader = XmlReader.Create(memStream))
            {
                var serializer = new XmlSerializer(typeof(T));
                return (T)serializer.Deserialize(xmlReader);
            }
        }
    }
}
