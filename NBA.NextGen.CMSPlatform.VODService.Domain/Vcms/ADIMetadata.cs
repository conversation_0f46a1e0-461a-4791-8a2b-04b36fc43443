// "//-----------------------------------------------------------------------".
// <copyright file="ADIMetadata.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Domain.Vcms
{
    using System.Collections.ObjectModel;

    /// <summary>
    /// ADIMetadata.
    /// </summary>
    [System.Serializable]
    [System.ComponentModel.DesignerCategory("code")]
    [System.Xml.Serialization.XmlType(AnonymousType = true)]
    public class ADIMetadata
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="ADIMetadata"/> class.
        /// </summary>
        public ADIMetadata()
        {
            this.AppData = new Collection<ADIMetadataAppData>();
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="ADIMetadata"/> class.
        /// </summary>
        /// <param name="appData">The application data.</param>
        public ADIMetadata(Collection<ADIMetadataAppData> appData)
        {
            this.AppData = appData;
        }

        /// <summary>
        /// Gets or sets the ams.
        /// </summary>
        /// <value>
        /// The ams.
        /// </value>
        public ADIMetadataAMS AMS { get; set; }

        /// <summary>
        /// Gets or sets the application data.
        /// </summary>
        /// <value>
        /// The application data.
        /// </value>
        [System.Xml.Serialization.XmlElement("App_Data")]
        public Collection<ADIMetadataAppData> AppData { get; set; }
    }
}
