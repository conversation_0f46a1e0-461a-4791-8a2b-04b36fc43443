// "//-----------------------------------------------------------------------".
// <copyright file="ADI.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Domain.Vcms
{
    using System;
    using System.Linq;

    /// <summary>
    /// ADI.
    /// </summary>
    [Serializable]
    [System.ComponentModel.DesignerCategory("code")]
    [System.Xml.Serialization.XmlType(AnonymousType = true)]
    [System.Xml.Serialization.XmlRoot(Namespace = "", IsNullable = false)]
    public class ADI
    {
        /// <summary>
        /// Gets or sets the metadata.
        /// </summary>
        /// <value>
        /// The metadata.
        /// </value>
        public ADIMetadata Metadata { get; set; }

        /// <summary>
        /// Gets or sets the asset.
        /// </summary>
        /// <value>
        /// The asset.
        /// </value>
        public ADIAsset Asset { get; set; }

        /// <summary>
        /// Gets Adi Asset Id.
        /// </summary>
        /// <returns>Adi Asset Id.</returns>
        public string GetAdiAssetId()
        {
            return this.GetAdiAssetId(null, null);
        }

        /// <summary>
        /// Gets Adi Asset Id.
        /// </summary>
        /// <param name="suffix">Provided suffix.</param>
        /// <param name="recordId">Record Id.</param>
        /// <returns>Adi Asset Id.</returns>
        public string GetAdiAssetId(string suffix, string recordId)
        {
            return string.IsNullOrEmpty(suffix) ? this.Metadata.AMS.AssetID : $"{this.Metadata.AMS.AssetID}~{suffix}-{recordId}";
        }
    }
}
