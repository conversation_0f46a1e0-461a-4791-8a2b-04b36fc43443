// "//-----------------------------------------------------------------------".
// <copyright file="ADIAssetAssetMetadataAppData.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".
namespace NBA.NextGen.CMSPlatform.VODService.Domain.Vcms
{
    using System;

    /// <summary>
    /// ADIAssetAssetMetadataAppData.
    /// </summary>
    [Serializable]
    [System.ComponentModel.DesignerCategory("code")]
    [System.Xml.Serialization.XmlType(AnonymousType = true)]
    [System.Xml.Serialization.XmlRoot(Namespace = "", IsNullable = false)]
    public class ADIAssetAssetMetadataAppData
    {
        /// <summary>
        /// Gets or sets the application.
        /// </summary>
        /// <value>
        /// The application.
        /// </value>
        [System.Xml.Serialization.XmlAttribute("App")]
        public string App { get; set; }

        /// <summary>
        /// Gets or sets the name.
        /// </summary>
        /// <value>
        /// The name.
        /// </value>
        [System.Xml.Serialization.XmlAttribute("Name")]
        public string Name { get; set; }

        /// <summary>
        /// Gets or sets the value.
        /// </summary>
        /// <value>
        /// The value.
        /// </value>
        [System.Xml.Serialization.XmlAttribute("Value")]
        public string Value { get; set; }
    }
}
