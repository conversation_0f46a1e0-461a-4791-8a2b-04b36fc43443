// "//-----------------------------------------------------------------------".
// <copyright file="ADISubmissionResponse.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Domain.Vcms
{
    using System;

    /// <summary>
    /// ADI Submission Response.
    /// </summary>
    public class ADISubmissionResponse
    {
        /// <summary>
        /// Gets or sets the metadata.
        /// </summary>
        /// <value>
        /// The metadata.
        /// </value>
        public string AssetId { get; set; }
    }
}
