// "//-----------------------------------------------------------------------".
// <copyright file="ADIAssetAssetMetadata.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".
namespace NBA.NextGen.CMSPlatform.VODService.Domain.Vcms
{
    using System;
    using System.Collections.ObjectModel;

    /// <summary>
    /// ADIAssetAssetMetadata.
    /// </summary>
    [Serializable]
    [System.ComponentModel.DesignerCategory("code")]
    [System.Xml.Serialization.XmlType(AnonymousType = true)]
    public class ADIAssetAssetMetadata
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="ADIAssetAssetMetadata"/> class.
        /// </summary>
        public ADIAssetAssetMetadata()
        {
            this.AppData = new Collection<ADIAssetAssetMetadataAppData>();
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="ADIAssetAssetMetadata" /> class.
        /// </summary>
        /// <param name="appData">The application data.</param>
        public ADIAssetAssetMetadata(Collection<ADIAssetAssetMetadataAppData> appData)
        {
            this.AppData = appData;
        }

        /// <summary>
        /// Gets or sets the ams.
        /// </summary>
        /// <value>
        /// The ams.
        /// </value>
        public ADIAssetAssetMetadataAMS AMS { get; set; }

        /// <summary>
        /// Gets or sets the application data.
        /// </summary>
        /// <value>
        /// The application data.
        /// </value>
        [System.Xml.Serialization.XmlElementAttribute("App_Data")]
        public Collection<ADIAssetAssetMetadataAppData> AppData { get; set; }
    }
}
