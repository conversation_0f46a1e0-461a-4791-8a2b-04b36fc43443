// "//-----------------------------------------------------------------------".
// <copyright file="ADIAsset.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Domain.Vcms
{
    using System.Collections.ObjectModel;

    /// <summary>
    /// ADIAsset.
    /// </summary>
    [System.Serializable]
    [System.ComponentModel.DesignerCategory("code")]
    [System.Xml.Serialization.XmlType(AnonymousType = true)]
    public class ADIAsset
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="ADIAsset"/> class.
        /// </summary>
        public ADIAsset()
        {
            this.Asset = new Collection<ADIAssetAsset>();
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="ADIAsset"/> class.
        /// </summary>
        /// <param name="appData">The application data.</param>
        public ADIAsset(Collection<ADIAssetAsset> appData)
        {
            this.Asset = appData;
        }

        /// <summary>
        /// Gets or sets the metadata.
        /// </summary>
        /// <value>
        /// The metadata.
        /// </value>
        public ADIAssetMetadata Metadata { get; set; }

        /// <summary>
        /// Gets or sets the asset.
        /// </summary>
        /// <value>
        /// The asset.
        /// </value>
        [System.Xml.Serialization.XmlElementAttribute("Asset")]
        public Collection<ADIAssetAsset> Asset { get; set; }
    }
}
