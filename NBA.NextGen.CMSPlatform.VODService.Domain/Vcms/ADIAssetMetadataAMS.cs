// "//-----------------------------------------------------------------------".
// <copyright file="ADIAssetMetadataAMS.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".
namespace NBA.NextGen.CMSPlatform.VODService.Domain.Vcms
{
    /// <summary>
    /// ADIAssetMetadataAMS.
    /// </summary>
    [System.Serializable]
    [System.ComponentModel.DesignerCategory("code")]
    [System.Xml.Serialization.XmlType(AnonymousType = true)]
    public class ADIAssetMetadataAMS
    {
        /// <summary>
        /// Gets or sets the name of the asset.
        /// </summary>
        /// <value>
        /// The name of the asset.
        /// </value>
        [System.Xml.Serialization.XmlAttribute("Asset_Name")]
        public string AssetName { get; set; }

        /// <summary>
        /// Gets or sets the provider.
        /// </summary>
        /// <value>
        /// The provider.
        /// </value>
        [System.Xml.Serialization.XmlAttribute("Provider")]
        public string Provider { get; set; }

        /// <summary>
        /// Gets or sets the provider identifier.
        /// </summary>
        /// <value>
        /// The provider identifier.
        /// </value>
        [System.Xml.Serialization.XmlAttribute("Provider_ID")]
        public string ProviderID { get; set; }

        /// <summary>
        /// Gets or sets the product.
        /// </summary>
        /// <value>
        /// The product.
        /// </value>
        [System.Xml.Serialization.XmlAttributeAttribute("Product")]
        public string Product { get; set; }

        /// <summary>
        /// Gets or sets the asset identifier.
        /// </summary>
        /// <value>
        /// The asset identifier.
        /// </value>
        [System.Xml.Serialization.XmlAttribute("Asset_ID")]
        public string AssetID { get; set; }

        /// <summary>
        /// Gets or sets the asset class.
        /// </summary>
        /// <value>
        /// The asset class.
        /// </value>
        [System.Xml.Serialization.XmlAttribute("Asset_Class")]
        public string AssetClass { get; set; }

        /// <summary>
        /// Gets or sets the verb.
        /// </summary>
        /// <value>
        /// The verb.
        /// </value>
        [System.Xml.Serialization.XmlAttribute("Verb")]
        public string Verb { get; set; }

        /// <summary>
        /// Gets or sets the version major.
        /// </summary>
        /// <value>
        /// The version major.
        /// </value>
        [System.Xml.Serialization.XmlAttribute("Version_Major")]
        public byte VersionMajor { get; set; }

        /// <summary>
        /// Gets or sets the version minor.
        /// </summary>
        /// <value>
        /// The version minor.
        /// </value>
        [System.Xml.Serialization.XmlAttribute("Version_Minor")]
        public byte VersionMinor { get; set; }

        /// <summary>
        /// Gets or sets the description.
        /// </summary>
        /// <value>
        /// The description.
        /// </value>
        [System.Xml.Serialization.XmlAttribute("Description")]
        public string Description { get; set; }

        /// <summary>
        /// Gets or sets the creation date.
        /// </summary>
        /// <value>
        /// The creation date.
        /// </value>
        [System.Xml.Serialization.XmlAttribute("Creation_Date")]
        public System.DateTime CreationDate { get; set; }
    }
}
