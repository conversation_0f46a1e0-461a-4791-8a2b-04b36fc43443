// "//-----------------------------------------------------------------------".
// <copyright file="ADIAssetAssetContent.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".
namespace NBA.NextGen.CMSPlatform.VODService.Domain.Vcms
{
    using System;

    /// <summary>
    /// ADIAssetAssetContent.
    /// </summary>
    [Serializable]
    [System.ComponentModel.DesignerCategory("code")]
    [System.Xml.Serialization.XmlType(AnonymousType = true)]
    public class ADIAssetAssetContent
    {
        /// <summary>
        /// Gets or sets the value.
        /// </summary>
        /// <value>
        /// The value.
        /// </value>
        [System.Xml.Serialization.XmlAttributeAttribute("Value")]
        public string Value { get; set; }
    }
}
