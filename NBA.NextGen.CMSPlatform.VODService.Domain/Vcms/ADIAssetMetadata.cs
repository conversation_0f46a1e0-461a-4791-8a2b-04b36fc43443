// "//-----------------------------------------------------------------------".
// <copyright file="ADIAssetMetadata.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".
namespace NBA.NextGen.CMSPlatform.VODService.Domain.Vcms
{
    using System.Collections.ObjectModel;

    /// <summary>
    /// ADIAssetMetadata.
    /// </summary>
    [System.Serializable]
    [System.ComponentModel.DesignerCategory("code")]
    [System.Xml.Serialization.XmlType(AnonymousType = true)]
    public class ADIAssetMetadata
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="ADIAssetMetadata"/> class.
        /// </summary>
        public ADIAssetMetadata()
        {
            this.AppData = new Collection<ADIAssetMetadataAppData>();
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="ADIAssetMetadata"/> class.
        /// </summary>
        /// <param name="appData">The application data.</param>
        public ADIAssetMetadata(Collection<ADIAssetMetadataAppData> appData)
        {
            this.AppData = appData;
        }

        /// <summary>
        /// Gets or sets the ams.
        /// </summary>
        /// <value>
        /// The ams.
        /// </value>
        public ADIAssetMetadataAMS AMS { get; set; }

        /// <summary>
        /// Gets or sets the application data.
        /// </summary>
        /// <value>
        /// The application data.
        /// </value>
        [System.Xml.Serialization.XmlElement("App_Data")]
        public Collection<ADIAssetMetadataAppData> AppData { get; set; }
    }
}
