// "//-----------------------------------------------------------------------".
// <copyright file="IngestionRequest.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Domain.Gam
{
    /// <summary>
    /// IngestionRequest.
    /// </summary>
    public class IngestionRequest
    {
        /// <summary>
        /// Gets or sets the title.
        /// </summary>
        public string Title { get; set; }

        /// <summary>
        /// Gets or sets the url.
        /// </summary>
#pragma warning disable CA1056
        public string Url { get; set; }
#pragma warning restore CA1056

        /// <summary>
        /// Gets or sets the click through url.
        /// </summary>
#pragma warning disable CA1056
        public string ClickThroughUrl { get; set; }
#pragma warning restore CA1056

        /// <summary>
        /// Gets or sets the company id.
        /// </summary>
        public long CompanyId { get; set; }
    }
}