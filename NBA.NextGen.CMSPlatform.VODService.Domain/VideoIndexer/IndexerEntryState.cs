// "//-----------------------------------------------------------------------".
// <copyright file="IndexerEntryState.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Domain.VideoIndexer
{
    /// <summary>
    /// IndexerEntryState.
    /// </summary>
    public enum IndexerEntryState
    {
        /// <summary>
        /// Processing.
        /// </summary>
        Processing,

        /// <summary>
        /// Completed.
        /// </summary>
        Completed,

        /// <summary>
        /// Error.
        /// </summary>
        Error,

        /// <summary>
        /// Unknown.
        /// </summary>
        Unknown,
    }
}