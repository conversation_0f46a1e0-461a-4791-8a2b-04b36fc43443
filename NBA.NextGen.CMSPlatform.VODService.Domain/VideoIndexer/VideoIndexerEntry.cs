// "//-----------------------------------------------------------------------".
// <copyright file="VideoIndexerEntry.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Domain.VideoIndexer
{
    /// <summary>
    /// VideoIndexerEntry.
    /// </summary>
    public class VideoIndexerEntry
    {
        /// <summary>
        /// Gets or sets the id.
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// Gets or sets the name.
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Gets or sets the external id.
        /// </summary>
        public string ExternalId { get; set; }

        /// <summary>
        /// Gets or sets the state.
        /// </summary>
        public IndexerEntryState State { get; set; }
    }
}