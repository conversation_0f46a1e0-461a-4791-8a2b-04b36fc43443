namespace NBA.NextGen.CMSPlatform.VODService.Domain.Config;

public class NormalFlowSettings
{
    public string BlobConnectionString { get; set; }

    public string ContainerName { get; set; }

    public string CaptionContainer { get; set; } = "closedcaption";

    public string CopyContainerName { get; set; }

    public string CopyCaptionContainer { get; set; } = "closedcaption";

    public string WscContainerPath { get; set; }

    public string InhouseJsonContainerPath { get; set; }

    public string NbaTvContainerPath { get; set; }

    public bool EnableMocking { get; set; }
    public string AccountName { get; set; }

    public string CaptionsDestinationBucket { get; set; }

    public string ClosedCaptionRequestEndpoint { get; set; }

    public string WordpressAPIDomain { get; set; }

    public string VodFeedbackFolder { get; set; }

    public string VodFeedbackContainer { get; set; }

    public bool BlobClientProviderSupport { get; set; }

    public bool EnableEventNotifier { get; set; }

    public string EventGridTopic { get; set; }

    public string EventGridEndpoint { get; set; }

    public string EventGridKey { get; set; }

    public string LogLevel { get; set; }

    /// <summary>
    /// League-specific API domain mappings for ECMS endpoints
    /// </summary>
    public Dictionary<string, string> LeagueApiDomains { get; set; } = new Dictionary<string, string>();

    /// <summary>
    /// Default API domain to use when leagueId is not found or not specified
    /// </summary>
    public string DefaultApiDomain { get; set; }
}