// "//-----------------------------------------------------------------------".
// <copyright file="VideoIndexerSettings.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Domain.Config
{
    /// <summary>
    /// VideoIndexerSettings.
    /// </summary>
    public class VideoIndexerSettings
    {
        /// <summary>
        /// Gets or sets the Resource Group.
        /// </summary>
        public string ResourceGroup { get; set; }

        /// <summary>
        /// Gets or sets the Subscription Id.
        /// </summary>
        public string SubscriptionId { get; set; }

        /// <summary>
        /// Gets or sets the Account Name.
        /// </summary>
        public string AccountName { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether to run the checker function.
        /// </summary>
        public bool EnableChecker { get; set; }
    }
}