// "//-----------------------------------------------------------------------".
// <copyright file="CmsStatusNotificationsSettings.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Domain.Config
{
    using System;
    using System.Collections.Generic;
    using System.Text;

    /// <summary>
    ///   CmsStatusNotificationSettings.
    /// </summary>
    public class CmsStatusNotificationsSettings
    {
        /// <summary>
        /// Gets or sets the endpoint.
        /// </summary>
        /// <value>
        /// The endpoint.
        /// </value>
        public string Endpoint { get; set; }

        /// <summary>
        /// Gets or sets the retry count.
        /// </summary>
        /// <value>
        /// The retry count.
        /// </value>
        public int RetryCount { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether [ignore certificate validation].
        /// </summary>
        /// <value>
        ///   <c>true</c> if [ignore certificate validation]; otherwise, <c>false</c>.
        /// </value>
        public bool IgnoreCertificateValidation { get; set; }

        /// <summary>
        /// Gets or sets the API Key.
        /// </summary>
        /// <value>
        /// The key.
        /// </value>
        public string ApiKey { get; set; }

        /// <summary>
        /// Gets or sets the event grid topic.
        /// </summary>
        /// <value>
        /// The event grid topic.
        /// </value>
        public string EventGridTopic { get; set; }

        /// <summary>
        /// Gets or sets the type of the vod ingestion start event.
        /// </summary>
        /// <value>
        /// The type of the vod ingestion start event.
        /// </value>
        public string EventGridEventType { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether to enable event grid messaging.
        /// </summary>
        /// /// <value>
        /// The boolean indicating whether to enable event grid messaging.
        /// </value>
        public bool EnableEventNotifier { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether to enable Ecms notification.
        /// </summary>
        /// /// <value>
        /// The boolean indicating whether to enable Ecms notification.
        /// </value>
        public bool EnableEcmsNotification { get; set; }

        /// <summary>
        /// Gets or sets a value that specifies the MediaKind ENV Key.
        /// </summary>
        public string MediaKindEnvKey { get; set; }

        /// <summary>
        /// Gets the retries.
        /// </summary>
        /// <value>
        /// The retries.
        /// </value>
        internal int Retries => this.RetryCount != 0 ? this.RetryCount : 3;
    }
}
