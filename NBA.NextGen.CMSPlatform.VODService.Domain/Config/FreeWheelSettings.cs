// "//-----------------------------------------------------------------------".
// <copyright file="FreeWheelSettings.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Domain.Config
{
    /// <summary>
    /// FreeWheelSettings.
    /// </summary>
    public class FreeWheelSettings
    {
        /// <summary>
        /// Gets or sets a value indicating whether or not to call the freewheel api.
        /// </summary>
        public bool EnableCallback { get; set; }

        /// <summary>
        /// Gets or sets the FreeWheelAccessToken Endpoint.
        /// </summary>
        public string FreeWheelAccessTokenEndpoint { get; set; }

        /// <summary>
        /// Gets or sets the FreeWheelRest Endpoint.
        /// </summary>
        public string FreeWheelRestEndpoint { get; set; }

        /// <summary>
        /// Gets or sets the SecretsJsonPath.
        /// </summary>
        public string SecretsJsonPath { get; set; }
    }
}
