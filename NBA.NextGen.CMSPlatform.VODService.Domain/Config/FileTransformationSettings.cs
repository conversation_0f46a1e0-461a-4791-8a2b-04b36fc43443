using System;

namespace NBA.NextGen.CMSPlatform.VODService.Domain.Config;

public class FileTransformationSettings
{

    public string AccountName { get; set; }

    public string BlobConnectionString { get; set; }

    public string ContainerName { get; set; }

    public string CopyContainerName { get; set; }

    public string WscContainerPath { get; set; }

    public string InhouseJsonContainerPath { get; set; }

    public string InhouseXmlContainerPath { get; set; }

    public string NbaTvContainerPath { get; set; }

    public bool EnableMocking { get; set; }

    public string GmsDomain { get; set; }

    public string GmsUser { get; set; }

}
