// "//-----------------------------------------------------------------------".
// <copyright file="AssetRegistryDetails.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Domain.Common
{
    using System;
    using NBA.NextGen.CMSPlatform.VODService.Domain.Common.Enums;

    /// <summary>
    /// AssetRegistryDetails.
    /// </summary>
    public class AssetRegistryDetails
    {
        /// <summary>
        /// Gets or sets the asset id.
        /// </summary>
        public string AssetId { get; set; }

        /// <summary>
        /// Gets or sets the name of the flow that the asset came in through.
        /// </summary>
        public string IngestionFlowName { get; set; }

        /// <summary>
        /// Gets or sets the name of the file that created the asset.
        /// </summary>
        public string TriggerFileName { get; set; }

        /// <summary>
        /// Gets or sets the current state of the asset.
        /// </summary>
        public AssetStates AssetState { get; set; }

        /// <summary>
        /// Gets or sets the time of the last event in utc.
        /// </summary>
        public DateTime LastEventUtc { get; set; }
    }
}