// "//-----------------------------------------------------------------------".
// <copyright file="JsonKeys.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Domain.Common
{
    using System.Diagnostics.CodeAnalysis;

    /// <summary>
    /// JsonKeys.
    /// </summary>
    [ExcludeFromCodeCoverage]
    public static class JsonKeys
    {
        /// <summary>Id.</summary>
        public static readonly string Id = "id";

        /// <summary>Title.</summary>
        public static readonly string Title = "title";

        /// <summary>Publish Date.</summary>
        public static readonly string PublishDate = "publishDate";

        /// <summary>Description.</summary>
        public static readonly string Description = "description";

        /// <summary>Duration.</summary>
        public static readonly string Duration = "duration";

        /// <summary>Events.</summary>
        public static readonly string Events = "events";

        /// <summary>Players.</summary>
        public static readonly string Players = "players";

        /// <summary>Name.</summary>
        public static readonly string Name = "name";
    }
}
