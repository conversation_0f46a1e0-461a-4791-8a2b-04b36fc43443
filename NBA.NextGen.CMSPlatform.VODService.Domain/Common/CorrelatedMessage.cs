// "//-----------------------------------------------------------------------".
// <copyright file="CorrelatedMessage.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Domain.Common
{
    /// <summary>
    /// Correlation Request.
    /// </summary>
    public class CorrelatedMessage
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="CorrelatedMessage"/> class.
        /// </summary>
        /// <param name="correlationId">The correlation identifier.</param>
        protected CorrelatedMessage(string correlationId)
            : this()
        {
            this.CorrelationId = correlationId;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="CorrelatedMessage"/> class.
        /// </summary>
        protected CorrelatedMessage()
        {
        }

        /// <summary>
        /// Gets or Sets the Correlation Id.
        /// </summary>
        public string CorrelationId { get; set; }
    }
}