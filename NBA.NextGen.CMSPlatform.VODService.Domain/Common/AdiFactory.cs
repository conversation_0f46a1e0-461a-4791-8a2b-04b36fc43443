// "//-----------------------------------------------------------------------".
// <copyright file="AdiFactory.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Domain
{
    using System;
    using NBA.NextGen.CMSPlatform.VODService.Domain.Vcms;

    /// <summary>
    /// AdiFactory.
    /// </summary>
    public static class AdiFactory
    {
        /// <summary>
        /// Creates an ADI for the given params.
        /// </summary>
        /// <param name="assetId">The asset id.</param>
        /// <param name="assetLocation">The asset location.</param>
        /// <param name="assetTitle">The asset title.</param>
        /// <param name="uniqueId">The unique id for the asset. (used internally for vod).</param>
        /// <returns>An ADI.</returns>
        public static ADI CreateADI(string assetId, string assetLocation, string assetTitle, string uniqueId) => new Domain.Vcms.ADI()
        {
            Metadata = new Domain.Vcms.ADIMetadata()
            {
                AMS = new Domain.Vcms.ADIMetadataAMS()
                {
                    AssetClass = "package",
                    AssetID = assetId,
                    Provider = "NBA",
                    ProviderID = "NBA",
                },
            },
            Asset = new Domain.Vcms.ADIAsset()
            {
                Metadata = new Domain.Vcms.ADIAssetMetadata()
                {
                    AMS = new Domain.Vcms.ADIAssetMetadataAMS()
                    {
                        AssetClass = "title",
                        AssetID = $"{assetId}_title",
                        Provider = "NBA",
                        ProviderID = "NBA",
                    },
                    AppData = new System.Collections.ObjectModel.Collection<Domain.Vcms.ADIAssetMetadataAppData>
                            {
                                new Domain.Vcms.ADIAssetMetadataAppData()
                                {
                                    App = "MOD", Name = "Title", Value = $"AD: {assetTitle}",
                                },
                                new Domain.Vcms.ADIAssetMetadataAppData()
                                {
                                    App = "MOD", Name = "AD_Type", Value = "Direct",
                                },
                                new Domain.Vcms.ADIAssetMetadataAppData()
                                {
                                    App = "MOD", Name = "Media_Uid", Value = uniqueId,
                                },
                            },
                },
                Asset = new System.Collections.ObjectModel.Collection<Domain.Vcms.ADIAssetAsset>()
                        {
                           new Domain.Vcms.ADIAssetAsset()
                           {
                               Metadata = new Domain.Vcms.ADIAssetAssetMetadata()
                               {
                                   AMS = new Domain.Vcms.ADIAssetAssetMetadataAMS()
                                   {
                                       AssetClass = "movie",
                                       AssetID = $"{assetId}_movie",
                                       Provider = "NBA",
                                       ProviderID = "NBA",
                                   },
                               },
                               Content = new Domain.Vcms.ADIAssetAssetContent()
                               {
                                   Value = assetLocation,
                               },
                           },
                        },
            },
        };
    }
}
