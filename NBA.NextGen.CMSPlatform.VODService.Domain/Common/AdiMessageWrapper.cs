// "//-----------------------------------------------------------------------".
// <copyright file="AdiMessageWrapper.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Domain.Common
{
    using NBA.NextGen.CMSPlatform.VODService.Domain.Vcms;

    /// <summary>
    /// AdiMessageWrapper.
    /// </summary>
    public class AdiMessageWrapper
    {
        /// <summary>
        /// Gets or sets the ADI.
        /// </summary>
        public ADI Adi { get; set; }

        public int RetryCount {get;set;}
    }
}