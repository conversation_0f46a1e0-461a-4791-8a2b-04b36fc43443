// "//-----------------------------------------------------------------------".
// <copyright file="FileTypeConstants.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Domain.Common
{
    using System.Diagnostics.CodeAnalysis;

    /// <summary>
    /// FileTypeConstants.
    /// </summary>
    [ExcludeFromCodeCoverage]
    public static class FileTypeConstants
    {
        /// <summary>The inbound XML.</summary>
        public static readonly string InhouseXML = "InhouseXML";

        /// <summary>The nba tv.</summary>
        public static readonly string NbaTv = "NBATV";

        /// <summary>The WSC.</summary>
        public static readonly string WSC = "WSC";
    }
}
