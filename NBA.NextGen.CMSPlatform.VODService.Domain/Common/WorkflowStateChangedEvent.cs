// "//-----------------------------------------------------------------------".
// <copyright file="WorkflowStateChangedEvent.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Domain.Common
{
    using NBA.NextGen.CMSPlatform.VODService.Domain.Common.Enums;

    /// <summary>
    /// WorkflowStateChangedEvent.
    /// </summary>
    public class WorkflowStateChangedEvent
    {
        /// <summary>
        /// Gets or sets the workflow identifier.
        /// </summary>
        /// <value>
        /// The workflow identifier.
        /// </value>
        public string WorkflowId { get; set; }

        /// <summary>
        /// Gets or sets the requestor live event identifier.
        /// </summary>
        /// <value>
        /// The requestor live event identifier.
        /// </value>
        public string RequestorLiveEventId { get; set; }

        /// <summary>
        /// Gets or sets the new workflowstate.
        /// </summary>
        /// <value>
        /// The new state of the workflow.
        /// </value>
        public WorkflowState WorkflowState { get; set; }
    }
}
