// "//-----------------------------------------------------------------------".
// <copyright file="VodStatus.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Domain.Common.Enums
{
    using System;
    using System.Collections.Generic;
    using System.Runtime.Serialization;
    using System.Text;

    /// <summary>
    /// VodStatus.
    /// </summary>
    public enum VodStatus
    {
        /// <summary>
        /// The succeeded
        /// </summary>
        [EnumMember(Value = "succeeded")]
        Succeeded,

        /// <summary>
        /// The failed
        /// </summary>
        [EnumMember(Value = "failed")]
        Failed,
    }
}
