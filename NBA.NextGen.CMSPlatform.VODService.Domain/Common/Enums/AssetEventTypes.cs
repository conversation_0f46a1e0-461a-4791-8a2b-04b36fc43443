namespace NBA.NextGen.CMSPlatform.VODService.Domain.Common.Enums;

/// <summary>
/// AssetEventTypes.
/// </summary>
public enum AssetEventTypes
{
    /// <summary>
    /// Blob watcher has begun processing the asset.
    /// </summary>
    IngestionStarted = 1,

    /// <summary>
    /// Blob watcher has finished processing the asset.
    /// </summary>
    IngestionCompleted = 2,

    /// <summary>
    /// Blob watcher has failed to process the asset but will retry.
    /// </summary>
    IngestionPendingRetry = 3,

    /// <summary>
    /// Blob watcher has begun processing the asset again..
    /// </summary>
    IngestionRestarted = 4,

    /// <summary>
    /// Blob watcher has failed to process the asset.
    /// </summary>
    IngestionFailed = 5,

    /// <summary>
    /// Blob watcher has submitted to transcribing service.
    /// </summary>
    TranscribingRequested = 6,

    /// <summary>
    /// Transcribing has completed.
    /// </summary>
    TranscribingCompleted = 7,

    /// <summary>
    /// Transcribing has failed.
    /// </summary>
    TranscribingFailed = 8,

    /// <summary>
    /// Asset data has been queued for ecms submission.
    /// </summary>
    QueuedForEcmsSubmission = 10,

    /// <summary>
    /// Asset data has been sent to the ecms.
    /// </summary>
    SuccessfullySentToEcms = 11,

    /// <summary>
    /// Asset data has been sent to the ecms.
    /// </summary>
    EcmsResponseIndicatesFailure = 12,

    /// <summary>
    /// Asset data has been failed to post to the ecms.
    /// </summary>
    FailedToSendEcms = 13,

    /// <summary>
    /// Vcms response sent to the ecms.
    /// </summary>
    SuccessfullySentVcmsResponseToEcms = 14,

    /// <summary>
    /// Vcms response sent to the ecms.
    /// </summary>
    FailedToSendVcmsResponseToEcms = 15,

    /// <summary>
    /// Asset data has been queued for ecms submission.
    /// </summary>
    QueuedForEcmsUpdateSubmission = 16,

    /// <summary>
    /// Asset data has been queued for vcms submission.
    /// </summary>
    QueuedForVcmsSubmission = 20,

    /// <summary>
    /// Asset data has been sent to the vcms.
    /// </summary>
    SuccessfullySentToVcms = 21,

    /// <summary>
    /// Asset data has been failed to post to the vcms.
    /// </summary>
    FailedToSendVcms = 22,

    /// <summary>
    /// Vcms progress response received.
    /// </summary>
    VcmsProgressResponseReceived = 23,

    /// <summary>
    /// Vcms completed response received.
    /// </summary>
    VcmsCompletedResponseReceived = 24,

    /// <summary>
    /// Vcms failure response received.
    /// </summary>
    VcmsFailureResponseReceived = 25,

    /// <summary>
    /// Asset has finished processing.
    /// </summary>
    WorkflowCompleted = 30,

    /// <summary>
    /// Ingestion cannot proceed due to missing asset.
    /// </summary>
    IngestionBlockedAssetMissing = 901,

    /// <summary>
    /// Ingestion cannot proceed due to gms.
    /// </summary>
    IngestionFailedGMSIssue = 902,

    /// <summary>
    /// Asset is still missing after a follow up check.
    /// </summary>
    AssetStillMissingAfterScan = 912,

    /// <summary>
    /// Missing asset found and being resubmitted.
    /// </summary>
    AssetFoundResubmitted = 913,

    /// <summary>
    /// Missing asset failed to appear after given timeout (24hr).
    /// </summary>
    IngestionFailedAssetMissingTimeout = 914,

    EcmsSubmitMessageDeadLettered = 961,
    EcmsUpdateMessageDeadLettered = 962,
    VcmsSubmitMessageDeadLettered = 963,

    /// <summary>
    /// Ingestion cannot proceed due to missing asset, has hit error threshold.
    /// </summary>
    IngestionBlockedThresholdAssetMissing = 915,

    EcmsAssetCacheNotFound = 917,

    /// <summary>
    /// CUSTOM EVENT: Liberty Live missing captions.
    /// </summary>
    LibertyLiveMissingCaptions = 1010,

    FreeWheelRequestReceived = 1101,
    FreeWheelSentToVcms = 1102,
    FreeWheelVcmsResponseReceived = 1103,
    FreeWheelResponseSentSuccessfully = 1104,
    FreeWheelResponseFailedToSend = 1105,
    FreeWheelVcmsFailureResponseReceived = 1113,

    GamRequestReceived = 1201,
    GamSentToVcms = 1202,
    GamVcmsResponseReceived = 1203,
    GamResponseSentSuccessfully = 1204,
    GamResponseFailedToSend = 1205,
    GamVcmsFailureResponseReceived = 1213,

    TranscribingAdvanceToTranscribe = 1301,
    TranscribingBackflowCompleted = 1303
}

