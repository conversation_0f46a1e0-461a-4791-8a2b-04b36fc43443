// "//-----------------------------------------------------------------------".
// <copyright file="TrackingEventType.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Domain.Common.Enums
{
    using System.Runtime.Serialization;

    /// <summary>
    /// TrackingEventType.
    /// </summary>
    public enum TrackingEventType
    {
        /// <summary>
        /// The succeeded
        /// </summary>
        [EnumMember(Value = "succeeded")]
        Succeeded,

        /// <summary>
        /// The ProcessingElapsed
        /// </summary>
        [EnumMember(Value = "ProcessingElapsed")]
        ProcessingElapsed,

        /// <summary>
        /// The succeeded
        /// </summary>
        [EnumMember(Value = "Failed")]
        Failed,

        /// <summary>
        /// The pending
        /// </summary>
        [EnumMember(Value = "Pending")]
        Pending,

        /// <summary>
        /// The VodIngestionSucceeded
        /// </summary>
        [EnumMember(Value = "VodIngestionSucceeded")]
        VodIngestionSucceeded,

        /// <summary>
        /// The VodIngestionFailed
        /// </summary>
        [EnumMember(Value = "VodIngestionFailed")]
        VodIngestionFailed,

        /// <summary>
        /// The VodIngestionStarting
        /// </summary>
        [EnumMember(Value = "VodIngestionStarting")]
        VodIngestionStarting,

        /// <summary>
        /// The StartProcessing
        /// </summary>
        [EnumMember(Value = "StartProcessing")]
        StartProcessing,

        /// <summary>
        /// The ECMSPostCreationSuccess
        /// </summary>
        [EnumMember(Value = "ECMSPostCreationSuccess")]
        ECMSPostCreationSuccess,

        /// <summary>
        /// The ECMSPostCreationFailed
        /// </summary>
        [EnumMember(Value = "ECMSPostCreationFailed")]
        ECMSPostCreationFailed,

        /// <summary>
        /// The CMSNotificationSuccess
        /// </summary>
        [EnumMember(Value = "CMSNotificationSuccess")]
        CMSNotificationSuccess,

        /// <summary>
        /// The MediaKindProgress
        /// </summary>
        [EnumMember(Value = "MediaKindProgress")]
        MediaKindProgress,

        /// <summary>
        /// The MediaKindProgressComplete
        /// </summary>
        [EnumMember(Value = "MediaKindProgressComplete")]
        MediaKindProgressComplete,

        /// <summary>
        /// The MediaKindProgressDelay
        /// </summary>
        [EnumMember(Value = "MediaKindProgressDelay")]
        MediaKindProgressDelay,
    }
}
