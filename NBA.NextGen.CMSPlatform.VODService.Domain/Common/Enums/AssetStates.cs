// "//-----------------------------------------------------------------------".
// <copyright file="AssetStates.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Domain.Common.Enums
{
    /// <summary>
    /// AssetStates.
    /// </summary>
    public enum AssetStates
    {
        /// <summary>
        /// Asset in an unknown state.
        /// </summary>
        Unknown = 0,

        /// <summary>
        /// New Asset processing in blob watcher.
        /// </summary>
        Processing = 1,

        /// <summary>
        /// Asset is with transcription service.
        /// </summary>
        AwaitingTranscribing = 3,

        /// <summary>
        /// Asset is with ecms.
        /// </summary>
        AwaitingEcms = 5,

        /// <summary>
        /// Asset is with vcms.
        /// </summary>
        AwaitingVcms = 10,

        /// <summary>
        /// Asset has failed.
        /// </summary>
        Failed = 50,

        /// <summary>
        /// Asset is completed.
        /// </summary>
        Completed = 100,
    }
}