// "//-----------------------------------------------------------------------".
// <copyright file="WorkflowState.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Domain.Common.Enums
{
    using System.Runtime.Serialization;

    /// <summary>
    /// The <see cref="WorkflowState"/>.
    /// </summary>
    public enum WorkflowState
    {
        /// <summary>
        /// None, no info.
        /// </summary>
        [EnumMember(Value = "none")]
        None,

        /// <summary>
        /// Requesting, the workflow is actively being requested.
        /// </summary>
        [EnumMember(Value = "requesting")]
        Requesting,

        /// <summary>
        /// Requested, the workflow has been requested but is not yet in progress.
        /// </summary>
        [EnumMember(Value = "requested")]
        Requested,

        /// <summary>
        /// InProgress, the workflow has begun but is not complete.
        /// </summary>
        [EnumMember(Value = "inprogress")]
        InProgress,

        /// <summary>
        /// Completed, the workflow completed successfully.
        /// </summary>
        [EnumMember(Value = "completed")]
        Completed,

        /// <summary>
        /// Failed, the workflow failed to complete.
        /// </summary>
        [EnumMember(Value = "failed")]
        Failed,

        /// <summary>
        /// AbortedByUser, the worflow was aborted by a user.
        /// </summary>
        [EnumMember(Value = "abortedbyuser")]
        AbortedByUser,
    }
}
