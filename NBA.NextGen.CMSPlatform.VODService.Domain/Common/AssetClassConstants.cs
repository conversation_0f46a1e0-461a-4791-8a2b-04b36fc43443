// "//-----------------------------------------------------------------------".
// <copyright file="AssetClassConstants.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Domain.Common
{
    using System.Diagnostics.CodeAnalysis;

    /// <summary>
    /// AssetClassConstants.
    /// </summary>
    [ExcludeFromCodeCoverage]
    public static class AssetClassConstants
    {
        /// <summary>Movie.</summary>
        public static readonly string Movie = "movie";

        /// <summary>Mezzanine.</summary>
        public static readonly string Mezzanine = "mezzanine";

        /// <summary>Captioning.</summary>
        public static readonly string Captioning = "captioning";
    }
}
