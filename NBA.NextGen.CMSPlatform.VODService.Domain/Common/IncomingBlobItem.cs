// "//-----------------------------------------------------------------------".
// <copyright file="IncomingBlobItem.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Domain.Common
{
    /// <summary>
    /// IncomingBlobItem.
    /// </summary>
    public class IncomingBlobItem
    {
        /// <summary>
        /// Gets or sets the name of the blob item.
        /// </summary>
        public string BlobName { get; set; }
    }
}