// "//-----------------------------------------------------------------------".
// <copyright file="EventTypes.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Domain.Common
{
    /// <summary>
    /// The Event Types.
    /// </summary>
    public static class EventTypes
    {
        /// <summary>
        /// The archive status updated.
        /// </summary>
        public const string ArchiveStatusUpdated = "Archive.StatusUpdated";
    }
}
