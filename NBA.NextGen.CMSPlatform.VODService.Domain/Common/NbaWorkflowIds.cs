// "//-----------------------------------------------------------------------".
// <copyright file="NbaWorkflowIds.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Domain.Common
{
    /// <summary>
    /// NbaWorkflowIds.
    /// </summary>
    public static class NbaWorkflowIds
    {
        /// <summary>
        /// The identifier for event metadata setup workflow.
        /// </summary>
        public const string EventMetadataSetup = nameof(EventMetadataSetup);

        /// <summary>
        /// The identifier for event infrastructure setup workflow.
        /// </summary>
        public const string EventInfrastructureSetup = nameof(EventInfrastructureSetup);

        /// <summary>
        /// The identifier for event metadata start workflow.
        /// </summary>
        public const string EventMetadataStart = nameof(EventMetadataStart);

        /// <summary>
        /// The identifier for event infrastructure start workflow.
        /// </summary>
        public const string EventInfrastructureStart = nameof(EventInfrastructureStart);

        /// <summary>
        /// The identifier for event metadata end workflow.
        /// </summary>
        public const string EventMetadataEnd = nameof(EventMetadataEnd);

        /// <summary>
        /// The identifier for event infrastructure end workflow.
        /// </summary>
        public const string EventInfrastructureEnd = nameof(EventInfrastructureEnd);

        /// <summary>
        /// The identifier for event metadata cleanup workflow.
        /// </summary>
        public const string EventMetadataCleanup = nameof(EventMetadataCleanup);

        /// <summary>
        /// The identifier for event infrastructure cleanup workflow.
        /// </summary>
        public const string EventInfrastructureCleanup = nameof(EventInfrastructureCleanup);

        /// <summary>
        /// The GUID for Viewing policy setup workflow.
        /// </summary>
        public const string ViewingPolicySetup = nameof(ViewingPolicySetup);
    }
}
