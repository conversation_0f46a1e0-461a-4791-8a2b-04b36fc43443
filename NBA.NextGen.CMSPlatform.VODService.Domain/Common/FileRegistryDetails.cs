// "//-----------------------------------------------------------------------".
// <copyright file="FileRegistryDetails.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Domain.Common
{
    using System;

    /// <summary>
    /// FileRegistryDetails.
    /// </summary>
    public class FileRegistryDetails
    {
        /// <summary>
        /// Gets or sets the filename.
        /// </summary>
        public string FileName { get; set; }

        /// <summary>
        /// Gets or sets the name of the flow that the asset came in through.
        /// </summary>
        public string IngestionFlowName { get; set; }

        /// <summary>
        /// Gets or sets the parent folder name.
        /// </summary>
        public string ParentFolderName { get; set; }

        /// <summary>
        /// Gets or sets the time, in utc, when the file arrived.
        /// </summary>
        public DateTime ArrivalTimeUtc { get; set; }

        /// <summary>
        /// Gets or sets the time, in utc, when the file went through the dispatcher.
        /// </summary>
        public DateTime ProcessedTimeUtc { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether this file is a .
        /// </summary>
        public bool IsWorkflowTriggerFile { get; set; }

        /// <summary>
        /// Gets or sets the asset id if one is resolved from the file.
        /// </summary>
        public string AssetId { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether a file failed to be processed.
        /// </summary>
        public bool IsFailure { get; set; }

        /// <summary>
        /// Gets or sets the Exception Message.
        /// </summary>
        public string ExceptionMessage { get; set; }
    }
}