// "//-----------------------------------------------------------------------".
// <copyright file="ActivityFunctionNames.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Domain.Common
{
    /// <summary>
    /// The name of the activity functions.
    /// </summary>
    public static class ActivityFunctionNames
    {
        /// <summary>
        /// The save game end details.
        /// </summary>
        public const string SaveGameEndDetails = nameof(SaveGameEndDetails);

        /// <summary>
        /// The save game archive details.
        /// </summary>
        public const string SaveGameArchiveDetails = nameof(SaveGameArchiveDetails);

        /// <summary>
        /// The Create Segments details.
        /// </summary>
        public const string CreateSegments = nameof(CreateSegments);

        /// <summary>
        /// The publish segments.
        /// </summary>
        public const string PublishSegments = nameof(PublishSegments);

        /// <summary>
        /// The get game archive details.
        /// </summary>
        public const string GetGameArchiveDetails = nameof(GetGameArchiveDetails);

        /// <summary>
        /// The notify upload failure.
        /// </summary>
        public const string NotifyUploadFailure = nameof(NotifyUploadFailure);

        /// <summary>
        /// The analyze segment.
        /// </summary>
        public const string AnalyzeSegment = nameof(AnalyzeSegment);

        /// <summary>
        /// The get game archive adi.
        /// </summary>
        public const string GetGameArchiveAdi = nameof(GetGameArchiveAdi);

        /// <summary>
        /// The update migration status.
        /// </summary>
        public const string UpdateMigrationStatus = nameof(UpdateMigrationStatus);

        /// <summary>
        /// The submit to vod.
        /// </summary>
        public const string SubmitToVod = nameof(SubmitToVod);

        /// <summary>
        /// The update analyzer acivity.
        /// </summary>
        public const string UpdateAnalyzer = nameof(UpdateAnalyzer);
    }
}
