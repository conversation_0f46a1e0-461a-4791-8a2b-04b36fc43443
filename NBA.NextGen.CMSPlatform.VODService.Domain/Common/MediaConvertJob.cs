using System;
using Newtonsoft.Json;

namespace NBA.NextGen.CMSPlatform.VODService.Domain.Common;

public class MediaConvertJob
{
    [JsonProperty("id")]
    public string Id { get; set; } = string.Empty;
    public string EncodedContent { get; set; } = string.Empty;
    public DateTime CreatedOn { get; set; }
    public DateTime LastCheckedAtUtc { get; set; }
    public string TriggerAsset { get; set; } = string.Empty;
    public string AssetId { get; set; } = string.Empty;
    public string ResourceId { get; set; } = string.Empty;
    public string JobId { get; set; } = string.Empty;
    public string AssetUrl { get; set; } = string.Empty;
    public string OutputUrl { get; set; } = string.Empty;
    public string BucketName { get; set; } = string.Empty;
}
