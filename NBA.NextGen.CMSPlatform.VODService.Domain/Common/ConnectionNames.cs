// "//-----------------------------------------------------------------------".
// <copyright file="ConnectionNames.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Domain.Common
{
    /// <summary>
    /// Connection names for the orchestrator.
    /// </summary>
    public static class ConnectionNames
    {
        /// <summary>
        /// The task hub connection name.
        /// </summary>
        public const string TaskHubConnectionName = "Storage";
    }
}
