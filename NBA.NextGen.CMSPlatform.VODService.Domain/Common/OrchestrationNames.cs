// "//-----------------------------------------------------------------------".
// <copyright file="OrchestrationNames.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Domain.Common
{
    /// <summary>
    /// The orchestration names.
    /// </summary>
    public static class OrchestrationNames
    {
        /// <summary>
        /// The upload monitor notification orchestration.
        /// </summary>
        public const string UploadMonitorNotificationOrchestration = nameof(UploadMonitorNotificationOrchestration);

        /// <summary>
        /// The upload game archive orchestration.
        /// </summary>
        public const string UploadGameArchiveOrchestration = nameof(UploadGameArchiveOrchestration);

        /// <summary>
        /// The start segment migration orchestration.
        /// </summary>
        public const string StartSegmentMigrationOrchestration = nameof(StartSegmentMigrationOrchestration);
    }
}
