// "//-----------------------------------------------------------------------".
// <copyright file="SubmitToEcmsWrapper.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Domain.Common
{
    /// <summary>
    /// SubmitToEcmsWrapper.
    /// </summary>
    public class SubmitToEcmsWrapper
    {
        public string ResourceId { get; set; }
        public string ContextPath { get; set; }
        public string Endpoint { get; set; }
        public string FilePath { get; set; }
        public string CacheId { get; set; }
        public int RetryCount { get; set; }
    }
}