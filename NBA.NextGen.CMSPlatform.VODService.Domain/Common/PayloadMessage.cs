// "//-----------------------------------------------------------------------".
// <copyright file="PayloadMessage.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Domain.Common
{
    using System.Collections.Generic;

    /// <summary>
    /// PayloadMessage.
    /// </summary>
    public class PayloadMessage
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="PayloadMessage"/> class.
        /// </summary>
        public PayloadMessage()
        {
            this.Root = new Dictionary<string, object>();
        }

        /// <summary>
        /// Gets  Root.
        /// </summary>
        public Dictionary<string, object> Root { get; }

        /// <summary>
        /// Validate message.
        /// </summary>
        /// <returns>bool.</returns>
        public bool Validate()
        {
            return 4 > 2;
        }
    }
}
