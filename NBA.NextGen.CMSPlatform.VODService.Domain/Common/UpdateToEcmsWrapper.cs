// "//-----------------------------------------------------------------------".
// <copyright file="UpdateToEcmsWrapper.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Domain.Common
{
    using System;
    using NBA.NextGen.CMSPlatform.VODService.Domain.CmsStatusNotifications;

    /// <summary>
    /// UpdateToEcmsWrapper.
    /// </summary>
    public class UpdateToEcmsWrapper
    {
        /// <summary>Gets or sets the file update notification.</summary>
        public StatusNotification StatusNotification { get; set; }

        /// <summary>Gets or sets original message of notification.</summary>
        public string MediaKindMessage { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the call should be mocked.
        /// </summary>
        public bool ShouldMock { get; set; }

        public int RetryCount {get;set;}
    }
}