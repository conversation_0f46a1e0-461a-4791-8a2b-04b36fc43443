// "//-----------------------------------------------------------------------".
// <copyright file="CreativeRendition.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Domain.FreeWheel.Response
{
    using System;
    using System.Xml.Serialization;

    /// <summary>
    /// CreativeRendition.
    /// </summary>
    [Serializable]
    [System.ComponentModel.DesignerCategory("code")]
    [System.Xml.Serialization.XmlType(AnonymousType = true)]
    public class CreativeRendition
    {
        /// <summary>
        /// Gets or sets the ContentType property.
        /// </summary>
        [XmlElement(ElementName = "contentType")]
        public string ContentType { get; set; }

        /// <summary>
        /// Gets or sets the Location propoerty.
        /// </summary>
        [XmlElement(ElementName = "location")]
        public string Location { get; set; }

        /// <summary>
        /// Gets or sets the Duration property.
        /// </summary>
        [XmlElement(ElementName = "duration")]
        public double Duration { get; set; }

        /// <summary>
        /// Gets or sets the DurationType property.
        /// </summary>
        [XmlElement(ElementName = "durationType")]
        public string DurationType { get; set; }

        /// <summary>
        /// Gets or sets the MediaInfo property.
        /// </summary>
        [XmlElement(ElementName = "mediainfo")]
        public Mediainfo Mediainfo { get; set; }

        /// <summary>
        /// Gets or sets the Id3 property.
        /// </summary>
        [XmlElement(ElementName = "id3")]
        public Id3 Id3 { get; set; }
    }
}
