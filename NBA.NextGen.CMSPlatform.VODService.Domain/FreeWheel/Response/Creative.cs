// "//-----------------------------------------------------------------------".
// <copyright file="Creative.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Domain.FreeWheel.Response
{
    using System;
    using System.Collections.Generic;
    using System.Collections.ObjectModel;
    using System.Xml.Serialization;

    /// <summary>
    /// Creative.
    /// </summary>
    [Serializable]
    [System.ComponentModel.DesignerCategory("code")]
    [System.Xml.Serialization.XmlType(AnonymousType = true)]
    public class Creative
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="Creative" /> class.
        /// </summary>
        public Creative()
        {
            this.CreativeRendition = new Collection<CreativeRendition>();
        }

        /// <summary>
        /// Gets or Sets the Name property.
        /// </summary>
        [XmlElement(ElementName = "name")]
        public string Name { get; set; }

        /// <summary>
        /// Gets or Sets the NetworkId property.
        /// </summary>
        [XmlElement(ElementName = "networkId")]
        public string NetworkId { get; set; }

        /// <summary>
        /// Gets or Sets the SourceCreativeRendition property.
        /// </summary>
        [XmlElement(ElementName = "sourceCreativeRendition")]
        public SourceCreativeRendition SourceCreativeRendition { get; set; }

        /// <summary>
        /// Gets the CreativeRendition property.
        /// </summary>
        [XmlElement(ElementName = "creativeRendition")]
        public Collection<CreativeRendition> CreativeRendition { get; }
    }
}
