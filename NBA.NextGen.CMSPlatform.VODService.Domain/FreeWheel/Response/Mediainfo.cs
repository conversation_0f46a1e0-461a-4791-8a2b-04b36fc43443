// "//-----------------------------------------------------------------------".
// <copyright file="Mediainfo.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Domain.FreeWheel.Response
{
    using System;
    using System.Xml.Serialization;

    /// <summary>
    /// Mediainfo.
    /// </summary>
    [Serializable]
    [System.ComponentModel.DesignerCategory("code")]
    [System.Xml.Serialization.XmlType(AnonymousType = true)]
    public class Mediainfo
    {
        /// <summary>
        /// Gets or sets the Width property.
        /// </summary>
        [XmlElement(ElementName = "width")]
        public int Width { get; set; }

        /// <summary>
        /// Gets or sets the Height property.
        /// </summary>
        [XmlElement(ElementName = "height")]
        public int Height { get; set; }

        /// <summary>
        /// Gets or sets the Filesize property.
        /// </summary>
        [XmlElement(ElementName = "filesize")]
        public int Filesize { get; set; }

        /// <summary>
        /// Gets or sets the Bitrate property.
        /// </summary>
        [XmlElement(ElementName = "bitrate")]
        public string Bitrate { get; set; }

        /// <summary>
        /// Gets or sets the Fps property.
        /// </summary>
        [XmlElement(ElementName = "fps")]
        public string Fps { get; set; }
    }
}
