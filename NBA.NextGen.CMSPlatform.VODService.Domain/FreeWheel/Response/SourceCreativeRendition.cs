// "//-----------------------------------------------------------------------".
// <copyright file="SourceCreativeRendition.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Domain.FreeWheel.Response
{
    using System;
    using System.Xml.Serialization;

    /// <summary>
    /// SourceCreativeRendition.
    /// </summary>
    [Serializable]
    [System.ComponentModel.DesignerCategory("code")]
    [System.Xml.Serialization.XmlType(AnonymousType = true)]
    public class SourceCreativeRendition
    {
        /// <summary>
        /// Gets or sets the Id property.
        /// </summary>
        [XmlElement(ElementName = "id")]
        public string Id { get; set; }
    }
}
