// "//-----------------------------------------------------------------------".
// <copyright file="Id3.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Domain.FreeWheel.Response
{
    using System;
    using System.Xml.Serialization;

    /// <summary>
    /// Id3.
    /// </summary>
    [Serializable]
    [System.ComponentModel.DesignerCategory("code")]
    [System.Xml.Serialization.XmlType(AnonymousType = true)]
    public class Id3
    {
        /// <summary>
        /// Gets or sets the Key property.
        /// </summary>
        [XmlElement(ElementName = "key")]
        public string Key { get; set; }

        /// <summary>
        /// Gets or sets the Value property.
        /// </summary>
        [XmlElement(ElementName = "value")]
        public string Value { get; set; }

        /// <summary>
        /// Gets or sets the TagType property.
        /// </summary>
        [XmlElement(ElementName = "TagType")]
        public object TagType { get; set; }

        /// <summary>
        /// Gets or sets Timestamp property.
        /// </summary>
        [XmlElement(ElementName = "Timestamp")]
        public string Timestamp { get; set; }
    }
}
