// "//-----------------------------------------------------------------------".
// <copyright file="Creative.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Domain.FreeWheel.Request
{
    using System;
    using System.Xml.Serialization;

    /// <summary>
    /// Creative.
    /// </summary>
    [Serializable]
    [System.ComponentModel.DesignerCategory("code")]
    [System.Xml.Serialization.XmlType(AnonymousType = true)]
    public class Creative
    {
        /// <summary>
        /// Gets or sets the Name property.
        /// </summary>
        [XmlElement(ElementName = "name")]
        public string Name { get; set; }

        /// <summary>
        /// Gets or sets the NetworkId property.
        /// </summary>
        [XmlElement(ElementName = "networkId")]
        public string NetworkId { get; set; }

        /// <summary>
        /// Gets or sets the SourceCreativeRendition property.
        /// </summary>
        [XmlElement(ElementName = "sourceCreativeRendition")]
        public SourceCreativeRendition SourceCreativeRendition { get; set; }

        /// <summary>
        /// Gets or sets the CreativeRendition property.
        /// </summary>
        [XmlElement(ElementName = "creativeRendition")]
        public CreativeRendition CreativeRendition { get; set; }
    }
}
