// "//-----------------------------------------------------------------------".
// <copyright file="Transcode.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Domain.FreeWheel.Request
{
    using System;
    using System.Xml.Serialization;

    /// <summary>
    /// Transcode.
    /// </summary>
    [Serializable]
    [System.ComponentModel.DesignerCategory("code")]
    [System.Xml.Serialization.XmlType(AnonymousType = true)]
    [System.Xml.Serialization.XmlRoot("transcode", Namespace = "", IsNullable = false)]
    public class Transcode
    {
        /// <summary>
        /// Gets or sets the SessionId property.
        /// </summary>
        [XmlElement(ElementName = "sessionId")]
        public string SessionId { get; set; }

        /// <summary>
        /// Gets or sets the Creatvie property.
        /// </summary>
        [XmlElement(ElementName = "creative")]
        public Creative Creative { get; set; }
    }
}
