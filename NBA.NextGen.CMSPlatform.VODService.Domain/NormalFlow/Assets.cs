// "//-----------------------------------------------------------------------".
// <copyright file="Assets.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Domain.NormalFlow
{
    using System.Collections.Generic;
    using Newtonsoft.Json;

    /// <summary>
    /// Assets.
    /// </summary>
    public class Assets
    {
        /// <summary>
        /// Gets or sets the Mp4 uri.
        /// </summary>
        [JsonProperty("mp4")]
        public string Mp4 { get; set; }

        /// <summary>
        /// Gets or sets the Jpg uri.
        /// </summary>
        [JsonProperty("jpg")]
        public string Jpg { get; set; }

        /// <summary>
        /// Gets or sets the subtitles dictionary.
        /// </summary>
        [JsonProperty("cc")]
        public Dictionary<string, string> CC { get; set; }
    }
}
