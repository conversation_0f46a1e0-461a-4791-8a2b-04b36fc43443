// "//-----------------------------------------------------------------------".
// <copyright file="NbaBetPayload.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Domain.NormalFlow
{
    using NBA.NextGen.CMSPlatform.VODService.Domain.Common;

    /// <summary>
    /// NbaBetPayload.
    /// </summary>
    public class NbaBetPayload : PayloadMessage
    {
        /// <summary>
        /// Gets or sets the Asset.
        /// </summary>
        public string Asset { get; set; }

        /// <summary>
        /// Gets or sets the Assets.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("assets")]
        public Assets Assets { get; set; }

        /// <summary>
        /// Gets or sets the Data.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("data")]
        public NbaBetPayloadData Data { get; set; }
    }
}
