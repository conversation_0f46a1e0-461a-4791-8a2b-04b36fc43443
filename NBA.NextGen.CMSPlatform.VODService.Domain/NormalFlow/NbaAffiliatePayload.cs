// "//-----------------------------------------------------------------------".
// <copyright file="NbaAffiliatePayload.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Domain.NormalFlow
{
    using NBA.NextGen.CMSPlatform.VODService.Domain.Common;

    /// <summary>
    /// NbaAffiliatePayload.
    /// </summary>
    public class NbaAffiliatePayload : PayloadMessage
    {
        /// <summary>
        /// Gets or sets the Asset.
        /// </summary>
        public string Asset { get; set; }

        /// <summary>
        /// Gets or sets the Assets.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("assets")]
        public Assets Assets { get; set; }

        /// <summary>
        /// Gets or sets the Data.
        /// </summary>
        [Newtonsoft.Json.JsonProperty("data")]
        public NbaAffiliatePayloadData Data { get; set; }
    }
}