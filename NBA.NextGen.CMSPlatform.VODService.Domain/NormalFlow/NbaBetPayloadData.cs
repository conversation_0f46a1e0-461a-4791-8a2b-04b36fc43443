// "//-----------------------------------------------------------------------".
// <copyright file="NbaBetPayloadData.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Domain.NormalFlow
{
    using System;
    using System.Collections.Generic;

    /// <summary>
    /// NbaBetPayloadData.
    /// </summary>
    public class NbaBetPayloadData
    {
        /// <summary>
        /// Gets or sets the AssetId.
        /// </summary>
        public string AssetID { get; set; }

        /// <summary>
        /// Gets or sets the Version.
        /// </summary>
        public string Version { get; set; }

        /// <summary>
        /// Gets or sets the Workflow.
        /// </summary>
        public string Workflow { get; set; }

        /// <summary>
        /// Gets or sets the Publisher.
        /// </summary>
        public string Publisher { get; set; }

        /// <summary>
        /// Gets or sets the list of Franchise.
        /// </summary>
        public List<string> Franchise { get; set; }

        /// <summary>
        /// Gets or sets the Publish Date.
        /// </summary>
        public DateTime PublishDate { get; set; }

        /// <summary>
        /// Gets or sets the Title.
        /// </summary>
        public string Title { get; set; }

        /// <summary>
        /// Gets or sets the Description.
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// Gets or sets the Video.
        /// </summary>
        public string Video { get; set; }

        /// <summary>
        /// Gets or sets the Thumbail.
        /// </summary>
        public string Thumbnail { get; set; }

        /// <summary>
        /// Gets or sets the list of Teams.
        /// </summary>
        public List<string> Teams { get; set; }

        /// <summary>
        /// Gets or sets the list of Players.
        /// </summary>
        public List<string> Players { get; set; }

        /// <summary>
        /// Gets or sets the list of Tags.
        /// </summary>
        public List<string> Tags { get; set; }
    }
}
