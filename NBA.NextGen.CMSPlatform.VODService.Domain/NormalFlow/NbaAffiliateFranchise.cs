// "//-----------------------------------------------------------------------".
// <copyright file="NbaAffiliateFranchise.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Domain.NormalFlow
{
    using Newtonsoft.Json;

    /// <summary>
    /// NbaAffiliateFranchise.
    /// </summary>
    public class NbaAffiliateFranchise
    {
        /// <summary>
        /// Gets or sets the Video Type.
        /// </summary>
        [JsonProperty("Video_Type")]
        public string VideoType { get; set; }

        /// <summary>
        /// Gets or sets the Video Category.
        /// </summary>
        [JsonProperty("Video_Category")]
        public string VideoCategory { get; set; }

        /// <summary>
        /// Gets or sets the Video Franchise.
        /// </summary>
        [JsonProperty("Video_Franchise")]
        public string VideoFranchise { get; set; }

        /// <summary>
        /// Gets or sets the Video Franchise Name.
        /// </summary>
        [JsonProperty("Video_Franchise_Name")]
        public string VideoFranchiseName { get; set; }
    }
}