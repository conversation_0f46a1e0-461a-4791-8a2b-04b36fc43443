// "//-----------------------------------------------------------------------".
// <copyright file="NbaAffiliatePayloadData.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Domain.NormalFlow
{
    using System;
    using System.Collections.Generic;
    using Newtonsoft.Json;

    /// <summary>
    /// NbaAffiliatePayloadData.
    /// </summary>
    public class NbaAffiliatePayloadData
    {
        /// <summary>
        /// Gets or sets the asset id.
        /// </summary>
        public string AssetID { get; set; }

        /// <summary>
        /// Gets or sets the workflow.
        /// </summary>
        public string Workflow { get; set; }

        /// <summary>
        /// Gets or sets the publisher.
        /// </summary>
        public string Publisher { get; set; }

        /// <summary>
        /// Gets or sets the version.
        /// </summary>
        public string Version { get; set; }

        /// <summary>
        /// Gets or sets the published date.
        /// </summary>
        public DateTime PublishDate { get; set; }

        /// <summary>
        /// Gets or sets the video file name.
        /// </summary>
        public string Video { get; set; }

        /// <summary>
        /// Gets or sets the thumbnail file name.
        /// </summary>
        public string Thumbnail { get; set; }

        /// <summary>
        /// Gets or sets the title.
        /// </summary>
        public string Title { get; set; }

        /// <summary>
        /// Gets or sets the description.
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// Gets or sets the list of franchises.
        /// </summary>
        public NbaAffiliateFranchise Franchise { get; set; }

        /// <summary>
        /// Gets or sets the list of tags.
        /// </summary>
        public List<string> Tags { get; set; }

        /// <summary>
        /// Gets or sets the list of teams.
        /// </summary>
        public List<string> Teams { get; set; }

        /// <summary>
        /// Gets or sets the list of players.
        /// </summary>
        public List<string> Players { get; set; }

        /// <summary>
        /// Gets or sets the list of entitlements.
        /// </summary>
        public string Entitlement { get; set; }

        /// <summary>
        /// Gets or sets the duration.
        /// </summary>
        public string Duration { get; set; }
    }
}