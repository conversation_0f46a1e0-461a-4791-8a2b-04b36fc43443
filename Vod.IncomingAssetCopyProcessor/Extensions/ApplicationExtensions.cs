using System.Diagnostics.CodeAnalysis;
using MST.Common.Azure.Extensions;
using Vod.Common.Extensions;
using Azure.Storage.Blobs;
using Azure.Messaging.ServiceBus;
using MST.Common.MongoDB.Extensions;

namespace Vod.IncomingAssetCopyProcessor.Extensions;

[ExcludeFromCodeCoverage]
public static class ApplicationExtensions
{
    public static IServiceCollection AddApplicationDependencies(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddSharedApplicationDependencies(configuration);
        return services;
    }

    public static IServiceCollection AddApplicationHandlers(this IServiceCollection services, IConfiguration configuration)
    {
        var normalFlowBlobConnectionString = configuration.GetValue<string>("blob_connection_string");
        var normalFlowCopyContainerName = configuration.GetSection("NormalFlowSettings").GetValue<string>("CopyContainerName");
        var directFlowCopyContainerName = configuration.GetSection("FileTransformationSettings").GetValue<string>("CopyContainerName");
        var live2vodCopyContainerName = configuration.GetSection("LiveToVodSettings").GetValue<string>("CopyContainerName");
        var vcmsStorageCopyContainerName = configuration.GetSection("VcmsStorageSettings").GetValue<string>("CopyContainerName");

        var blobClient = new BlobServiceClient(normalFlowBlobConnectionString);
        services.AddSingleton(blobClient);

        services.RegisterBlobDataContainer(normalFlowCopyContainerName);
        services.RegisterBlobDataContainer(directFlowCopyContainerName);
        services.RegisterBlobDataContainer(live2vodCopyContainerName);
        services.RegisterBlobDataContainer(vcmsStorageCopyContainerName);

        var queueConnectionString = configuration.GetValue<string>("service_bus_connection_string");
        var queueName = configuration.GetSection("QueueNames")["AssetCopy"] ?? throw new NullReferenceException();

        var sbClient = new ServiceBusClient(queueConnectionString, new ServiceBusClientOptions
        {
            TransportType = ServiceBusTransportType.AmqpWebSockets,
        });
        services.AddSingleton(sbClient);

        var mongoDbName = configuration.GetSection("MongoSettings").GetValue<string>("DatabaseName") ?? throw new NullReferenceException();
        services.RegisterMongoDBRepository<AssetCopyRequest>(mongoDbName, "assetcopyrequests", x => x.Id);

        var enableConsumer = configuration.GetSection("AssetCopySettings").GetValue<bool>("EnableConsumer");
        if (enableConsumer)
        {
            services.AddKeyedSingleton("signal-event", new AutoResetEvent(true));
            services.AddHostedService<AssetCopyService>();
            services.RegisterServiceBusQueueConsumer<IncomingAssetCopyMessageHandler>(queueName);
        }

        return services;
    }
}