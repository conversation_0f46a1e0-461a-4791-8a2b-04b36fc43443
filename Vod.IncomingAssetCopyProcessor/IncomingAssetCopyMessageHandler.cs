using Microsoft.Extensions.Options;
using MST.Common.Data;
using MST.Common.Messaging;
using NBA.NextGen.CMSPlatform.VODService.Domain.Common.Enums;
using NBA.NextGen.CMSPlatform.VODService.Domain.Config;
using NBA.NextGen.CMSPlatform.VODService.Domain.Common;

namespace Vod.IncomingAssetCopyProcessor;

public class IncomingAssetCopyMessageHandler : IMessageHandler
{
    private readonly ILogger _logger;
    private readonly IDataStoreFactory _dataStoreFactory;
    private readonly AssetCopySettings _settings;
    private readonly NormalFlowSettings _normalFlowSettings;
    private readonly FileTransformationSettings _fileTransformationSettings;
    private readonly LiveToVodSettings _liveToVodSettings;
    private readonly AutoResetEvent _signalEvent;
    private readonly IQueryableRepository<AssetCopyRequest> _repository;

    public IncomingAssetCopyMessageHandler(
        ILogger<IncomingAssetCopyMessageHandler> logger,
        IDataStoreFactory blobClientProvider,
        IQueryableRepositoryFactory repositoryFactory,
        IOptions<AssetCopySettings> settings,
        IOptions<NormalFlowSettings> normalFlowSettings,
        IOptions<FileTransformationSettings> fileTransformationSettings,
        IOptions<LiveToVodSettings> liveToVodSettings,
        [FromKeyedServices("signal-event")] AutoResetEvent signalEvent
    )
    {
        _logger = logger;
        _dataStoreFactory = blobClientProvider;
        _settings = settings.Value;
        _normalFlowSettings = normalFlowSettings.Value;
        _fileTransformationSettings = fileTransformationSettings.Value;
        _liveToVodSettings = liveToVodSettings.Value;
        _signalEvent = signalEvent;
        _repository = repositoryFactory.Resolve<AssetCopyRequest>();

        _logger.LogInformation("IncomingAssetCopyMessageHandler completed initializing");
    }
    public Task ProcessError(Exception exception)
    {
        _logger.LogError(exception, "IncomingAssetCopyMessageHandler Queue Error: {ExceptionMessage}", exception.Message);
        return Task.CompletedTask;
    }

    private (string?, string?) GetContainerNames(IngestionFlowTypes? flowTypes, string direction)
    {
        return (flowTypes, direction) switch
        {
            (IngestionFlowTypes.NormalFlow, "migrate") => (_normalFlowSettings.CopyContainerName, _normalFlowSettings.ContainerName),
            (IngestionFlowTypes.NormalFlow, "backup") => (_normalFlowSettings.ContainerName, _normalFlowSettings.CopyContainerName),
            (IngestionFlowTypes.DirectFlow, "migrate") => (_fileTransformationSettings.CopyContainerName, _fileTransformationSettings.ContainerName),
            (IngestionFlowTypes.DirectFlow, "backup") => (_fileTransformationSettings.ContainerName, _fileTransformationSettings.CopyContainerName),
            (IngestionFlowTypes.LiveToVod, "migrate") => (_liveToVodSettings.CopyContainerName, _liveToVodSettings.ContainerName),
            (IngestionFlowTypes.LiveToVod, "backup") => (_liveToVodSettings.ContainerName, _liveToVodSettings.CopyContainerName),
            (_, _) => (null, null)
        };
    }

    public async Task ProcessMessage(ReceivedMessage message)
    {
        _logger.LogInformation($"IncomingAssetCopyMessageHandler ProcessMessage starting; message recieved: {message.Content}");

        if (string.IsNullOrWhiteSpace(message.Content))
        {
            _logger.LogError($"IncomingAssetCopyMessageHandler incoming message has null content; message should deadletter");
            message.ShouldDeadLetter = true;
            return;
        }

        var messageData = System.Text.Json.JsonSerializer.Deserialize<AssetCopyMessage>(message.Content);
        var bucketName = messageData?.BucketName;
        var flowType = messageData?.FlowType;
        var key = messageData?.Key;

        _logger.LogInformation($"IncomingAssetCopyMessageHandler message deserialized: {flowType} - ({bucketName}/{key})");

        if (flowType is null)
        {
            _logger.LogError("No flow type found on message: " + message.Content);
            message.ShouldDeadLetter = true;
            return;
        }

        if (key is null)
        {
            _logger.LogError("No key found on message: " + message.Content);
            message.ShouldDeadLetter = true;
            return;
        }

        _logger.LogInformation($"IncomingAssetCopyMessageHandler copy direction: {_settings.Direction}");

        var (source, destination) = GetContainerNames(flowType, _settings.Direction);
        if (source is null || destination is null)
        {
            _logger.LogError($"ERROR: IncomingAssetCopyMessageHandler copy direction not set or flow type on message is invalid; terminating ProcessMessage");
            message.ShouldDeadLetter = true;
            return;
        }

        var sourceRepository = _dataStoreFactory.Resolve(source);
        var destinationRepository = _dataStoreFactory.Resolve(destination);
        if (_settings.EnableConsumer && sourceRepository is not null && destinationRepository is not null && bucketName is not null)
        {
            var exists = await sourceRepository.ExistsAsync(key);

            if (exists)
            {
                var descriptor = await sourceRepository.GetDataDescriptorAsync(key);
                var request = new AssetCopyRequest()
                {
                    Id = Guid.NewGuid().ToString(),
                    BucketName = bucketName,
                    Destination = destination,
                    Source = source,
                    FlowType = flowType ?? IngestionFlowTypes.Unknown,
                    Key = key,
                    SizeInBytes = descriptor?.SizeInBytes ?? 0,
                    ArrivaleTimeUtc = DateTime.UtcNow
                };

                await _repository.CreateItemAsync(request);
                _signalEvent.Set();
                _logger.LogInformation($"IncomingAssetCopyMessageHandler Finished! Record Saved, and Signal Set!");
            }
            else
            {
                _logger.LogError($"Asset Not Found Source: {source}  Key: {key}");
                message.ShouldDeadLetter = true;
            }
        }
        else
        {
            _logger.LogError("ERROR: IncomingAssetCopyMessageHandler is either disabled, or blob clients were not initialized");
        }


    }
}
