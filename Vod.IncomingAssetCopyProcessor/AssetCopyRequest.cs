using System;
using NBA.NextGen.CMSPlatform.VODService.Domain.Common.Enums;
using Newtonsoft.Json;

namespace Vod.IncomingAssetCopyProcessor;

public class AssetCopyRequest
{
    [JsonProperty("id")]
    public string Id { get; set; } = "";
    public string Source { get; set; } = "";
    public string Destination { get; set; } = "";
    public string Key { get; set; } = "";
    public IngestionFlowTypes FlowType { get; set; }
    public string BucketName { get; set; } = "";
    public double SizeInBytes { get; set; } = 0;
    public DateTime ArrivaleTimeUtc { get; set; } = DateTime.MaxValue;
}
