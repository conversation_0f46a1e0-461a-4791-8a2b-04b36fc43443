using System.Diagnostics;
using MST.Common.Data;
using NBA.NextGen.CMSPlatform.VODService.Application.Services;
using Vod.Common.Extensions;

namespace Vod.IncomingAssetCopyProcessor;

public class AssetCopyService : BackgroundService
{
    private readonly ILogger _logger;
    private IDataStoreFactory _dataStoreFactory;
    private readonly IVodFileRegistryService _vodFileRegistryService;
    private readonly IQueryableRepository<AssetCopyRequest> _repository;
    private readonly AutoResetEvent _signalEvent;

    public AssetCopyService(ILogger<AssetCopyService> logger, IDataStoreFactory dataStoreFactory, IVodFileRegistryService vodFileRegistryService, IQueryableRepositoryFactory repositoryFactory, [FromKeyedServices("signal-event")] AutoResetEvent signalEvent)
    {
        _logger = logger;
        _dataStoreFactory = dataStoreFactory;
        _vodFileRegistryService = vodFileRegistryService;
        _repository = repositoryFactory.Resolve<AssetCopyRequest>();
        _signalEvent = signalEvent;
    }

    private async Task<List<AssetCopyRequest>> GetAssetCopyRequestsAsync(bool shouldRetry = true)
    {
        var results = await _repository.GetItemsAsync(_ => true);

        if (results is null || results.Count() == 0 && shouldRetry)
        {
            _logger.LogInformation($"No requests found, but > 0 expected, will recheck in 30 seconds!");
            await Task.Delay(TimeSpan.FromSeconds(30));
            return await GetAssetCopyRequestsAsync(false);
        }

        return results.ToList() ?? [];
    }

    private async Task CopyWorkerAsync(CancellationToken cancellationToken)
    {
        var sw = new Stopwatch();
        while (true)
        {
            _logger.LogInformation($"Waiting for Signal!");

            _signalEvent.WaitOne();
            if (cancellationToken.IsCancellationRequested)
            {
                _logger.LogWarning($"Cancellation Requested!!");
                return;
            }

            var requests = await GetAssetCopyRequestsAsync();
            var count = requests?.Count() ?? 0;
            _logger.LogInformation($"Found {count} requests to Copy!");
            foreach (var request in requests ?? [])
            {
                var sourceRepository = _dataStoreFactory.Resolve(request.Source);
                var destinationRepository = _dataStoreFactory.Resolve(request.Destination);
                var formattedSize = request.SizeInBytes.ToSizeString();
                _logger.LogInformation($"Requested Source: {request.Source}, Destination: {request.Destination}, Key: {request.Key}, Size: {formattedSize}!");

                var exists = await sourceRepository.ExistsAsync(request.Key);
                if (!exists)
                {
                    _logger.LogWarning($"Asset ({request.BucketName}/{request.Key}) Does not exist!");
                    await _repository.DeleteItemAsync(request.Id);
                    continue;
                }

                var sourceStream = await sourceRepository.GetItemStreamAsync(request.Key);
                if (sourceStream is not null)
                {
                    sw.Restart();
                    await destinationRepository.CreateItemAsync(request.Key, sourceStream, "application/json");
                    sw.Stop();
                    var formattedDuration = sw.Elapsed.ToExpandedFormatString();
                    _logger.LogInformation($"Asset ({request.BucketName}/{request.Key}) copied to destination, Duration: {formattedDuration}, Size: {formattedSize}!");
                    await _repository.DeleteItemAsync(request.Id);
                    await _vodFileRegistryService.LogFileCopyAsync(request.Key, request.FlowType, DateTime.UtcNow, request.Source, request.Destination, request.SizeInBytes, sw.Elapsed);
                }
            }
            _logger.LogInformation($"Finished Copying {count} requests!");
        }
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
         while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await CopyWorkerAsync(stoppingToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Copy Worker threw exception!");
            }
            _logger.LogInformation("Trying to recover from error state in 60 seconds...");
            await Task.Delay(TimeSpan.FromSeconds(60));
            // Set the signal so it'll start running again!
            _signalEvent.Set();
        }
    }
}
