<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.Extensions.Hosting" Version="8.0.0" />
        <PackageReference Include="AspNetCore.HealthChecks.AzureServiceBus" Version="8.0.1" />
        <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="8.0.8" />
        <PackageReference Include="Swashbuckle.AspNetCore" Version="6.4.0" />
        <PackageReference Include="AspNetCore.HealthChecks.UI" Version="8.0.2" />
        <PackageReference Include="AspNetCore.HealthChecks.UI.Client" Version="8.0.1" />
        <PackageReference Include="AspNetCore.HealthChecks.UI.InMemory.Storage" Version="8.0.1" />
        <PackageReference Include="AspNetCore.HealthChecks.Uris" Version="8.0.1" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\NBA.NextGen.CMSPlatform.VODService.Infrastructure\NBA.NextGen.CMSPlatform.VODService.Infrastructure.csproj" />
        <ProjectReference Include="..\Vod.Common\Vod.Common.csproj" />
    </ItemGroup>

    <ItemGroup>
        <Content Include="..\config\*.json">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </Content>
    </ItemGroup>
</Project>
