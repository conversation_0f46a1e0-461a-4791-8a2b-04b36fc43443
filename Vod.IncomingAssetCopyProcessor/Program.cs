using HealthChecks.UI.Client;
using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using Vod.IncomingAssetCopyProcessor.Extensions;
using Vod.Common.Extensions;
using Vod.Common.Health;
using Microsoft.Extensions.Diagnostics.HealthChecks;

Console.WriteLine("Incoming Asset Copy Processor starting up!");

var builder = WebApplication.CreateBuilder(args);
builder.Configuration
    .SetBasePath(AppDomain.CurrentDomain.BaseDirectory)
    .AddJsonFile("appsettings.json", false, true)
    .AddEnvironmentVariables();

var envName = builder.Configuration.GetValue<string>("ENV") ?? "qa";
builder.Configuration
    .AddAmazonSecretsManager(Environment.GetEnvironmentVariable("AWS_REGION") ?? "us-east-1",
            Environment.GetEnvironmentVariable("ENV") is not null ? $"/vod-middle-tier/{Environment.GetEnvironmentVariable("ENV")}" : "/vod-middle-tier/dev")
    .AddJsonFile($"{envName}.settings.json", false, true)
    .AddSharedConfiguration()
    .AddJsonFile("local.settings.json", true, true);

Console.WriteLine("Env: " + envName);

builder.Services.AddAuthorization();
builder.Services.AddControllers();
builder.Services.AddMvc();

builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

builder.Services
        .AddApplicationDependencies(builder.Configuration)
        .AddApplicationHandlers(builder.Configuration);

builder.Services.AddHealthChecks()
    .AddCheck<MemoryHealthCheck>("Memory Check", HealthStatus.Unhealthy, ["VOD Incoming Asset Copy Processor"])
    .AddCheck<AzureBlobHealthCheck>("Azure Blob Check", HealthStatus.Unhealthy, ["VOD Incoming Asset Copy Processor"]);

var app = builder.Build();

var logger = app.Services.GetService<ILogger<Program>>();

logger!.LogInformation("Getting Started!");

if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseHttpsRedirection();

app.UseAuthorization();
app.MapControllers();

app.MapHealthChecks("/healthz", new HealthCheckOptions
{
    Predicate = _ => true,
    ResponseWriter = UIResponseWriter.WriteHealthCheckUIResponse
});

app.MapHealthChecks("/readyz", new HealthCheckOptions
{
    Predicate = _ => true,
    ResponseWriter = UIResponseWriter.WriteHealthCheckUIResponse
});

app.Run();