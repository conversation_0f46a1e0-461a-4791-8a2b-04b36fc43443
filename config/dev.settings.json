{"AWS": {"Profile": "dev", "Region": "us-east-1"}, "CosmosSettings": {"DatabaseName": "ott-dvue2-vcms-cdb001"}, "MongoSettings": {"DatabaseName": "dev-vod-main"}, "KeyVault": {"ConnectionString": "https://ott-dvue2-vcms-kv001.vault.azure.net/"}, "SlackSettings": {"EnableOutput": true}, "CmsStatusNotificationsSettings": {"EnableEcmsNotification": "true", "EnableEventNotifier": "false", "Endpoint": "https://manage-dev.nba.com", "EventGridEventType": "VodService.Status", "EventGridTopic": "vvaibhavEventGridTopic", "IgnoreCertificateValidation": "true", "MediaKindEnvKey": "prodc"}, "QueueNames": {"IncomingBlob": "vcvodsv-development-incomingblob-cloudevents-queue", "SubmitToEcms": "vcvodsv-development-ecms-submit-queue", "SubmitToVcms": "vcvodsv-development-vcms-submit-queue", "UpdateEcms": "vcvodsv-development-ecms-update-queue", "AssetCopy": "vcvodsv-development-incomingblob-copy-queue"}, "AWSQueueNames": {"IncomingBlob": "https://sqs.us-east-1.amazonaws.com/************/vcvodsv-dev-incomingblob-cloudevents-queue.fifo", "SubmitToEcms": "https://sqs.us-east-1.amazonaws.com/************/vcvodsv-dev-ecms-submit-queue.fifo", "SubmitToVcms": "https://sqs.us-east-1.amazonaws.com/************/vcvodsv-dev-vcms-submit-queue.fifo", "UpdateEcms": "https://sqs.us-east-1.amazonaws.com/************/vcvodsv-dev-ecms-update-queue.fifo", "AssetCopy": "https://sqs.us-east-1.amazonaws.com/************/vcvodsv-dev-asset-copy-queue.fifo"}, "QueueSettings": {"MaximumBackOff": "30", "MaximumRetryCount": "3", "MinimumBackOff": "10", "Namespace": "ott-npue2-message-sbn001", "Hostname": "ott-npue2-message-sbn001.servicebus.windows.net", "TenantId": "e898ff4a-4b69-45ee-a3ae-1cd6f239feb2", "UseWebSockets": "true"}, "NormalFlowSettings": {"AccountName": "nbaqamediaingest01", "BlobClientProviderSupport": "true", "ContainerName": "mst-vod-dev-normalflow", "CaptionContainer": "mst-vod-dev-normalflow", "CopyContainerName": "qa-normal-flow", "CopyCaptionContainer": "qa-normal-flow", "EnableEventNotifier": "false", "EventGridEndpoint": "https://ott-qaue2-events-evg001.eastus2-1.eventgrid.azure.net/api/events", "EventGridTopic": "ott-qaue2-events-evg001", "LogLevel": "Debug", "MaxRetry": "2", "SleepSeconds": "180", "VodFeedbackContainer": "mst-vod-dev-livetovod", "VodFeedbackFolder": "VOD", "WordpressAPIDomain": "https://manage-dev.nba.com/wp-json", "WriteToLedger": "false", "VodLedgerEndpoint": "https://apphub-dev.nba.com/hub/vod/tracking"}, "VideoIndexerSettings": {"ResourceGroup": "ott-dvue2-vcvodsv-rg001", "SubscriptionId": "41b1ff69-2718-42e0-82cb-527b42efeb7b", "AccountName": "ott-dvue2-vcvodsv-vidindexer001", "EnableChecker": false}, "FileTransformationSettings": {"GmsDomain": "https://gms-qa.internal.nba.com", "GmsPortNumber": "DON'T USE", "GmsUser": "blobwatcher", "AccountName": "nbaqamediaingest01", "ContainerName": "mst-vod-dev-directflow", "CopyContainerName": "qa-direct-flow", "EnableMocking": "true", "VODServiceFunctionURL": "***********************************************/VODServiceFunction/VODServiceFunction", "WSCContainerPath": "wsc-condensed-json"}, "VcmsSettings": {"EnableEventNotifier": "false", "EventGridTopic": "ott-dvue2-events-evg001", "IngestContentUser": "fileupload", "ProviderId": "NBA", "SendWorkflowMsg": "true", "VodIngestionStartEventType": "VodService.VodIngestionStart", "Endpoint": "https://cms.nba.prodc.tv3ce.com:8443", "IgnoreCertificateValidation": "true"}, "GamSettings": {"ApplicationName": "VOD Service App", "NetworkCode": "3446059", "SecretsJsonPath": "/config-secrets/gam_test.json"}, "VcmsStorageSettings": {"ContainerName": "mst-vod-dev-directflow-generated-adi", "CopyContainerName": "qa-directflow-generated-adi", "WriteToBlobStorage": "true"}, "BlobSettings": {"AccountName": "nbaqamediaingest01", "DeltaBackOff": "3", "MaxAttempts": "3", "TenantId": "b40e2cee-d3d7-48a5-8c88-461433c0f520"}, "FreeWheelSettings": {"EnableCallback": "true", "FreeWheelRestEndpoint": "https://api.freewheel.tv/services/v4/transcode/callback", "FreeWheelAccessTokenEndpoint": "https://api.freewheel.tv/auth/token", "SecretsJsonPath": "/config-secrets/freewheel_secrets.json"}, "LiveToVodSettings": {"AccountName": "nbaqamediaingest01", "ContainerName": "mst-vod-dev-livetovod", "CopyContainerName": "qa-vcms-generated"}, "NotifierSettings": {"Topics": [{"Name": "ott-qaue2-events-evg001", "Endpoint": "https://ott-qaue2-events-evg001.eastus2-1.eventgrid.azure.net/api/events"}]}, "TranscriptionEngine": {"EnableMediaConvertCron": true, "EnableTranscribeCron": true, "MediaConvertRole": "arn:aws:iam::************:role/MediaConvert", "EnableBackFlow": false, "WorkingBucket": "mst-vod-dev-transcribe"}, "DelayedAssetMonitor": {"AlertThresholdMinutes": 30}, "AssetCopySettings": {"EnableProducer": false, "EnableConsumer": false, "Direction": "migrate"}, "AssetDelivery": {"UseAzure": false}}