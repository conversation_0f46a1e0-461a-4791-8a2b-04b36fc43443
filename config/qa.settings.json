{"AWS": {"Profile": "dev", "Region": "us-east-1"}, "CosmosSettings": {"DatabaseName": "ott-qaue2-vcms-cdb001"}, "MongoSettings": {"DatabaseName": "qa-vod-main"}, "SlackSettings": {"EnableOutput": true}, "CmsStatusNotificationsSettings": {"EnableEcmsNotification": "true", "EnableEventNotifier": "false", "Endpoint": "https://manage-qa.nba.com", "EventGridEventType": "VodService.Status", "EventGridTopic": "vvaibhavEventGridTopic", "IgnoreCertificateValidation": "true", "MediaKindEnvKey": "prodb"}, "QueueNames": {"IncomingBlob": "vcvodsv-quality_assurance-incomingblob-cloudevents-queue", "SubmitToEcms": "vcvodsv-quality_assurance-ecms-submit-queue", "SubmitToVcms": "vcvodsv-quality_assurance-vcms-submit-queue", "UpdateEcms": "vcvodsv-quality_assurance-ecms-update-queue", "AssetCopy": "vcvodsv-quality_assurance-incomingblob-copy-queue"}, "AWSQueueNames": {"IncomingBlob": "https://sqs.us-east-1.amazonaws.com/************/vcvodsv-qa-incomingblob-cloudevents-queue.fifo", "SubmitToEcms": "https://sqs.us-east-1.amazonaws.com/************/vcvodsv-qa-ecms-submit-queue.fifo", "SubmitToVcms": "https://sqs.us-east-1.amazonaws.com/************/vcvodsv-qa-vcms-submit-queue.fifo", "UpdateEcms": "https://sqs.us-east-1.amazonaws.com/************/vcvodsv-qa-ecms-update-queue.fifo", "AssetCopy": "https://sqs.us-east-1.amazonaws.com/************/vcvodsv-qa-asset-copy-queue.fifo"}, "QueueSettings": {"MaximumBackOff": "30", "MaximumRetryCount": "3", "MinimumBackOff": "10", "Namespace": "ott-npue2-message-sbn001", "Hostname": "ott-npue2-message-sbn001.servicebus.windows.net", "TenantId": "e898ff4a-4b69-45ee-a3ae-1cd6f239feb2", "UseWebSockets": "true"}, "NormalFlowSettings": {"AccountName": "nbaqamediaingest01", "BlobClientProviderSupport": "true", "ContainerName": "mst-vod-qa-normalflow", "CopyContainerName": "qa-normal-flow", "CaptionContainer": "mst-vod-qa-normalflow", "EnableEventNotifier": "true", "EventGridEndpoint": "https://ott-qaue2-events-evg001.eastus2-1.eventgrid.azure.net/api/events", "EventGridTopic": "ott-qaue2-events-evg001", "LogLevel": "Debug", "MaxRetry": "2", "SleepSeconds": "180", "VodFeedbackContainer": "mst-vod-qa-livetovod", "VodFeedbackFolder": "VOD", "WordpressAPIDomain": "https://manage-qa.nba.com/wp-json"}, "VideoIndexerSettings": {"ResourceGroup": "ott-qaue2-vcvodsv-rg001", "SubscriptionId": "a0e16212-0f55-4127-b93a-f3635a4be48c", "AccountName": "ott-qaue2-vcvodsv-vidindexer001", "EnableChecker": true}, "FileTransformationSettings": {"GmsDomain": "https://gms-qa.internal.nba.com", "GmsPortNumber": "DON'T USE", "GmsUser": "blobwatcher", "AccountName": "nbaqamediaingest01", "ContainerName": "mst-vod-qa-directflow", "CopyContainerName": "qa-direct-flow", "EnableMocking": "true", "VODServiceFunctionURL": "***********************************************/VODServiceFunction/VODServiceFunction", "WSCContainerPath": "wsc-condensed-json"}, "VcmsSettings": {"EnableEventNotifier": "false", "EventGridTopic": "ott-qaue2-events-evg001", "IngestContentUser": "fileupload", "ProviderId": "NBA", "SendWorkflowMsg": "true", "VodIngestionStartEventType": "VodService.VodIngestionStart", "Endpoint": "https://cms.aws.prodb.nba.tv3ce.com:8443", "AzureEndpoint": "https://************:8443", "IgnoreCertificateValidation": "true"}, "GamSettings": {"ApplicationName": "VOD Service App", "NetworkCode": "3446059", "SecretsJsonPath": "/config-secrets/gam_test.json"}, "VcmsStorageSettings": {"ContainerName": "mst-vod-qa-directflow-generated-adi", "CopyContainerName": "qa-directflow-generated-adi", "WriteToBlobStorage": "true"}, "BlobSettings": {"AccountName": "nbaqamediaingest01", "DeltaBackOff": "3", "MaxAttempts": "3", "TenantId": "b40e2cee-d3d7-48a5-8c88-461433c0f520"}, "FreeWheelSettings": {"EnableCallback": "true", "FreeWheelRestEndpoint": "https://api.freewheel.tv/services/v4/transcode/callback", "FreeWheelAccessTokenEndpoint": "https://api.freewheel.tv/auth/token", "SecretsJsonPath": "/config-secrets/freewheel_secrets.json"}, "LiveToVodSettings": {"AccountName": "nbaqamediaingest01", "ContainerName": "mst-vod-qa-livetovod", "CopyContainerName": "qa-vcms-generated"}, "NotifierSettings": {"Topics": [{"Name": "ott-qaue2-events-evg001", "Endpoint": "https://ott-qaue2-events-evg001.eastus2-1.eventgrid.azure.net/api/events"}]}, "TranscriptionEngine": {"EnableMediaConvertCron": true, "EnableTranscribeCron": true, "MediaConvertRole": "arn:aws:iam::************:role/MediaConvert", "EnableBackFlow": true, "WorkingBucket": "mst-vod-qa-transcribe"}, "DelayedAssetMonitor": {"AlertThresholdMinutes": 30}, "AssetCopySettings": {"EnableProducer": false, "EnableConsumer": true, "Direction": "migrate"}, "AssetDelivery": {"UseAzure": false, "BucketToContainer": {"mst-vod-qa-normalflow": "qa-normal-flow", "mst-vod-qa-directflow-generated-adi": "qa-directflow-generated-adi", "mst-vod-qa-livetovod": "qa-vcms-generated", "mst-vod-qa-directflow": "qa-direct-flow"}}}