// "//-----------------------------------------------------------------------".
// <copyright file="RegisterServices.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Infrastructure
{
    using System.Diagnostics.CodeAnalysis;
    using Amazon.MediaConvert;
    using Amazon.S3;
    using Amazon.TranscribeService;
    using Microsoft.Extensions.Configuration;
    using Microsoft.Extensions.DependencyInjection;
    using NBA.NextGen.CMSPlatform.VODService.Application.Dispatcher;
    using NBA.NextGen.CMSPlatform.VODService.Application.Mappers;
    using NBA.NextGen.CMSPlatform.VODService.Application.Services;
    using NBA.NextGen.CMSPlatform.VODService.Domain.Config;
    using NBA.NextGen.CMSPlatform.VODService.Infrastructure.Services;

    /// <summary>
    /// Class for Dependency Injection.
    /// </summary>
    [ExcludeFromCodeCoverage]
    public static class RegisterServices
    {
        /// <summary>
        /// Adds the infrastructure.
        /// </summary>
        /// <param name="services">The services.</param>
        /// <param name="configuration">The configuration.</param>
        /// <returns>
        /// An instance of IService Collection.
        /// </returns>
        public static IServiceCollection AddInfrastructure(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddHttpClient();
            services.AddOptions<VcmsSettings>()
                .Bind(configuration.GetSection(nameof(VcmsSettings)));
            services.AddOptions<CmsStatusNotificationsSettings>()
                .Bind(configuration.GetSection(nameof(CmsStatusNotificationsSettings)));
            services.AddOptions<NormalFlowSettings>()
                .Bind(configuration.GetSection(nameof(NormalFlowSettings)));
            services.AddOptions<LiveToVodSettings>()
                .Bind(configuration.GetSection(nameof(LiveToVodSettings)));
            services.AddOptions<FreeWheelSettings>()
                .Bind(configuration.GetSection(nameof(FreeWheelSettings)));
            services.AddOptions<GamSettings>()
                .Bind(configuration.GetSection(nameof(GamSettings)));
            services.AddOptions<QueueNames>()
                .Bind(configuration.GetSection(nameof(QueueNames)));
            services.AddOptions<VideoIndexerSettings>()
                .Bind(configuration.GetSection(nameof(VideoIndexerSettings)));
            services.AddOptions<AssetCopySettings>()
                .Bind(configuration.GetSection(nameof(AssetCopySettings)));

            services.AddAutoMapper((cfg) => cfg.AddProfile(new ServiceProfile()), typeof(ServiceProfile).Assembly);
            services.AddFileTransformation(configuration);
            services.AddScoped<IFreeWheelRestClientService, FreeWheelRestClientService>();
            services.AddScoped<IBlobSearchService, BlobSearchService>();
            services.AddScoped<NormalFlowDispatcher>();
            services.AddScoped<DirectFlowDispatcher>();
            services.AddScoped<LiveToVodDispatcher>();
            services.AddHttpClient<ISlackNotificationService, SlackNotificationService>();
            services.AddHttpClient<IAzureVideoIndexerService, AzureVideoIndexerService>();
            services.AddAWSService<IAmazonTranscribeService>();
            services.AddAWSService<IAmazonMediaConvert>();
            services.AddAWSService<IAmazonS3>();
            services.AddSingleton<IAWSTranscribeService, AWSTranscribeService>();
            services.AddSingleton<IAWSMediaConvertService, AWSMediaConvertService>();
            services.AddSingleton<IAWSTranscriptionOrchestrator, AWSTranscriptionOrchestrator>();
            return services;
        }

        /// <summary>
        /// Adds the File Transformation settings.
        /// </summary>
        /// <param name="services">The services.</param>
        /// <param name="configuration">The configuration.</param>
        /// <returns>services.</returns>
        public static IServiceCollection AddFileTransformation(this IServiceCollection services, [NotNull] IConfiguration configuration)
        {
            services.AddOptions<FileTransformationSettings>()
                    .Bind(configuration.GetSection(nameof(FileTransformationSettings)));
            services.AddOptions<VcmsStorageSettings>()
                    .Bind(configuration.GetSection(nameof(VcmsStorageSettings)));
            services.AddHttpClient<IFileTransformationBlobService, FileTransformationBlobService>();
            return services;
        }

        /// <summary>
        /// Adds the key vault.
        /// </summary>
        /// <param name="configurationBuilder">The configuration builder.</param>
        /// <param name="configuration">The configuration.</param>
        /// <returns>
        /// IConfigurationBuilder.
        /// </returns>
        public static IConfigurationBuilder AddKeyVaultSecrets(this IConfigurationBuilder configurationBuilder, [NotNull] IConfiguration configuration)
        {
            var endpoint = configuration["AzureSettings:KeyVaultEndpoint"];

            configurationBuilder.AddAzureKeyVault(endpoint);
            return configurationBuilder;
        }

        /// <summary>
        /// Adds the application configuration.
        /// </summary>
        /// <param name="configurationBuilder">The configuration builder.</param>
        /// <param name="configuration">The configuration.</param>
        /// <returns>
        /// Azure App Configuration Builder.
        /// </returns>
        public static IConfigurationBuilder AddAppConfigurationConfigs(this IConfigurationBuilder configurationBuilder, [NotNull] IConfiguration configuration)
        {
            // var endpoint = configuration["AzureSettings:AppConfigEndpoint"];
            // var defaultRefreshInterval = double.TryParse(configuration["AzureSettings:DefaultRefreshInterval"], out var result) ? result : 10;
            // endpoint.Required(nameof(endpoint));
            //
            // configurationBuilder.AddAzureAppConfiguration(options =>
            // {
            //     options.Connect(endpoint)
            //         .ConfigureRefresh(options =>
            //         {
            //             options.Register("Application:Settings:Sentinel", true).SetCacheExpiration(TimeSpan.FromSeconds(defaultRefreshInterval));
            //         });
            // });

            return configurationBuilder;
        }
    }
}
