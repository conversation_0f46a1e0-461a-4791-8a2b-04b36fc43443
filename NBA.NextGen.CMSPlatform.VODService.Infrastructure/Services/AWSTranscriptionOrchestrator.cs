using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using MST.Common.Data;
using NBA.NextGen.CMSPlatform.VODService.Application.Services;
using NBA.NextGen.CMSPlatform.VODService.Domain.Common;
using NBA.NextGen.CMSPlatform.VODService.Domain.Common.Enums;
using Newtonsoft.Json;

namespace NBA.NextGen.CMSPlatform.VODService.Infrastructure.Services;

public class AWSTranscriptionOrchestrator : IAWSTranscriptionOrchestrator
{
    private readonly ILogger _logger;
    private readonly IQueryableRepository<TranscribeJob> _transcriptionRepository;
    private readonly IQueryableRepository<MediaConvertJob> _mediaConvertRepository;
    private readonly IAWSMediaConvertService _mediaConvert;
    private readonly IAWSTranscribeService _transcribeService;
    private readonly IDataStoreFactory _dataStoreFactory;
    private readonly IConfiguration _configuration;
    private readonly string _workingBucketname;
    private readonly IVodAssetRegistryService _vodAssetRegistryService;

    public AWSTranscriptionOrchestrator(ILogger<AWSTranscriptionOrchestrator> logger, IDataStoreFactory dataStoreFactory, IQueryableRepositoryFactory repositoryFactory, IAWSMediaConvertService mediaConvert, IAWSTranscribeService transcribeService, IConfiguration configuration, IVodAssetRegistryService vodAssetRegistryService)
    {
        _logger = logger;
        _transcriptionRepository = repositoryFactory.Resolve<TranscribeJob>();
        _mediaConvertRepository = repositoryFactory.Resolve<MediaConvertJob>();
        _mediaConvert = mediaConvert;
        _transcribeService = transcribeService;
        _dataStoreFactory = dataStoreFactory;
        _configuration = configuration;
        _vodAssetRegistryService = vodAssetRegistryService;
        _workingBucketname = _configuration.GetSection("TranscriptionEngine")["WorkingBucket"];
    }

    public async Task<string> StartTranscriptionWorkflowAsync<T>(string bucketName, string assetId, string resourcePath, T payload, string triggerFile, string resourceId) where T : class
    {
        var json = JsonConvert.SerializeObject(payload);
        var job = new MediaConvertJob()
        {
            Id = Guid.NewGuid().ToString(),
            AssetId = assetId,
            CreatedOn = DateTime.UtcNow,
            ResourceId = resourceId,
            TriggerAsset = triggerFile,
            EncodedContent = System.Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes(json)),
            AssetUrl = resourcePath,
            LastCheckedAtUtc = DateTime.MinValue,
            BucketName = bucketName
        };

        var relativePath = GetRelativePathSafe(bucketName, resourcePath);
        var inputPath = GetS3Url(bucketName, relativePath);
        var ext = Path.GetExtension(relativePath);
        var outputPath = relativePath.Substring(0, relativePath.Length - ext.Length) + "-" + Guid.NewGuid().ToString("N").Substring(0, 6);
        var outputUrl = $"s3://{_workingBucketname}/{outputPath}";
        job.OutputUrl = outputUrl + ".mp4";

        job.JobId = await _mediaConvert.StripAudioTrackAsync(inputPath, outputUrl);
        await this._vodAssetRegistryService.AddAssetEventAsync(assetId, AssetEventTypes.TranscribingRequested, DateTime.UtcNow, $"MediaConvert Id: {job.JobId}").ConfigureAwait(false);

        await _mediaConvertRepository.CreateItemAsync(job);

        return job.JobId;
    }

    public async Task<bool> AdvanceToTranscribeAsync(string jobId)
    {
        var mcJobs = await _mediaConvertRepository.GetItemsAsync(x => x.JobId == jobId);
        if (mcJobs is null || mcJobs.Count() == 0)
        {
            throw new ArgumentException($"Could not find existing JobId: {jobId} for Media Convert");
        }

        var mcJob = mcJobs.FirstOrDefault();

        var dataStore = _dataStoreFactory.Resolve(_workingBucketname);
        var relativePath = GetRelativePath(_workingBucketname, mcJob.OutputUrl);
        var exists = await dataStore.ExistsAsync(relativePath);
        if (!exists)
        {
            _logger.LogError($"JobId: {mcJob.JobId} Returned Completed, but asset does not exist in container!");
            return false;
        }

        var transcribeJob = new TranscribeJob()
        {
            Id = Guid.NewGuid().ToString(),
            AssetId = mcJob.AssetId,
            CreatedOn = DateTime.UtcNow,
            EncodedContent = mcJob.EncodedContent,
            ResourceId = mcJob.ResourceId,
            TriggerAsset = mcJob.TriggerAsset,
            AssetUrl = mcJob.AssetUrl,
            LastCheckedAtUtc = DateTime.MinValue,
            BucketName = mcJob.BucketName
        };

        var jobName = Path.GetFileNameWithoutExtension(mcJob.OutputUrl);
        var path = Path.GetDirectoryName(GetRelativePath(_workingBucketname, mcJob.OutputUrl));

        if (!path.EndsWith("/"))
        {
            path += "/";
        }

        transcribeJob.JobId = await _transcribeService.StartTranscriptionAsync(_workingBucketname, path, jobName, mcJob.OutputUrl);
        await _transcriptionRepository.CreateItemAsync(transcribeJob);
        await _mediaConvertRepository.DeleteItemAsync(mcJob.Id);

        await this._vodAssetRegistryService.AddAssetEventAsync(transcribeJob.AssetId, AssetEventTypes.TranscribingAdvanceToTranscribe, DateTime.UtcNow, $"Transcribe Id: {transcribeJob.JobId}").ConfigureAwait(false);

        return true;
    }
    public async Task CleanUpAsync(string jobId)
    {
        await _transcriptionRepository.DeleteItemAsync(jobId);
    }

    public async Task<List<string>> RearrangeCaptionsAsync(string jobId, string srtFile, string vttFile)
    {
        var transcribeJobs = await _transcriptionRepository.GetItemsAsync(t => t.JobId == jobId);

        if (transcribeJobs is null || transcribeJobs.Count() == 0)
        {
            throw new ArgumentException($"Could not find existing JobId: {jobId} for AWS Transcribe");
        }

        var transcribeJob = transcribeJobs.FirstOrDefault();

        var ext = Path.GetExtension(transcribeJob.AssetUrl);
        var originalFile = transcribeJob.AssetUrl.Substring(0, transcribeJob.AssetUrl.Length - ext.Length);
        var originalSrt = originalFile + ".srt";
        var originalVtt = originalFile + ".vtt";

        // These are relative paths to the SRT / VTT WITHOUT the random guids at the end.
        var relativeSrt = GetRelativePathSafe(transcribeJob.BucketName, originalSrt);
        var relativeVtt = GetRelativePathSafe(transcribeJob.BucketName, originalVtt);

        // These are relative paths to the SRT / VTT WITH the random guid at the end.
        var workingSrt = GetRelativePath(_workingBucketname, srtFile);
        var workingVtt = GetRelativePath(_workingBucketname, vttFile);

        var workingDataStore = _dataStoreFactory.Resolve(_workingBucketname);
        var dataStore = _dataStoreFactory.Resolve(transcribeJob.BucketName);

        var srtExists = await dataStore.ExistsAsync(relativeSrt);
        var vttExists = await dataStore.ExistsAsync(relativeVtt);

        if (srtExists)
        {
            await dataStore.DeleteItemAsync(relativeSrt);
        }

        if (vttExists)
        {
            await dataStore.DeleteItemAsync(relativeVtt);
        }

        var srtContent = await workingDataStore.GetItemAsync(workingSrt);
        var vttContent = await workingDataStore.GetItemAsync(workingVtt);

        await dataStore.CreateItemAsync(relativeSrt, srtContent);
        await dataStore.CreateItemAsync(relativeVtt, vttContent);

        bool.TryParse(_configuration.GetSection("TranscriptionEngine")["EnableBackFlow"], out var enableBackflow);

        // Warning this assumes captions will _ALWAYS_ backflow to normal-flow container!!!! 
        var backflowContainerName = _configuration.GetSection("NormalFlowSettings")["CopyContainerName"];
        if (enableBackflow && !string.IsNullOrWhiteSpace(backflowContainerName))
        {
            var backflowRepository = _dataStoreFactory.Resolve(backflowContainerName);
            await backflowRepository.CreateItemAsync(relativeSrt, srtContent);
            _logger.LogInformation($"Backflowing SRT Caption {relativeSrt} to {backflowContainerName}");
            await backflowRepository.CreateItemAsync(relativeVtt, vttContent);
            _logger.LogInformation($"Backflowing VTT Caption {relativeVtt} to {backflowContainerName}");
            await _vodAssetRegistryService.AddAssetEventAsync(transcribeJob.AssetId, AssetEventTypes.TranscribingBackflowCompleted, DateTime.UtcNow, $"{relativeSrt},{relativeVtt} Delivered To: {backflowContainerName} ").ConfigureAwait(false);
        }

        await workingDataStore.DeleteItemAsync(workingSrt);
        await workingDataStore.DeleteItemAsync(workingVtt);

        await _vodAssetRegistryService.AddAssetEventAsync(transcribeJob.AssetId, AssetEventTypes.TranscribingCompleted, DateTime.UtcNow, $"{originalSrt},{originalVtt}").ConfigureAwait(false);
        return new List<string>() { originalSrt, originalVtt };
    }

    public string GetRelativePath(string bucketName, string url)
    {
        var bucketLocation = url.IndexOf(bucketName);
        if (bucketLocation == -1)
        {
            return url;
        }
        var slashLocation = url.IndexOf("/", bucketLocation);
        return url.Substring(slashLocation + 1);
    }

    private string GetS3Url(string bucketName, string key)
    {
        if (key.StartsWith("/"))
        {
            return $"s3://{bucketName}{key}";
        }
        return $"s3://{bucketName}/{key}";
    }

    public string GetRelativePathSafe(string bucketName, string url)
    {
        bool.TryParse(this._configuration.GetSection("AssetDelivery")["UseAzure"], out var useAzure);
        if (useAzure)
        {
            bucketName = this._configuration.GetSection("AssetDelivery").GetSection("BucketToContainer")[bucketName];
        }

        var bucketLocation = url.IndexOf(bucketName);
        if (bucketLocation == -1)
        {
            return url;
        }
        var slashLocation = url.IndexOf("/", bucketLocation);
        return url.Substring(slashLocation + 1);
    }
}
