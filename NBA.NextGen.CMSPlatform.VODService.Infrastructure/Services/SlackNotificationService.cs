using System;
using System.Diagnostics.CodeAnalysis;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using NBA.NextGen.CMSPlatform.VODService.Application.Services;
using NBA.NextGen.CMSPlatform.VODService.Domain.Config;
using Newtonsoft.Json;

namespace NBA.NextGen.CMSPlatform.VODService.Infrastructure.Services;

public class SlackNotificationService : ISlackNotificationService
{
    private readonly HttpClient httpClient;
    private readonly ILogger logger;

    private readonly string _webhookUrl;
    private readonly string _errorWebhookUrl; 

    public SlackNotificationService(HttpClient httpClient, IConfiguration configuration, ILogger<SlackNotificationService> logger)
    {
        this.httpClient = httpClient;
        _webhookUrl = configuration["slack_webhook_url"];
        _errorWebhookUrl = configuration["slack_error_webhook_url"];
        this.logger = logger;
    }

    public Task WriteMessageAsync(string message)
    {
        return this.InnerWriteMessageAsync(_webhookUrl, message);
    }

    public Task WriteErrorMessageAsync(string message)
    {
        return this.InnerWriteMessageAsync(_errorWebhookUrl, message);
    }

    public Task WriteJsonAsync(string json)
    {
        return this.InnerWriteJsonAsync(_webhookUrl, json);
    }

    public Task WriteErrorJsonAsync(string json)
    {
        return this.InnerWriteJsonAsync(_errorWebhookUrl, json);
    }

    private async Task InnerWriteJsonAsync(string uri, string json)
    {
        if (string.IsNullOrWhiteSpace(uri))
        {
            return;
        }

        try
        {
            using var content = new StringContent(json, Encoding.UTF8, "application/json");
            await this.httpClient.PostAsync(uri, content).ConfigureAwait(false);
        }
        catch (Exception e)
        {
            this.logger.LogError(e, "Could not post to Slack");
        }
    }

    private async Task InnerWriteMessageAsync(string uri, string message)
    {
        if (string.IsNullOrWhiteSpace(uri))
        {
            return;
        }

        try
        {
            var payload = new SlackMessage() { Text = message };
            var json = JsonConvert.SerializeObject(payload);
            using var content = new StringContent(json, Encoding.UTF8, "application/json");
            await this.httpClient.PostAsync(uri, content).ConfigureAwait(false);
        }
        catch (Exception e)
        {
            this.logger.LogError(e, "Could not post to Slack");
        }
    }

    private class SlackMessage
    {
        [JsonProperty("text")]
        public string Text { get; set; }
    }
}
