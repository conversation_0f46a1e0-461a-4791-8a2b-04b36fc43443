using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Amazon.TranscribeService;
using Amazon.TranscribeService.Model;
using Microsoft.Extensions.Logging;
using NBA.NextGen.CMSPlatform.VODService.Application.Services;

namespace NBA.NextGen.CMSPlatform.VODService.Infrastructure.Services
{
    public class AWSTranscribeService : IAWSTranscribeService
    {
        private readonly IAmazonTranscribeService _transcribeService;
        private readonly ILogger _logger;
        
        public AWSTranscribeService(ILogger<AWSTranscribeService> logger, IAmazonTranscribeService transcribeService)
        {
            _logger = logger;
            _transcribeService = transcribeService;

        }

        public async Task<string> StartTranscriptionAsync(string outputBucketName, string relativeFolders, string jobName, string inputUrl)
        {
            var response = await _transcribeService.StartTranscriptionJobAsync(
                new StartTranscriptionJobRequest()
                {
                    OutputBucketName = outputBucketName,
                    OutputKey = relativeFolders,
                    TranscriptionJobName = jobName,
                    Media = new Media()
                    {
                        MediaFileUri = inputUrl
                    },
                    MediaFormat = MediaFormat.Mp4,
                    LanguageCode = LanguageCode.EnUS,
                    Settings = null,
                    Subtitles = new Subtitles()
                    {
                        Formats = new List<string>() { "vtt", "srt" },
                    }
                });

            return response.TranscriptionJob.TranscriptionJobName;
        }

        public async Task<GetTranscriptionJobResponse> GetTranscriptionJobByIdAsync(string jobName)
        {
            try
            {
                jobName = $"{jobName}";
                var response = await _transcribeService.GetTranscriptionJobAsync(new GetTranscriptionJobRequest
                {
                    TranscriptionJobName = jobName
                });

                _logger.LogInformation("Found job for {jobName}: {response}", jobName, response);

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogInformation("Failed to get transcription job for '{jobName}': {message}", jobName, ex.Message);
                throw new Exception($"Failed to get transcription job for '{jobName}': {ex.Message}", ex);
            }
        }
    }
}