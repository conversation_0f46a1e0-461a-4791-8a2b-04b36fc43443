// "//-----------------------------------------------------------------------".
// <copyright file="AzureVideoIndexerHelper.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Infrastructure.Services
{
    using System;
    using System.Net.Http;
    using System.Net.Http.Headers;
    using System.Text;
    using System.Text.Json.Serialization;
    using System.Threading.Tasks;
    using Azure.Core;
    using Azure.Identity;
    using Microsoft.Extensions.Logging;
    using Microsoft.Extensions.Options;
    using NBA.NextGen.CMSPlatform.VODService.Domain.Config;
    using NBA.NextGen.CMSPlatform.VODService.Infrastructure.Services.VideoIndexer;

    /// <summary>
    /// AzureVideoIndexerHelper.
    /// </summary>
    public class AzureVideoIndexerHelper
    {
        /// <summary>
        /// AzureResourceManager.
        /// </summary>
        private const string AzureResourceManager = "https://management.azure.com";

        /// <summary>
        /// ApiVersion.
        /// </summary>
        private const string ApiVersion = "2022-08-01";

        /// <summary>
        /// Settings.
        /// </summary>
        private readonly VideoIndexerSettings settings;

        /// <summary>
        /// AccountName.
        /// </summary>
        private readonly string armAccessToken;

        /// <summary>
        /// Initializes a new instance of the <see cref="AzureVideoIndexerHelper"/> class.
        /// </summary>
        /// <param name="armAccessToken">Access token.</param>
        /// <param name="settings">The settings.</param>
        protected AzureVideoIndexerHelper(string armAccessToken, VideoIndexerSettings settings)
        {
            this.armAccessToken = armAccessToken;
            this.settings = settings;
        }

        /// <summary>
        /// Builds the Video Indexer Resource Provider Client with the proper token for authorization.
        /// </summary>
        /// <param name="settings">The settings.</param>
        /// <returns>AzureVideoIndexerHelper.</returns>
        public static async Task<AzureVideoIndexerHelper> GetAzureVideoIndexerHelperAsync(VideoIndexerSettings settings)
        {
            var tokenRequestContext = new TokenRequestContext(new[] { $"{AzureResourceManager}/.default" });
            var tokenRequestResult = await new DefaultAzureCredential(new DefaultAzureCredentialOptions()).GetTokenAsync(tokenRequestContext).ConfigureAwait(false);

            return new AzureVideoIndexerHelper(tokenRequestResult.Token, settings);
        }

        /// <summary>
        /// Generates an access token. Calls the generateAccessToken API  (https://github.com/Azure/azure-rest-api-specs/blob/main/specification/vi/resource-manager/Microsoft.VideoIndexer/stable/2022-08-01/vi.json#:~:text=%22/subscriptions/%7BsubscriptionId%7D/resourceGroups/%7BresourceGroupName%7D/providers/Microsoft.VideoIndexer/accounts/%7BaccountName%7D/generateAccessToken%22%3A%20%7B).
        /// </summary>
        /// <param name="client">Http Client.</param>
        /// <param name="permission"> The permission for the access token.</param>
        /// <param name="scope"> The scope of the access token.</param>
        /// <param name="log">The logger.</param>
        /// <returns> The access token, otherwise throws an exception.</returns>
        public async Task<string> GetAccessTokenAsync(HttpClient client, ArmAccessTokenPermission permission, ArmAccessTokenScope scope, ILogger log)
        {
            var accessTokenRequest = new AccessTokenRequest
            {
                PermissionType = permission,
                Scope = scope,
            };

            log.LogInformation("AzureVideoIndexerHelper - Getting access token...");

            // Set the generateAccessToken (from video indexer) http request content
            try
            {
                var jsonRequestBody = System.Text.Json.JsonSerializer.Serialize(accessTokenRequest);
                using var httpContent = new StringContent(jsonRequestBody, Encoding.UTF8, "application/json");

                // Set request uri
                var requestUri = $"{AzureResourceManager}/subscriptions/{this.settings.SubscriptionId}/resourcegroups/{this.settings.ResourceGroup}/providers/Microsoft.VideoIndexer/accounts/{this.settings.AccountName}/generateAccessToken?api-version={ApiVersion}";
                client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", this.armAccessToken);

                var result = await client.PostAsync(requestUri, httpContent).ConfigureAwait(false);

                if (result.StatusCode != System.Net.HttpStatusCode.OK)
                {
                    var message = await result.Content.ReadAsStringAsync().ConfigureAwait(false);
                    log.LogError("AzureVideoIndexerHelper - Failed to get access token! Error: {Message}", message);
                    throw new InvalidOperationException("Could not get access token!");
                }

                var jsonResponseBody = await result.Content.ReadAsStringAsync().ConfigureAwait(false);

                log.LogInformation("AzureVideoIndexerHelper - Acquired access token");

                return System.Text.Json.JsonSerializer.Deserialize<GenerateAccessTokenResponse>(jsonResponseBody).AccessToken;
            }
            catch (Exception ex)
            {
                log.LogInformation("AzureVideoIndexerHelper - Exception: {Exception}", ex.ToString());
                throw;
            }
        }

        /// <summary>
        /// Gets an account. Calls the getAccount API (https://github.com/Azure/azure-rest-api-specs/blob/main/specification/vi/resource-manager/Microsoft.VideoIndexer/stable/2022-08-01/vi.json#:~:text=%22/subscriptions/%7BsubscriptionId%7D/resourceGroups/%7BresourceGroupName%7D/providers/Microsoft.VideoIndexer/accounts/%7BaccountName%7D%22%3A%20%7B).
        /// </summary>
        /// <param name="client">Http Client.</param>
        /// <param name="log">Logger.</param>
        /// <returns>The Account, otherwise throws an exception.</returns>
        public async Task<Account> GetAccountAsync(HttpClient client, ILogger log)
        {
            log.LogInformation("Getting account {AccountName}", this.settings.AccountName);

            Account account;

            try
            {
                // Set request uri
                var requestUri = $"{AzureResourceManager}/subscriptions/{this.settings.SubscriptionId}/resourcegroups/{this.settings.ResourceGroup}/providers/Microsoft.VideoIndexer/accounts/{this.settings.AccountName}?api-version={ApiVersion}";
                log.LogInformation("Requesting Video Indexer Account Name: {AccountName}", this.settings.AccountName);

                client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", this.armAccessToken);

                var result = await client.GetAsync(requestUri).ConfigureAwait(false);

                if (result.StatusCode != System.Net.HttpStatusCode.OK)
                {
                    var message = await result.Content.ReadAsStringAsync().ConfigureAwait(false);
                    log.LogError("AzureVideoIndexerHelper - Failed to get account! Error: {Message}", message);
                    throw new InvalidOperationException("Could not get account!");
                }

                var jsonResponseBody = await result.Content.ReadAsStringAsync().ConfigureAwait(false);
                account = System.Text.Json.JsonSerializer.Deserialize<Account>(jsonResponseBody);

                this.VerifyValidAccount(account, log);

                log.LogInformation("The account ID is {PropertiesId}", account.Properties.Id);
                log.LogInformation("The account location is {AccountLocation}", account.Location);

                return account;
            }
            catch (Exception ex)
            {
                log.LogInformation("AzureVideoIndexerHelper - Exception: {Exception}", ex.ToString());
                throw;
            }
        }

        /// <summary>
        /// Verifies the account.
        /// </summary>
        /// <param name="account">The account.</param>
        /// <param name="log">The logger.</param>
        private void VerifyValidAccount(Account account, ILogger log)
        {
            if (string.IsNullOrWhiteSpace(account.Location) || account.Properties == null || string.IsNullOrWhiteSpace(account.Properties.Id))
            {
                log.LogInformation("{AccountName} not found. Check SubscriptionId, ResourceGroup, AccountName", this.settings.AccountName);

#pragma warning disable S112
                throw new Exception($"Account {this.settings.AccountName} not found.");
#pragma warning restore S112
            }
        }
    }
}