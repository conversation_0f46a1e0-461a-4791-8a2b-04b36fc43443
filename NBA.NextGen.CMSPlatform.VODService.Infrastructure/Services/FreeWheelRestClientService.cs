namespace NBA.NextGen.CMSPlatform.VODService.Infrastructure.Services;

using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using NBA.NextGen.CMSPlatform.VODService.Application.Services;
using NBA.NextGen.CMSPlatform.VODService.Domain.Config;
using NBA.NextGen.CMSPlatform.VODService.Domain.FreeWheel.Response;
using NBA.NextGen.CMSPlatform.VODService.Domain.Utilities;
using NBA.NextGen.CMSPlatform.VODService.Infrastructure.Services.FreeWheel;

public class FreeWheelRestClientService : IFreeWheelRestClientService
{
    private readonly FreeWheelSettings settings;
    private readonly HttpClient httpClient;
    private readonly IVodAssetRegistryService vodAssetRegistryService;

    public FreeWheelRestClientService(
        [NotNull] IOptions<FreeWheelSettings> settings,
        HttpClient httpClient,
        IVodAssetRegistryService vodAssetRegistryService
    )
    {
        this.settings = settings.Value;
        this.httpClient = httpClient;
        this.vodAssetRegistryService = vodAssetRegistryService;
    }

    public async Task<string> GetAccessTokenAsync(
        Transcode response,
        HttpClient client,
        ILogger log
    )
    {
        log.LogInformation("FreeWheelRestClientService - Getting access token...");

        var jsonString = File.ReadAllText(this.settings.SecretsJsonPath);

        var parsedSecrets = System.Text.Json.JsonSerializer.Deserialize<List<FreeWheelSecrets>>(
            jsonString
        );

        FreeWheelSecrets matchedSecrets = parsedSecrets.FirstOrDefault(s =>
            s.NetworkId == response.Creative.NetworkId
        );

        if (matchedSecrets == null)
        {
#pragma warning disable S112
            throw new Exception("No FreeWheel Credentials Matched to NetworkId");
#pragma warning disable S112
        }

        var values = new List<KeyValuePair<string, string>>();
        values.Add(new KeyValuePair<string, string>("grant_type", "password"));
        values.Add(new KeyValuePair<string, string>("username", matchedSecrets.Username));
        values.Add(new KeyValuePair<string, string>("password", matchedSecrets.Password));
        using var content = new FormUrlEncodedContent(values);

        // Set the generateAccessToken (from freewheel) http request content
        try
        {
            // Set request uri
            var requestUri = this.settings.FreeWheelAccessTokenEndpoint;
            content.Headers.ContentType = MediaTypeHeaderValue.Parse(
                "application/x-www-form-urlencoded"
            );

            var result = await client.PostAsync(requestUri, content).ConfigureAwait(false);

            if (result.StatusCode != System.Net.HttpStatusCode.OK)
            {
                var message = await result.Content.ReadAsStringAsync().ConfigureAwait(false);
                log.LogError(
                    "FreeWheelRestClientService - Failed to get access token! Error: {Message}",
                    message
                );
                throw new InvalidOperationException("Could not get access token!");
            }

            var jsonResponseBody = await result.Content.ReadAsStringAsync().ConfigureAwait(false);

            log.LogInformation("FreeWheelRestClientService - Acquired access token");

            return System
                .Text.Json.JsonSerializer.Deserialize<GenerateAccessTokenResponse>(jsonResponseBody)
                .AccessToken;
        }
        catch (Exception ex)
        {
            log.LogInformation(
                "FreeWheelRestClientService - Exception: {Exception}",
                ex.ToString()
            );
            throw;
        }
    }

    public async Task<bool> SendResponseToFreeWheelAsync(
        Transcode response,
        ILogger logger,
        CancellationToken cancellationToken
    )
    {
        var body = XmlHelper.SerializeXML(response, Encoding.UTF8);

        logger.LogInformation($"Freewheel Post Body: {body}");

        if (this.settings.EnableCallback)
        {
            logger.LogInformation("Making Callback to Freewheel.");
            HttpContent httpContent = new StringContent(body);
            httpContent.Headers.ContentType = MediaTypeHeaderValue.Parse("application/xml");
            var assetId =
                $"{response.Creative.NetworkId}.{response.Creative.SourceCreativeRendition.Id}";
            using (
                var req = new HttpRequestMessage(
                    HttpMethod.Post,
                    this.settings.FreeWheelRestEndpoint
                )
            )
            {
                req.Content = httpContent;
                var accessToken = await this.GetAccessTokenAsync(response, this.httpClient, logger)
                    .ConfigureAwait(false);
                this.httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue(
                    "Bearer",
                    accessToken
                );
                var result = await this
                    .httpClient.PostAsync(
                        this.settings.FreeWheelRestEndpoint,
                        httpContent,
                        cancellationToken
                    )
                    .ConfigureAwait(false);
                logger.LogInformation($"Freewheel API Response code {result.StatusCode}");

                if (result.StatusCode != System.Net.HttpStatusCode.OK)
                {
                    var message = await result.Content.ReadAsStringAsync().ConfigureAwait(false);
                    await this.vodAssetRegistryService.AddAssetEventAsync(
                        assetId,
                        Domain.Common.Enums.AssetEventTypes.FreeWheelResponseFailedToSend,
                        DateTime.UtcNow,
                        message
                    );
                    logger.LogError(
                        "FreeWheelRestClientService - Failed to Call API! Error: {Message}",
                        message
                    );
                    throw new InvalidOperationException("Could not call API!");
                }

                await this.vodAssetRegistryService.AddAssetEventAsync(
                    assetId,
                    Domain.Common.Enums.AssetEventTypes.FreeWheelResponseSentSuccessfully,
                    DateTime.UtcNow
                );
                return result.IsSuccessStatusCode;
            }
        }
        else
        {
            logger.LogInformation("Callback to freewheel disabled.");
            return true;
        }
    }
}
