// "//-----------------------------------------------------------------------".
// <copyright file="AzureVideoIndexerService.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Infrastructure.Services
{
    using System;
    using System.Collections.Generic;
    using System.IO;
    using System.Linq;
    using System.Net.Http;
    using System.Net.Http.Headers;
    using System.Threading.Tasks;
    using System.Web;
    using Azure.Storage.Blobs;
    using Azure.Storage.Sas;
    using Microsoft.Extensions.Logging;
    using Microsoft.Extensions.Options;
    using MST.Common.Data;
    using NBA.NextGen.CMSPlatform.VODService.Application.Services;
    using NBA.NextGen.CMSPlatform.VODService.Domain.Common;
    using NBA.NextGen.CMSPlatform.VODService.Domain.Config;
    using NBA.NextGen.CMSPlatform.VODService.Domain.Utilities;
    using NBA.NextGen.CMSPlatform.VODService.Domain.VideoIndexer;
    using NBA.NextGen.CMSPlatform.VODService.Infrastructure.Services.VideoIndexer;
    using Newtonsoft.Json;

    /// <summary>
    /// AzureVideoIndexerService.
    /// </summary>
    public class AzureVideoIndexerService : IAzureVideoIndexerService
    {
        /// <summary>
        /// ApiUrl.
        /// </summary>
#pragma warning disable S1075
        private const string ApiUrl = "https://api.videoindexer.ai";
#pragma warning restore S1075

        /// <summary>
        /// Gets or sets Logger.
        /// </summary>
        private readonly ILogger logger;

        /// <summary>
        /// Settings.
        /// </summary>
        private readonly NormalFlowSettings settings;

        /// <summary>
        /// Video Indexer Settings.
        /// </summary>
        private readonly VideoIndexerSettings indexerSettings;

        /// <summary>
        /// The http client.
        /// </summary>
        private readonly HttpClient httpClient;

        private readonly IObjectRepositoryFactory objectRepositoryFactory;

        /// <summary>
        /// Initializes a new instance of the <see cref="AzureVideoIndexerService"/> class.
        /// </summary>
        public AzureVideoIndexerService(ILogger<AzureVideoIndexerService> logger, IOptions<NormalFlowSettings> settings, IOptions<VideoIndexerSettings> indexerSettings, HttpClient httpClient, IObjectRepositoryFactory objectRepositoryFactory)
        {
            this.logger = logger;
            this.settings = settings.Value;
            this.indexerSettings = indexerSettings.Value;
            this.httpClient = httpClient;
            this.objectRepositoryFactory = objectRepositoryFactory;
        }

        /// <inheritdoc />
        public async Task<string> SubmitJobAsync<T>(string containerName, string resourcePath, string correlationKey, T payload, string trigger, string resourceId) where T : class
        {
            var sasUrl = this.GetSasUrl(containerName, resourcePath);

            // Build Azure Video Indexer resource provider client that has access token throuhg ARM
            var videoIndexerHelper = await AzureVideoIndexerHelper.GetAzureVideoIndexerHelperAsync(this.indexerSettings).ConfigureAwait(false);

            // Get account details
            var account = await videoIndexerHelper.GetAccountAsync(this.httpClient, this.logger).ConfigureAwait(false);
            var accountLocation = account.Location;
            var accountId = account.Properties.Id;

            // Get account level access token for Azure Video Indexer.
            var accountAccessToken = await videoIndexerHelper.GetAccessTokenAsync(this.httpClient, ArmAccessTokenPermission.Contributor, ArmAccessTokenScope.Account, this.logger).ConfigureAwait(false);

            var json =  JsonConvert.SerializeObject(payload);
            await SavePayloadToCacheAsync(correlationKey, json,  trigger, resourceId);

            // Upload the video
            var videoId = await this.UploadVideoAsync(resourcePath, correlationKey, sasUrl, accountLocation, accountId, accountAccessToken).ConfigureAwait(false);
            return videoId;
        }

        /// <inheritdoc />
        public async Task<List<VideoIndexerEntry>> ListExistingVideosAsync()
        {
            // Build Azure Video Indexer resource provider client that has access token through ARM
            var videoIndexerHelper = await AzureVideoIndexerHelper.GetAzureVideoIndexerHelperAsync(this.indexerSettings).ConfigureAwait(false);

            // Get account details
            var account = await videoIndexerHelper.GetAccountAsync(this.httpClient, this.logger).ConfigureAwait(false);
            var accountLocation = account.Location;
            var accountId = account.Properties.Id;

            // Get account level access token for Azure Video Indexer.
            var accountAccessToken = await videoIndexerHelper.GetAccessTokenAsync(this.httpClient, ArmAccessTokenPermission.Contributor, ArmAccessTokenScope.Account, this.logger).ConfigureAwait(false);

            var url = $"{ApiUrl}/{accountLocation}/Accounts/{accountId}/Videos";

            this.httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", accountAccessToken);
            var externalIdResult = await this.httpClient.GetAsync(url).ConfigureAwait(false);

            if (externalIdResult.StatusCode != System.Net.HttpStatusCode.OK)
            {
                var message = await externalIdResult.Content.ReadAsStringAsync().ConfigureAwait(false);
                this.logger.LogError("AzureVideoIndexerService - Could not get video list. Error: {Message}", message);
                throw new InvalidOperationException("Could not get Video List!");
            }

            var json = await externalIdResult.Content.ReadAsStringAsync().ConfigureAwait(false);

            var videolist = System.Text.Json.JsonSerializer.Deserialize<ListIndexerVideosResponse>(json);

            if (videolist?.Results is null)
            {
                return new List<VideoIndexerEntry>();
            }

            var videos = videolist.Results.Select(v =>
                new VideoIndexerEntry()
                {
                    Id = v.Id,
                    Name = v.Name,
                    ExternalId = v.ExternalId,
                    State = this.MapState(v.State),
                }).ToList();

            return videos;
        }

        /// <inheritdoc />
        public async Task<string> GetExternalIdAsync(string videoId)
        {
            // Build Azure Video Indexer resource provider client that has access token through ARM
            var videoIndexerHelper = await AzureVideoIndexerHelper.GetAzureVideoIndexerHelperAsync(this.indexerSettings).ConfigureAwait(false);

            // Get account details
            var account = await videoIndexerHelper.GetAccountAsync(this.httpClient, this.logger).ConfigureAwait(false);
            var accountLocation = account.Location;
            var accountId = account.Properties.Id;

            // Get account level access token for Azure Video Indexer.
            var accountAccessToken = await videoIndexerHelper.GetAccessTokenAsync(this.httpClient, ArmAccessTokenPermission.Contributor, ArmAccessTokenScope.Account, this.logger).ConfigureAwait(false);

            var url = $"{ApiUrl}/{accountLocation}/Accounts/{accountId}/Videos/{videoId}/Index?excludedInsights=Transcript";

            this.httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", accountAccessToken);
            var externalIdResult = await this.httpClient.GetAsync(url).ConfigureAwait(false);

            if (externalIdResult.StatusCode != System.Net.HttpStatusCode.OK)
            {
                var message = await externalIdResult.Content.ReadAsStringAsync().ConfigureAwait(false);
                this.logger.LogError("AzureVideoIndexerService - Could not Get external Id. Error: {Message}", message);
                throw new InvalidOperationException("Could not get external id!");
            }

            var json = await externalIdResult.Content.ReadAsStringAsync().ConfigureAwait(false);

            var videoSummary = System.Text.Json.JsonSerializer.Deserialize<VideoIndexerSummaryResponse>(json);

            var video = videoSummary.Videos.FirstOrDefault(v => v.Id == videoId);
            if (video != null)
            {
                return video.ExternalId;
            }

            return string.Empty;
        }

        /// <inheritdoc />
        public async Task<bool> DeleteVideoAsync(string videoId)
        {
        // Build Azure Video Indexer resource provider client that has access token through ARM
            var videoIndexerHelper = await AzureVideoIndexerHelper.GetAzureVideoIndexerHelperAsync(this.indexerSettings).ConfigureAwait(false);

            // Get account details
            var account = await videoIndexerHelper.GetAccountAsync(this.httpClient, this.logger).ConfigureAwait(false);
            var accountLocation = account.Location;
            var accountId = account.Properties.Id;

            // Get account level access token for Azure Video Indexer.
            var accountAccessToken = await videoIndexerHelper.GetAccessTokenAsync(this.httpClient, ArmAccessTokenPermission.Contributor, ArmAccessTokenScope.Account, this.logger).ConfigureAwait(false);

            var url = $"{ApiUrl}/{accountLocation}/Accounts/{accountId}/Videos/{videoId}";

            this.httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", accountAccessToken);
            var deleteVideoResult = await this.httpClient.DeleteAsync(url).ConfigureAwait(false);

            if (deleteVideoResult.StatusCode != System.Net.HttpStatusCode.NoContent)
            {
                var message = await deleteVideoResult.Content.ReadAsStringAsync().ConfigureAwait(false);
                this.logger.LogError("AzureVideoIndexerService - Could not fully delete video. Error: {Message}", message);
                return false;
            }

            return true;
        }

        /// <inheritdoc />
        public async Task<string> GetVttCaptionsAsync(string videoId)
        {
            var videoIndexerHelper = await AzureVideoIndexerHelper.GetAzureVideoIndexerHelperAsync(this.indexerSettings).ConfigureAwait(false);

            // Get account details
            var account = await videoIndexerHelper.GetAccountAsync(this.httpClient, this.logger).ConfigureAwait(false);
            var accountLocation = account.Location;
            var accountId = account.Properties.Id;

            // Get account level access token for Azure Video Indexer.
            var accountAccessToken = await videoIndexerHelper.GetAccessTokenAsync(this.httpClient, ArmAccessTokenPermission.Contributor, ArmAccessTokenScope.Account, this.logger).ConfigureAwait(false);

            return await this.GetSubtitleFormatAsync(videoId, accountAccessToken, accountLocation, accountId).ConfigureAwait(false);
        }

        protected async Task SavePayloadToCacheAsync(string key, string json, string contextPath, string resourceId)
        {
            var repository = this.objectRepositoryFactory.Resolve<AssetCacheItem>();
            var encodedPayload = System.Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes(json));

            var cacheRequest = new AssetCacheItem()
            {
                Id = key,
                CreatedOn = DateTime.UtcNow,
                TriggerAsset = contextPath,
                Payload = encodedPayload,
                ResourceId = resourceId,
            };

            
            await repository.CreateItemAsync(cacheRequest).ConfigureAwait(false);
        }

        /// <summary>
        /// Gets a sas url for a given file.
        /// </summary>
        /// <param name="containerName">The container name.</param>
        /// <param name="resourcePath">The resource path.</param>
        /// <returns>A Uri to the blob.</returns>
        private Uri GetSasUrl(string containerName, string resourcePath)
        {
            BlobClient blobClient = new BlobClient(this.settings.BlobConnectionString, containerName, resourcePath);

            BlobSasBuilder sasBuilder = new BlobSasBuilder()
            {
                BlobContainerName = containerName,
                BlobName = resourcePath,
                Resource = "b",
            };

            sasBuilder.ExpiresOn = DateTimeOffset.UtcNow.AddDays(1);
            sasBuilder.SetPermissions(BlobSasPermissions.Read);

            Uri uri = blobClient.GenerateSasUri(sasBuilder);

            this.logger.LogInformation("SAS URI for blob is Generated");
            return uri;
        }

        /// <summary>
        /// Uploads the video from a station to the Video Indexer.
        /// </summary>
        /// <param name="videoName">Video Name.</param>
        /// <param name="correlationKey">Correlation Key.</param>
        /// <param name="videoUri">Video Uri.</param>
        /// <param name="accountLocation">Account Location.</param>
        /// <param name="accountId">Account id.</param>
        /// <param name="accountAccessToken">Account Access Token.</param>
        /// <returns>The video id.</returns>
        private async Task<string> UploadVideoAsync(string videoName, string correlationKey, Uri videoUri, string accountLocation, string accountId, string accountAccessToken)
        {
            this.logger.LogInformation("Video is starting to upload with video name: {VideoName}, videoUri: {VideoUri}", videoName, videoUri);

            using var content = new MultipartFormDataContent();

            var name = Path.GetFileName(videoName);
            if (name.Length > 75)
            {
                name = name.Substring(0, 75);
            }

            try
            {
                var paramDict = new Dictionary<string, string>()
                {
                    { "indexingPreset", "BasicAudio" },
                    { "externalId", correlationKey },
                    { "name", name },
                    { "privacy", "Private" },
                    { "videoUrl", videoUri.ToString() },
                };

                var queryParams = this.CreateQueryString(paramDict);

                this.httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", accountAccessToken);

                var uploadRequestResult = await this.httpClient.PostAsync($"{ApiUrl}/{accountLocation}/Accounts/{accountId}/Videos?{queryParams}", content).ConfigureAwait(false);

                if (uploadRequestResult.StatusCode != System.Net.HttpStatusCode.OK)
                {
                    var message = await uploadRequestResult.Content.ReadAsStringAsync().ConfigureAwait(false);
                    this.logger.LogError("AzureVideoIndexerService - Could not start upload. Error: {Message}", message);
                    throw new InvalidOperationException("Could not upload video!");
                }

                var uploadResult = await uploadRequestResult.Content.ReadAsStringAsync().ConfigureAwait(false);

                // Get the video ID from the upload result
                var videoId = System.Text.Json.JsonSerializer.Deserialize<Video>(uploadResult).Id;
                this.logger.LogInformation("Video ID {VideoId} was uploaded successfully", videoId);
                return videoId;
            }
            catch (Exception ex)
            {
                this.logger.LogInformation("AzureVideoIndexerService - Exception: {Exception}", ex.ToString());
                throw;
            }
        }

        /// <summary>
        /// Gets a subtitle response from the indexer.
        /// </summary>
        /// <param name="videoId">The video id.</param>
        /// <param name="accountAccessToken">The account access token.</param>
        /// <param name="accountLocation">The account location.</param>
        /// <param name="accountId">The account id.</param>
        /// <returns>The subtitles.</returns>
        private async Task<string> GetSubtitleFormatAsync(string videoId, string accountAccessToken, string accountLocation, string accountId)
        {
            string queryParams = this.CreateQueryString(
                new Dictionary<string, string>()
                {
                    { "format", "Vtt" },
                    { "language", "English" },
                });

            this.httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", accountAccessToken);
            var videoCaptionsRequestResult = await this.httpClient.GetAsync($"{ApiUrl}/{accountLocation}/Accounts/{accountId}/Videos/{videoId}/Captions?{queryParams}").ConfigureAwait(false);

            if (videoCaptionsRequestResult.StatusCode != System.Net.HttpStatusCode.OK)
            {
                var message = await videoCaptionsRequestResult.Content.ReadAsStringAsync().ConfigureAwait(false);
                this.logger.LogError("AzureVideoIndexerService - Could not get captions. Error: {Message}", message);
                throw new InvalidOperationException("Could not get captions!");
            }

            var videoCaptionsResult = await videoCaptionsRequestResult.Content.ReadAsStringAsync().ConfigureAwait(false);

            this.logger.LogInformation("Captions of the video for video ID {VideoId} Received", videoId);

            return videoCaptionsResult;
        }

        /// <summary>
        /// Created a Query String.
        /// </summary>
        /// <param name="parameters">The parameters.</param>
        /// <returns>The string.</returns>
        private string CreateQueryString(IDictionary<string, string> parameters)
        {
            var queryParameters = HttpUtility.ParseQueryString(string.Empty);
            foreach (var parameter in parameters)
            {
                queryParameters[parameter.Key] = parameter.Value;
            }

            return queryParameters.ToString();
        }

        /// <summary>
        /// Maps the Indexer state to our domain state.
        /// </summary>
        /// <param name="state">The indexer state.</param>
        /// <returns>The domain state.</returns>
        private IndexerEntryState MapState(ProcessingState state)
        {
            return state switch
            {
                ProcessingState.Uploaded => IndexerEntryState.Processing,
                ProcessingState.Failed => IndexerEntryState.Error,
                ProcessingState.Processing => IndexerEntryState.Processing,
                ProcessingState.Processed => IndexerEntryState.Completed,
                _ => IndexerEntryState.Unknown
            };
        }
    }
}