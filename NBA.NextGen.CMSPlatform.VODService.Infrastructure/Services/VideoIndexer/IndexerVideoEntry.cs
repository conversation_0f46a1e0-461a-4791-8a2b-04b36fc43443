// "//-----------------------------------------------------------------------".
// <copyright file="IndexerVideoEntry.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Infrastructure.Services.VideoIndexer
{
    using System.Text.Json.Serialization;

    /// <summary>
    /// IndexerVideoEntry.
    /// </summary>
    public class IndexerVideoEntry
    {
        /// <summary>
        /// Gets or sets the id.
        /// </summary>
        [JsonPropertyName("id")]
        public string Id { get; set; }

        /// <summary>
        /// Gets or sets the state.
        /// </summary>
        [JsonPropertyName("state")]
        public ProcessingState State { get; set; }

        /// <summary>
        /// Gets or sets the external id.
        /// </summary>
        [JsonPropertyName("externalId")]
        public string ExternalId { get; set; }

        /// <summary>
        /// Gets or sets the name.
        /// </summary>
        [JsonPropertyName("name")]
        public string Name { get; set; }
    }
}