// "//-----------------------------------------------------------------------".
// <copyright file="ListIndexerVideosPageDetails.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Infrastructure.Services.VideoIndexer
{
    using System.Text.Json.Serialization;

    /// <summary>
    /// ListIndexerVideosPageDetails.
    /// </summary>
    public class ListIndexerVideosPageDetails
    {
        /// <summary>
        /// Gets or sets the page size.
        /// </summary>
        [JsonPropertyName("pageSize")]
        public int PageSize { get; set; }

        /// <summary>
        /// Gets or sets the skip count.
        /// </summary>
        [JsonPropertyName("skip")]
        public int Skip { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the list is done.
        /// </summary>
        [JsonPropertyName("done")]
        public bool Done { get; set; }
    }
}