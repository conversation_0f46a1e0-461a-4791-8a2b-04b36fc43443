// "//-----------------------------------------------------------------------".
// <copyright file="Account.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Infrastructure.Services.VideoIndexer
{
    using System.Text.Json.Serialization;

    /// <summary>
    /// Account.
    /// </summary>
    public class Account
    {
        /// <summary>
        /// Gets or sets the properties.
        /// </summary>
        [JsonPropertyName("properties")]
        public AccountProperties Properties { get; set; }

        /// <summary>
        /// Gets or sets the location.
        /// </summary>
        [JsonPropertyName("location")]
        public string Location { get; set; }
    }
}