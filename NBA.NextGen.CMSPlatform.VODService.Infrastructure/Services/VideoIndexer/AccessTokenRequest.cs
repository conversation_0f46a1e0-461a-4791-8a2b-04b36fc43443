// "//-----------------------------------------------------------------------".
// <copyright file="AccessTokenRequest.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Infrastructure.Services.VideoIndexer
{
    using System.Text.Json.Serialization;

    /// <summary>
    /// AccessTokenRequest.
    /// </summary>
    public class AccessTokenRequest
    {
        /// <summary>
        /// Gets or sets the PermissionType.
        /// </summary>
        [JsonPropertyName("permissionType")]
        public ArmAccessTokenPermission PermissionType { get; set; }

        /// <summary>
        /// Gets or sets the Scope.
        /// </summary>
        [JsonPropertyName("scope")]
        public ArmAccessTokenScope Scope { get; set; }
    }
}