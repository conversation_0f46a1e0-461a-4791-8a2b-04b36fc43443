// "//-----------------------------------------------------------------------".
// <copyright file="GenerateAccessTokenResponse.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Infrastructure.Services.VideoIndexer
{
    using System.Text.Json.Serialization;

    /// <summary>
    /// GenerateAccessTokenResponse.
    /// </summary>
    public class GenerateAccessTokenResponse
    {
        /// <summary>
        /// Gets or sets the access token.
        /// </summary>
        [J<PERSON><PERSON>ropertyName("accessToken")]
        public string AccessToken { get; set; }
    }
}