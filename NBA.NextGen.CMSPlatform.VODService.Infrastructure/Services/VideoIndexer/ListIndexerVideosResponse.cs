// "//-----------------------------------------------------------------------".
// <copyright file="ListIndexerVideosResponse.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Infrastructure.Services.VideoIndexer
{
    using System.Collections.Generic;
    using System.Text.Json.Serialization;

    /// <summary>
    /// ListIndexerVideosResponse.
    /// </summary>
    public class ListIndexerVideosResponse
    {
        /// <summary>
        /// Gets or sets the results.
        /// </summary>
        [JsonPropertyName("results")]
#pragma warning disable CA2227
        public List<IndexerVideoEntry> Results { get; set; }
#pragma warning restore CA2227

        /// <summary>
        /// Gets or sets the next page info.
        /// </summary>
        [JsonPropertyName("nextPage")]
        public ListIndexerVideosPageDetails NextPage { get; set; }
    }
}