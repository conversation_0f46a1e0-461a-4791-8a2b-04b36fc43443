// "//-----------------------------------------------------------------------".
// <copyright file="AccountProperties.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Infrastructure.Services.VideoIndexer
{
    using System.Text.Json.Serialization;

    /// <summary>
    /// AccountProperties.
    /// </summary>
    public class AccountProperties
    {
        /// <summary>
        /// Gets or sets the id.
        /// </summary>
        [JsonPropertyName("accountId")]
        public string Id { get; set; }
    }
}