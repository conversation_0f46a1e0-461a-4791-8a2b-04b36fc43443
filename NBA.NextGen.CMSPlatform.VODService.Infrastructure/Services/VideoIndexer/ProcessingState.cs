// "//-----------------------------------------------------------------------".
// <copyright file="ProcessingState.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Infrastructure.Services.VideoIndexer
{
    using System.Text.Json.Serialization;

    /// <summary>
    /// ProcessingState.
    /// </summary>
    [JsonConverter(typeof(JsonStringEnumConverter))]
    public enum ProcessingState
    {
        /// <summary>
        /// Uploaded.
        /// </summary>
        Uploaded,

        /// <summary>
        /// Processing.
        /// </summary>
        Processing,

        /// <summary>
        /// Processed.
        /// </summary>
        Processed,

        /// <summary>
        /// Failed.
        /// </summary>
        Failed,
    }
}