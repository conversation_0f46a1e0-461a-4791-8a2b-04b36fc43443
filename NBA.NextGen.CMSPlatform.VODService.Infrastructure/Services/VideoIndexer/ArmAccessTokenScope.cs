// "//-----------------------------------------------------------------------".
// <copyright file="ArmAccessTokenScope.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Infrastructure.Services.VideoIndexer
{
    using System.Text.Json.Serialization;

    /// <summary>
    /// ArmAccessTokenScope.
    /// </summary>
    [JsonConverter(typeof(JsonStringEnumConverter))]
    public enum ArmAccessTokenScope
    {
        /// <summary>
        /// Account.
        /// </summary>
        Account,

        /// <summary>
        /// Project.
        /// </summary>
        Project,

        /// <summary>
        /// Video.
        /// </summary>
        Video,
    }
}