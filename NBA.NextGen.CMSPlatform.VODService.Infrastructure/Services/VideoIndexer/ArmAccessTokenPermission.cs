// "//-----------------------------------------------------------------------".
// <copyright file="ArmAccessTokenPermission.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Infrastructure.Services.VideoIndexer
{
    using System.Text.Json.Serialization;

    /// <summary>
    /// ArmAccessTokenPermission.
    /// </summary>
    [JsonConverter(typeof(JsonStringEnumConverter))]
#pragma warning disable CA1711
    public enum ArmAccessTokenPermission
#pragma warning restore CA1711
    {
        /// <summary>
        /// Reader.
        /// </summary>
        Reader,

        /// <summary>
        /// Contributor.
        /// </summary>
        Contributor,

        /// <summary>
        /// MyAccessAdministrator.
        /// </summary>
        MyAccessAdministrator,

        /// <summary>
        /// Owner.
        /// </summary>
        Owner,
    }
}