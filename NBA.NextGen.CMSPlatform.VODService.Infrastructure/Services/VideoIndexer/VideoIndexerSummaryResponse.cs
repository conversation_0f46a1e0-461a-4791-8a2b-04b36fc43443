// "//-----------------------------------------------------------------------".
// <copyright file="VideoIndexerSummaryResponse.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Infrastructure.Services.VideoIndexer
{
    using System.Collections.Generic;
    using System.Text.Json.Serialization;

    /// <summary>
    /// VideoIndexerSummaryResponse.
    /// </summary>
    public class VideoIndexerSummaryResponse
    {
        /// <summary>
        /// Gets or sets the account id.
        /// </summary>
        [JsonPropertyName("accountId")]
        public string AccountId { get; set; }

        /// <summary>
        /// Gets or sets the id.
        /// </summary>
        [JsonPropertyName("id")]
        public string Id { get; set; }

        /// <summary>
        /// Gets or sets the name.
        /// </summary>
        [JsonPropertyName("name")]
        public string Name { get; set; }

        /// <summary>
        /// Gets or sets the collection of videos.
        /// </summary>
#pragma warning disable CA2227
        [JsonPropertyName("videos")]
        public List<VideoIndexerDetailedResponse> Videos { get; set; }
#pragma warning restore CA2227
    }
}