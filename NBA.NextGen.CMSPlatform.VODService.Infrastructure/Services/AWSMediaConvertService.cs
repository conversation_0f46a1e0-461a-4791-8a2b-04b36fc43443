using System.Collections.Generic;
using System;
using System.Threading.Tasks;
using Amazon.MediaConvert;
using Amazon.MediaConvert.Model;
using Microsoft.Extensions.Configuration;
using NBA.NextGen.CMSPlatform.VODService.Application.Services;
using Microsoft.Extensions.Logging;

namespace NBA.NextGen.CMSPlatform.VODService.Infrastructure.Services
{
    public class AWSMediaConvertService : IAWSMediaConvertService
    {
        private readonly string _roleName;
        private readonly IAmazonMediaConvert _mediaConvert;
        private readonly ILogger _logger;

        public AWSMediaConvertService(IAmazonMediaConvert mediaConvert, IConfiguration configuration, ILogger<AWSMediaConvertService> logger)
        {
            _roleName = configuration.GetSection("TranscriptionEngine")["MediaConvertRole"] ?? throw new NullReferenceException("Could not find MediaConvert RoleName");
            _mediaConvert = mediaConvert;
            _logger = logger;
        }

        public async Task<string> StripAudioTrackAsync(string fileInput, string fileOutput)
        {
            _logger.LogInformation($"Creating Audio Track Job! Input: {fileInput} Output: {fileOutput}");

            CreateJobRequest createJobRequest = new CreateJobRequest
            {
                Role = _roleName
            };

            JobSettings jobSettings = new JobSettings
            {
                AdAvailOffset = 0,
                TimecodeConfig = new TimecodeConfig
                {
                    Source = TimecodeSource.EMBEDDED
                }
            };
            createJobRequest.Settings = jobSettings;

            #region OutputGroup

            OutputGroup ofg = new OutputGroup
            {
                Name = "File Group",
                OutputGroupSettings = new OutputGroupSettings
                {
                    Type = OutputGroupType.FILE_GROUP_SETTINGS,
                    FileGroupSettings = new FileGroupSettings
                    {
                        Destination = fileOutput
                    }
                }
            };

            Output output = new Output();

            #region AudioDescription

            AudioDescription ades = new AudioDescription
            {
                LanguageCodeControl = AudioLanguageCodeControl.FOLLOW_INPUT,
                // This name matches one specified in the following Inputs.
                AudioSourceName = "Audio Selector 1",
                CodecSettings = new AudioCodecSettings
                {
                    Codec = AudioCodec.AAC
                }
            };

            AacSettings aac = new AacSettings
            {
                AudioDescriptionBroadcasterMix = AacAudioDescriptionBroadcasterMix.NORMAL,
                RateControlMode = AacRateControlMode.CBR,
                CodecProfile = AacCodecProfile.LC,
                CodingMode = AacCodingMode.CODING_MODE_2_0,
                RawFormat = AacRawFormat.NONE,
                SampleRate = 48000,
                Specification = AacSpecification.MPEG4,
                Bitrate = 64000
            };
            ades.CodecSettings.AacSettings = aac;
            output.AudioDescriptions = [ades];

            #endregion AudioDescription

            #region Mp4 Container

            output.ContainerSettings = new ContainerSettings
            {
                Container = ContainerType.MP4
            };
            Mp4Settings mp4 = new Mp4Settings
            {
                CslgAtom = Mp4CslgAtom.INCLUDE,
                FreeSpaceBox = Mp4FreeSpaceBox.EXCLUDE,
                MoovPlacement = Mp4MoovPlacement.PROGRESSIVE_DOWNLOAD
            };
            output.ContainerSettings.Mp4Settings = mp4;

            #endregion Mp4 Container

            ofg.Outputs = [output];
            createJobRequest.Settings.OutputGroups = [ofg];

            #endregion OutputGroup

            #region Input

            Input input = new Input
            {
                FilterEnable = InputFilterEnable.AUTO,
                PsiControl = InputPsiControl.USE_PSI,
                FilterStrength = 0,
                DeblockFilter = InputDeblockFilter.DISABLED,
                DenoiseFilter = InputDenoiseFilter.DISABLED,
                TimecodeSource = InputTimecodeSource.EMBEDDED,
                FileInput = fileInput
            };

            AudioSelector audsel = new AudioSelector
            {
                Offset = 0,
                DefaultSelection = AudioDefaultSelection.NOT_DEFAULT,
                ProgramSelection = 1,
                SelectorType = AudioSelectorType.TRACK
            };
            audsel.Tracks = [1];
            input.AudioSelectors = new Dictionary<string, AudioSelector>();
            input.AudioSelectors.Add("Audio Selector 1", audsel);

            input.VideoSelector = new VideoSelector
            {
                ColorSpace = ColorSpace.FOLLOW
            };

            createJobRequest.Settings.Inputs = [input];

            #endregion Input

            var createJobResponse = await _mediaConvert.CreateJobAsync(createJobRequest);

            return createJobResponse.Job.Id;
        }

        public async Task<Job> GetJobByIdAsync(string jobId)
        {
            var request = new GetJobRequest
            {
                Id = jobId
            };

            var response = await _mediaConvert.GetJobAsync(request);
            return response.Job;
        }
    }
}