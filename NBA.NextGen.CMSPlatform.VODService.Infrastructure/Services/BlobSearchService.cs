// "//-----------------------------------------------------------------------".
// <copyright file="BlobSearchService.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Infrastructure.Services
{
    using System;
    using System.Collections.Generic;
    using System.Text;
    using System.Threading.Tasks;
    using Azure.Core;
    using Azure.Identity;
    using Azure.Storage.Blobs;
    using Azure.Storage.Blobs.Models;
    using Microsoft.Azure.Storage;
    using Microsoft.Azure.Storage.Blob;
    using NBA.NextGen.CMSPlatform.VODService.Application.Services;

    /// <summary>
    /// BlobSearchService.
    /// </summary>
    public class BlobSearchService : IBlobSearchService
    {
        /// <summary>
        /// Searches for blobs in a folder.
        /// </summary>
        /// <param name="connectionString">The blob connection String.</param>
        /// <param name="containerName">The container name.</param>
        /// <param name="folderName">The name of the folder.</param>
        /// <param name="from">The starting time.</param>
        /// <param name="to">The ending time.</param>
        /// <returns>The list of blobs found.</returns>
        public async Task<List<ICloudBlob>> SearchByFolderAndDateTimeRangeAsync(string connectionString, string containerName, string folderName, DateTime from, DateTime to)
        {
            var list = new List<ICloudBlob>();

            var blobClient = this.GetCloudBlobClient(connectionString);
            CloudBlobContainer container = blobClient.GetContainerReference(containerName);

            BlobContinuationToken blobContinuationToken = null;

            try
            {
                do
                {
                    var resultSegment = await container.ListBlobsSegmentedAsync(folderName, true, BlobListingDetails.None, null, blobContinuationToken, null, null).ConfigureAwait(false);

                    blobContinuationToken = resultSegment.ContinuationToken;

                    foreach (var blob in resultSegment.Results)
                    {
                        var icb = blob as ICloudBlob;
                        if (icb != null)
                        {
                            var dt = icb.Properties.Created?.DateTime;
                            if (dt != null && dt >= from && dt <= to)
                            {
                                list.Add(icb);
                            }
                        }
                    }
                }
                while (blobContinuationToken != null);
            }
            catch (Exception e)
            {
                Console.WriteLine(e.Message);
                throw;
            }

            return list;
        }

        /// <summary>
        /// Creates a new Blob Client.
        /// </summary>
        /// <param name="connectionString">The connection string.</param>
        /// <returns>The blob client.</returns>
        private CloudBlobClient GetCloudBlobClient(string connectionString)
        {
            CloudStorageAccount storageAccount = CloudStorageAccount.Parse(connectionString);
            return storageAccount.CreateCloudBlobClient();
        }
    }
}
