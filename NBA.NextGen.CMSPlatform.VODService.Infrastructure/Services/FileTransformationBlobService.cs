// "//-----------------------------------------------------------------------".
// <copyright file="FileTransformationBlobService.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.CMSPlatform.VODService.Infrastructure.Services
{
    using System;
    using System.Collections.Generic;
    using System.Diagnostics.CodeAnalysis;
    using System.IO;
    using System.Linq;
    using System.Net.Http;
    using System.Text;
    using System.Threading.Tasks;
    using AutoMapper;
    using Microsoft.Extensions.Configuration;
    using Microsoft.Extensions.Logging;
    using Microsoft.Extensions.Options;
    using MST.Common.Data;
    using NBA.NextGen.CMSPlatform.VODService.Application.Dispatcher.ContentHandler;
    using NBA.NextGen.CMSPlatform.VODService.Application.Services;
    using NBA.NextGen.CMSPlatform.VODService.Domain.Config;
    using NBA.NextGen.CMSPlatform.VODService.Domain.Utilities;
    using NBA.NextGen.CMSPlatform.VODService.Domain.Vcms;

    using Newtonsoft.Json;
    using Newtonsoft.Json.Linq;

    /// <summary>
    /// The service for handling BLOBs.
    /// </summary>
    public class FileTransformationBlobService : IFileTransformationBlobService
    {
        private readonly FileTransformationSettings blobSettings;
        private readonly VcmsStorageSettings vcmsStorageSettings;
        private readonly IDataStoreFactory blobClientProvider;
        private readonly HttpClient httpClient;
        private readonly IMapper mapper;
        private IDataStore blobClient;
        private IDataStore requestStorageClient;
        private IVodAssetRegistryService assetRegistryService;
        private readonly IConfiguration configuration;

        public FileTransformationBlobService(
            [NotNull] IOptions<FileTransformationSettings> blobSettings,
            IDataStoreFactory blobClientProvider,
            IMapper mapper,
            [NotNull] IOptions<VcmsStorageSettings> vcmsStorageSettings,
            IVodAssetRegistryService assetRegistryService,
            HttpClient httpClient,
            IConfiguration configuration)
        {
            this.blobSettings = blobSettings.Value;
            this.blobClientProvider = blobClientProvider;
            this.mapper = mapper;
            this.vcmsStorageSettings = vcmsStorageSettings.Value;
            this.httpClient = httpClient;
            this.assetRegistryService = assetRegistryService;
            this.configuration = configuration;
            this.InitializeContainer();
        }

        /// <summary>
        /// Checks if json file exists.
        /// </summary>
        /// <param name="fileName">Name of the file.</param>
        /// <returns>boolean value.</returns>
        public async Task<bool> CheckIfJSONExistsAsync([NotNull] string fileName)
        {
            return await this.blobClient.ExistsAsync(fileName + ".json").ConfigureAwait(false);
        }

        /// <summary>
        /// Checks if xml file exists.
        /// </summary>
        /// <param name="fileName">Name of the file.</param>
        /// <returns>
        /// bool.
        /// </returns>
        public async Task<bool> CheckIfXMLExistsAsync([NotNull] string fileName)
        {
            return await this.blobClient.ExistsAsync(fileName + ".xml").ConfigureAwait(false);
        }

        /// <summary>
        /// Gets the name of the file.
        /// </summary>
        /// <param name="fileName">Name of the file.</param>
        /// <param name="logger">logger.</param>
        /// <returns>name.</returns>
        public string GetFileName([NotNull] string fileName, ILogger logger)
        {
            string name;
            fileName = string.Join("/", fileName.Split("/", StringSplitOptions.None).Skip(1));
            string[] arrFileName = fileName.Split(".");
            name = arrFileName[0];

            logger.LogInformation($"Filename is : {name} ");
            return name;
        }

        /// <summary>
        /// Reads the Json.
        /// </summary>
        /// <param name="fileName">Name of the file.</param>
        /// <param name="logger">logger.</param>
        /// <returns>ADI.</returns>
        public async Task<ADI> ReadJsonAsync([NotNull] string fileName, ILogger logger)
        {
            string jsondata = await this.blobClient.GetItemAsync(fileName + ".json").ConfigureAwait(false);

            logger.LogInformation($"Read Contents of File {fileName}");

            JObject wscJsonData = JObject.Parse(jsondata);
            string broadcastType = wscJsonData["broadcastType"]?.ToString();
            logger.LogInformation("Media Name: " + (wscJsonData["mediaName"] ?? "<null>"));
            logger.LogInformation("Broadcast Type: " + (broadcastType ?? "<null>"));
            if (wscJsonData["mediaName"] != null || (broadcastType != null && broadcastType.StartsWith("NSS-", StringComparison.OrdinalIgnoreCase)))
            {
                if (fileName.Contains("/", System.StringComparison.Ordinal))
                {
                    fileName = fileName.Substring(fileName.LastIndexOf("/", System.StringComparison.OrdinalIgnoreCase) + 1);
                }

                string mediaName = wscJsonData["mediaName"]?.ToString();
                string mediaId = wscJsonData["mediaId"]?.ToString();
                string teamContext = wscJsonData["role"]?.ToString();
                JToken homeTeam = wscJsonData["homeTeam"];
                JToken awayTeam = wscJsonData["awayTeam"];
                string homeTeamName = homeTeam["teamName"].ToObject<string>();
                string awayTeamName = awayTeam["teamName"].ToObject<string>();
                if (mediaName == null)
                {
                    mediaName = broadcastType;
                    if (broadcastType != null && broadcastType.StartsWith("NSS-", StringComparison.OrdinalIgnoreCase))
                    {
                        wscJsonData["broadcastType"] = broadcastType;
                    }
                }

                logger.LogInformation($"get the streaming media {mediaName}");
                if (mediaName == "Mobile-view")
                {
                    wscJsonData["mediaName"] = "Mobile View";
                }
                else
                {
                    Dictionary<string, object> mediaObject = await MessageHelper.GetMediaObjectAsync(logger, mediaId, teamContext, homeTeamName, awayTeamName, mediaName, this.blobSettings, this.assetRegistryService, this.httpClient, this.configuration).ConfigureAwait(false);
                    wscJsonData["mediaId"] = mediaObject["mediaId"].ToString();
                    wscJsonData["mediaName"] = mediaObject["mediaName"].ToString();
                    string gmsMediaName = mediaObject["mediaUnique"].ToString();
                    string languageName = mediaObject["languageName"]?.ToString();
                    logger.LogInformation($"languageName : {languageName}");
                    if (languageName.StartsWith("Spanish", StringComparison.OrdinalIgnoreCase))
                    {
                        languageName = "Spanish";
                    }

                    if (languageName.StartsWith("Portuguese", StringComparison.OrdinalIgnoreCase))
                    {
                        languageName = "Portuguese";
                    }

                    string videoTypeInMetaFile = wscJsonData["videoType"]?.ToString();
                    if (videoTypeInMetaFile == null)
                    {
                        string videoCategory = "Archives";
                        string videoType = "Games";
                        string videoFranchise = "In-Language Broadcast";
                        string videoFranchiseName = languageName + " Broadcast";
                        Dictionary<string, string> broadcasterMapping = this.BuildMetadataMapping();
                        if (broadcasterMapping.ContainsKey(gmsMediaName))
                        {
                            string mappingValue = broadcasterMapping[gmsMediaName];
                            string[] spllitValues = mappingValue.Split(",");
                            videoFranchise = spllitValues[0];
                            if (spllitValues.Length == 2)
                            {
                                videoFranchiseName = spllitValues[1];
                            }
                        }

                        wscJsonData["videoType"] = videoType;
                        wscJsonData["videoCategory"] = videoCategory;
                        wscJsonData["videoFranchise"] = videoFranchise;
                        wscJsonData["videoFranchiseName"] = videoFranchiseName;
                    }
                }

                wscJsonData["fileName"] = fileName;
            }

            logger.LogInformation($"Read Contents of File  : {wscJsonData}");
            ADI adi = this.mapper.Map<ADI>(wscJsonData);
            return adi;
        }

        /// <summary>
        /// Reads the XML.
        /// </summary>
        /// <typeparam name="T">Type of object.</typeparam>
        /// <param name="fileName">Name of the file.</param>
        /// <param name="logger">logger.</param>
        /// <returns>Dedired type of Xml object.</returns>
        public async Task<T> ReadXmlAsync<T>([NotNull] string fileName, ILogger logger)
        {
            string xmldata = await this.blobClient.GetItemAsync(fileName + ".xml").ConfigureAwait(false);

            logger.LogInformation($"Read Contents of File {fileName}.xml : {xmldata}");

            return XmlHelper.DeserializeXML<T>(xmldata);
        }

        /// <summary>
        /// Reads the XML.
        /// </summary>
        /// <typeparam name="T">Type of object.</typeparam>
        /// <param name="fileName">Name of the file.</param>
        /// <param name="logger">logger.</param>
        /// <returns>Dedired type of Xml object.</returns>
        public async Task<string> ReadResourceAsStringAsync<T>([NotNull] string fileName, ILogger logger)
        {
            string xmldata = await this.blobClient.GetItemAsync(fileName + ".xml").ConfigureAwait(false);

            logger.LogInformation($"Read Contents of File {fileName}.xml : {xmldata}");

            return xmldata;
        }

        /// <summary>
        /// Writes the BLOB.
        /// </summary>
        /// <param name="fileName">Name of the file.</param>
        /// <param name="content">Content of the BLOb.</param>
        /// <param name="logger">The logger.</param>
        /// <returns>A <see cref="Task"/> representing the result of the asynchronous operation.</returns>
        public async Task WriteBlobAsync(string fileName, ADI content, ILogger logger)
        {
            if (this.vcmsStorageSettings?.WriteToBlobStorage ?? false)
            {
                string contentString = XmlHelper.SerializeXML(content, Encoding.UTF8);
                await this.requestStorageClient.CreateItemAsync(fileName, contentString).ConfigureAwait(false);
                logger.LogInformation($"Uploaded {fileName} to {this.vcmsStorageSettings.ContainerName} container");
            }
        }

        /// <summary>
        /// Checks if BLOB exists.
        /// </summary>
        /// <param name="fileName">Name of the file.</param>
        /// <returns>A <see cref="Task{Boolean}"/> representing the result of the asynchronous operation.</returns>
        public async Task<bool> BlobExistsAsync(string fileName)
        {
            return await this.blobClient.ExistsAsync(fileName).ConfigureAwait(false);
        }

        /// <summary>
        /// Gets BLOB location.
        /// </summary>
        /// <param name="fileName">Name of the file.</param>
        /// <returns>string.</returns>
        public string GetBlobLocation(string fileName)
        {
            if (fileName.StartsWith("/"))
            {
                fileName = fileName.Substring(1);
            }

            bool.TryParse(this.configuration.GetSection("AssetDelivery")["UseAzure"], out var useAzure);
            if (useAzure)
            {
                var translatedContainer = this.configuration.GetSection("AssetDelivery").GetSection("BucketToContainer")[this.blobSettings.ContainerName];
                return "https://" + this.blobSettings.AccountName + ".blob.core.windows.net/" + translatedContainer + "/" + fileName;
            }
            else
            {
                var region = this.configuration.GetSection("AWS")["Region"];
                return $"https://{blobSettings.ContainerName}.s3.{region}.amazonaws.com/{fileName}";
            }
        }

        /// <summary>
        /// Initializes the Container for handler.
        /// </summary>
        private void InitializeContainer()
        {
            if (this.blobClient == null)
            {
                this.blobClient = this.blobClientProvider.Resolve(this.blobSettings.ContainerName);
            }

            this.requestStorageClient ??= this.blobClientProvider.Resolve(this.vcmsStorageSettings.ContainerName);
        }

        /// <summary>
        /// Build Metadata Mapping.
        /// </summary>
        /// <returns>string.</returns>
        private Dictionary<string, string> BuildMetadataMapping()
        {
            Dictionary<string, string> mapping = new Dictionary<string, string>();
            mapping["NSS-Influencer-NBA-HooperVision"] = "Influencer Broadcast,Kiswe Influencer Broadcast";
            mapping["NSS-ClipperVision-BallerVision"] = "Influencer Broadcast,";
            mapping["NSS-ClipperVisionLP-BallerVision"] = "Influencer Broadcast,";
            mapping["NSS-NBA-Strategy-Stream"] = "Influencer Broadcast,Kiswe Influencer Broadcast";

            mapping["NSS-NBA-BetStream"] = "Alternate Broadcast,NBABet Stream Broadcast";
            mapping["NSS-NBATV-Betstream"] = "Alternate Broadcast,NBABet Stream Broadcast";

            mapping["NSS-ClipperVision-ShotIQ"] = "Alternate Broadcast,";
            mapping["NSS-ClipperVisionLP-ShotIQ"] = "Alternate Broadcast,";
            mapping["NSS-ClipperVision-Mascot-Mode"] = "Alternate Broadcast,Mascot Mode Broadcast";
            mapping["NSS-ClipperVisionLP-MascotMode"] = "Alternate Broadcast,Mascot Mode Broadcast";

            mapping["NSS-ClipperVision-KTLA"] = "Domestic Broadcast,Local Home Broadcast";
            mapping["NSS-Portuguese-Vivo"] = "In-Language Broadcast,Vivo Game of the Week";

            mapping["NSS-NBA-HBCU-Stream"] = "Influencer Broadcast,NBA x HBCU Broadcast";
            mapping["NSS-Bill-Walton-Stream"] = "Influencer Broadcast,Throw It Down with Bill Walton Broadcast";
            mapping["NSS-Center-Court"] = "Domestic Broadcast,NBA TV Center Court Broadcast";
            mapping["NSS-Iso-Cam"] = "Alternate Broadcast,Player Iso Cam Broadcast";
            mapping["NSS-Inside-The-All-Star-Game"] = "Influencer Broadcast,Inside The All-Star Game Broadcast";
            mapping["NSS-NBA-Stephen-A-World"] = "Influencer Broadcast,Stephen A World Broadcast";
            mapping["NSS-Kiswe4-German"] = "In-Language Broadcast,German Broadcast";
            mapping["NSS-Bucketsquad-Stream"] = "Influencer Broadcast,Bucketsquad Stream";
            mapping["NSS-Influencer"] = "Influencer Broadcast";
            return mapping;
        }
    }
}
