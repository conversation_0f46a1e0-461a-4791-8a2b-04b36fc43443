using System;
using NBA.NextGen.CMSPlatform.VODService.Domain.Common.Enums;
using Newtonsoft.Json;

namespace NBA.NextGen.CMSPlatform.VODService.Infrastructure.Models;

public class AssetRegistryEntry
{
    [JsonProperty("id")]
    public string Id { get; set; }

    public string IngestionFlowName { get; set; }

    public string TriggerFileName { get; set; }

    public AssetStates AssetState { get; set; }

    public DateTime LastEventUtc { get; set; }

}
