namespace NBA.NextGen.CMSPlatform.VODService.Infrastructure.Models;

using System;
using Newtonsoft.Json;

public class FileRegistryEntry
{
    [JsonProperty("id")]
    public string Id { get; set; }

    public string FileName { get; set; }

    public string IngestionFlowName { get; set; }

    public string ParentFolderName { get; set; }

    public DateTime ArrivalTimeUtc { get; set; }

    public DateTime ProcessedTimeUtc { get; set; }

    public bool IsWorkflowTriggerFile { get; set; }

    public string AssetId { get; set; }

    public bool IsFailure { get; set; }

    public string ExceptionMessage { get; set; }

}