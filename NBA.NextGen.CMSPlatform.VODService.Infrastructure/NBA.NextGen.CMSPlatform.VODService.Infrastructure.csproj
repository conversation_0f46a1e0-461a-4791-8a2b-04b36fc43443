<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <DebugType>pdbonly</DebugType>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <GenerateAssemblyTitleAttribute>true</GenerateAssemblyTitleAttribute>
  </PropertyGroup>
  
  <ItemGroup>
    <PackageReference Include="AWSSDK.Extensions.NETCore.Setup" Version="4.0.0" />
    <PackageReference Include="AWSSDK.MediaConvert" Version="4.0.1" />
    <PackageReference Include="AWSSDK.S3" Version="4.0.0.2" />
    <PackageReference Include="AWSSDK.SSO" Version="4.0.0.2" />
    <PackageReference Include="AWSSDK.SSOOIDC" Version="4.0.0.2" />
    <PackageReference Include="AWSSDK.TranscribeService" Version="4.0.0.2" />
    <PackageReference Include="Azure.Data.AppConfiguration" Version="1.0.3" />
    <PackageReference Include="Azure.Identity" Version="1.12.0" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="8.0.1" />
    <PackageReference Include="Microsoft.Extensions.Configuration.AzureAppConfiguration" Version="4.4.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.AzureKeyVault" Version="3.1.16" />
    <PackageReference Include="System.Net.Http" Version="4.3.4" />
    <PackageReference Include="System.Text.RegularExpressions" Version="4.3.1" />
    <PackageReference Include="Microsoft.Extensions.Caching.Memory" Version="8.0.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\NBA.NextGen.CMSPlatform.VODService.Application\NBA.NextGen.CMSPlatform.VODService.Application.csproj" />
  </ItemGroup>
</Project>
